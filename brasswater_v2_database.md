### **Optimized Database Structure (n8n-Friendly & Feature-Rich)**

**Conventions:**

*   **`id`**: Primary Key (UUID), `DEFAULT gen_random_uuid()`
*   **`created_at`**: Timestamp with time zone, `DEFAULT NOW()`
*   **`updated_at`**: Timestamp with time zone, `DEFAULT NOW()`
*   **`_id`**: Suffix for Foreign Keys (UUID).
*   **`_type` / `_status`**: `VARCHAR` or `TEXT` columns used for controlled vocabularies.
    *   **N8n Handling for `_type` / `_status` columns:** It is **CRITICAL** that your n8n workflows are designed to:
        *   **Validate:** Check extracted values against an *external, single source of truth vocabulary* (e.g., a shared Google Sheet, Notion database, or a simple JSON file within n8n).
        *   **Normalize:** Convert variations (e.g., "Main Lease" vs. "Primary Lease") to your chosen standard term.
        *   **Error Handling:** Alert if an unknown value is encountered, preventing bad data from entering the DB.
*   **`_jsonb`**: JSONB data type for flexible, schema-on-read, or nested data. This is ideal for n8n's ability to directly ingest JSON outputs from LLM/extractor nodes.

---

#### **I. Core Management Entities (Users, Documents & Parties)**

**1. `users`** (Internal staff/app users)

*   `id` (PK, UUID)
*   `username` (VARCHAR(100), UNIQUE, NOT NULL)
*   `email` (VARCHAR(255), UNIQUE, NOT NULL)
*   `first_name` (VARCHAR(100))
*   `last_name` (VARCHAR(100))
*   `role` (VARCHAR(50)) - *e.g., 'Admin', 'Property Manager', 'Lease Specialist', 'Operations'.*
*   `is_active` (BOOLEAN, DEFAULT TRUE)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**2. `documents`** (Stores metadata and raw content of *any* document)

*   `id` (PK, UUID)
*   `file_name` (TEXT, NOT NULL)
*   `file_url` (TEXT, NOT NULL) - *Link to file storage (S3, etc.).*
*   `document_type` (VARCHAR(100), NOT NULL) - *e.g., 'Lease Agreement', 'Amendment', 'Inspection Report', 'Board Resolution', 'Invoice', 'Rental Application'. (N8n controlled vocabulary).*
*   `document_language` (VARCHAR(50), NOT NULL) - *e.g., 'English', 'Français'. (N8n controlled vocabulary).*
*   `document_date` (DATE) - *The date the physical document was issued/signed.*
*   `complete_content_text` (TEXT) - *Full text content (for search/LLM re-processing).*
*   `extracted_content_jsonb` (JSONB) - *Full raw JSON output from the initial N8n extraction specific to *this document*. Useful for debugging and future re-extraction.*
*   `uploaded_by_user_id` (FK to `users.id`, NULLABLE)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**3. `document_sections`** (Parsed sections of a document for structured content display/analysis)

*   `id` (PK, UUID)
*   `document_id` (FK to `documents.id`, ON DELETE CASCADE)
*   `section_title` (TEXT, NULLABLE)
*   `content` (TEXT)
*   `doc_order` (INTEGER)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**4. `parties`** (Master list of all individuals/entities involved in the system - **One source for all contact info**)

*   `id` (PK, UUID)
*   `legal_name` (VARCHAR(255), NOT NULL)
*   `entity_type` (VARCHAR(100), NOT NULL) - *e.g., 'Corporation', 'Individual', 'Government Entity', 'Limited Partnership'. (N8n controlled vocabulary).*
*   `primary_contact_name` (VARCHAR(255), NULLABLE)
*   `primary_contact_title` (VARCHAR(100), NULLABLE)
*   `primary_email` (VARCHAR(255), NULLABLE)
*   `primary_phone_number` (VARCHAR(50), NULLABLE)
*   `primary_address_street` (VARCHAR(255), NULLABLE)
*   `primary_address_city` (VARCHAR(100), NULLABLE)
*   `primary_address_province` (VARCHAR(50), NULLABLE)
*   `primary_address_postal_code` (VARCHAR(20), NULLABLE)
*   `additional_contact_info_jsonb` (JSONB) - *For fax, secondary contacts, other addresses (e.g., notice_address, billing_address, head_office if different from primary), and their types.*
*   `notes` (TEXT, NULLABLE)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

---

#### **II. Property & Space Management (Addresses Req #1: Log Properties & Spaces)**

**5. `properties`** (Main table for buildings/complexes)

*   `id` (PK, UUID)
*   `name` (VARCHAR(255), NOT NULL)
*   `address_street` (VARCHAR(255))
*   `address_city` (VARCHAR(100))
*   `address_province` (VARCHAR(50))
*   `address_postal_code` (VARCHAR(20))
*   `address_country` (VARCHAR(50), DEFAULT 'Canada')
*   `property_type` (VARCHAR(100), NOT NULL) - *e.g., 'Commercial Building', 'Shopping Center', 'Industrial Complex'. (N8n controlled vocabulary).*
*   `current_status` (VARCHAR(50), NOT NULL) - *e.g., 'Active', 'Under Acquisition', 'Redevelopment', 'Sold'. (N8n controlled vocabulary).*
*   `total_land_area_sqft` (NUMERIC)
*   `total_built_area_sqft` (NUMERIC)
*   `year_built` (INTEGER)
*   `number_of_floors` (INTEGER)
*   `total_parking_spaces` (INTEGER) - *Sum of all parking spaces on the property.*
*   `property_manager_party_id` (FK to `parties.id`, NULLABLE) - *The `parties` record that manages this property.*
*   `description` (TEXT)
*   `zoning_classification` (VARCHAR(100))
*   `legal_description` (TEXT) - *Full lot numbers, cadastre details from *any* relevant document.*
*   `amenities_jsonb` (JSONB) - *Structured JSON for amenities: `{"common_areas": ["gym", "pool"], "security_features": ["24/7", "cameras"]}`.*
*   `media_urls_jsonb` (JSONB) - *URLs to property photos/videos.*
*   `external_reference_id` (VARCHAR(100), UNIQUE, NULLABLE) - *Any external ID from legacy systems or public records.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**6. `property_units`** (Individual spaces/units within a property - **addresses Req #1**)

*   `id` (PK, UUID)
*   `property_id` (FK to `properties.id`, ON DELETE CASCADE)
*   `unit_number` (VARCHAR(50), NOT NULL) - *e.g., 'Suite 100 B-1', 'Space 21', 'Parking Spot A1'.*
*   `unit_type` (VARCHAR(50), NOT NULL) - *e.g., 'Office', 'Retail', 'Warehouse', 'EV Charging Spot', 'Storage Unit'. (N8n controlled vocabulary).*
*   `floor_number` (INTEGER, NULLABLE)
*   `rentable_area_sqft` (NUMERIC, NOT NULL) - *The actual measurable area of the unit.*
*   `rentable_area_sqm` (NUMERIC, NULLABLE)
*   `current_status` (VARCHAR(50), NOT NULL) - *e.g., 'Vacant', 'Occupied', 'Available', 'Under Renovation', 'Not Leasable', 'Reserved'. (N8n controlled vocabulary).* **This explicitly tracks "available (or not)".**
*   `is_leasable` (BOOLEAN, DEFAULT TRUE) - *Flag if it's generally intended for leasing.*
*   `move_in_ready` (BOOLEAN, DEFAULT TRUE)
*   `last_renovation_date` (DATE, NULLABLE)
*   `description` (TEXT) - *Specific details about the unit.*
*   `features_jsonb` (JSONB) - *Structured JSON for unique unit features (e.g., `{"accessibility_features": ["ramp", "elevator"], "appliances_included": ["stove", "fridge"]}`).*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

---

#### **III. Lease Agreement & Terms Management (Addresses Req #2 & #5)**

**7. `leases`** (The main lease agreement record - **addresses Req #2**)

*   `id` (PK, UUID)
*   `document_id` (FK to `documents.id`, ON DELETE SET NULL) - *Points to the primary executed lease document.*
*   `lease_name` (VARCHAR(255), NOT NULL) - *A human-readable name (e.g., "XYZ Corp Office Lease - Suite 100").*
*   `lease_type` (VARCHAR(100), NOT NULL) - *e.g., 'Main Lease', 'EV Charging Agreement', 'Sublease'. (N8n controlled vocabulary).*
*   `current_status` (VARCHAR(50), NOT NULL) - *e.g., 'Active', 'Expired', 'Terminated', 'Draft', 'Under Negotiation'. (N8n controlled vocabulary).*
*   `execution_date` (DATE) - *Date the lease was signed by all parties.*
*   `commencement_date` (DATE) - *Lease term commencement date (may precede rent start due to fixturing).*
*   `term_start_date` (DATE) - *Actual start of rent period (after fixturing, if any).*
*   `term_end_date` (DATE)
*   `initial_term_months` (INTEGER)
*   `initial_term_years` (INTEGER)
*   `possession_date` (DATE, NULLABLE)
*   `fixturing_period_start_date` (DATE, NULLABLE)
*   `fixturing_period_end_date` (DATE, NULLABLE)
*   `fixturing_period_months` (INTEGER, NULLABLE)
*   `version_number` (INTEGER, DEFAULT 1) - *To track major versions (e.g., via amendments affecting core terms).*
*   `original_lease_id` (FK to `leases.id`, NULLABLE) - *If this lease is a renewal or replacement of a previous one.*
*   `lease_language` (VARCHAR(50), NOT NULL) - *e.g., 'English', 'Français'. (N8n controlled vocabulary).*
*   `notes` (TEXT) - *General, unstructured notes about the lease.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**8. `lease_party_roles`** (Links `leases` to `parties` via roles - **Many-to-Many**)

*   `lease_id` (PK, FK to `leases.id`, ON DELETE CASCADE)
*   `party_id` (PK, FK to `parties.id`, ON DELETE CASCADE)
*   `role_name` (PK, VARCHAR(100)) - *e.g., 'Landlord', 'Tenant', 'Guarantor', 'Broker (Leasing)', 'Lender', 'Signatory', 'Property Manager (Lease Specific)'. (N8n controlled vocabulary).*
*   `is_primary_role` (BOOLEAN, DEFAULT FALSE) - *e.g., the main tenant, the main landlord.*
*   `role_specific_details_jsonb` (JSONB) - *For role-specific details not in `parties` (e.g., `{"representative_title": "CEO", "authorization_details": "Board Resolution XYZ"}`).*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**9. `lease_unit_allocations`** (Links `leases` to `property_units` - **Many-to-Many - addresses Req #2**)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `unit_id` (FK to `property_units.id`, ON DELETE CASCADE)
*   `effective_start_date` (DATE, NOT NULL) - *When this unit started being covered by this lease.*
*   `effective_end_date` (DATE, NULLABLE) - *When this unit ceased being covered by this lease (due to amendment or termination).*
*   `allocated_area_sqft` (NUMERIC) - *Store the area as per the lease allocation for this specific unit (important if the lease document's area differs from the physical unit's actual area).*
*   `proportionate_share_pct` (NUMERIC, NULLABLE) - *Tenant's proportionate share for *this specific unit/property portion**.*
*   `specific_use_details` (TEXT) - *Any specific permitted use for *this unit* under the lease (e.g., 'Storage of auto parts', 'Spa studio').*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**10. `rent_schedules`** (Detailed rent breakdown, **versioned**)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `schedule_type` (VARCHAR(100), NOT NULL) - *e.g., 'Base Rent', 'Semi-Gross Rent', 'Gross Rent', 'EV Charging Rent'. (N8n controlled vocabulary).*
*   `effective_start_date` (DATE, NOT NULL)
*   `effective_end_date` (DATE, NULLABLE) - *For versioning of rent schedules (e.g., different rent for different years or due to amendments).*
*   `annual_rent_per_sqft` (NUMERIC, NULLABLE)
*   `annual_total_amount` (NUMERIC, NOT NULL)
*   `monthly_installment` (NUMERIC, NOT NULL)
*   `currency` (VARCHAR(10), NOT NULL) - *e.g., 'CAD', 'USD'. (N8n controlled vocabulary).*
*   `escalation_details_jsonb` (JSONB) - *Structured JSON for escalation rules (e.g., `{"type": "CPI", "method": "CPI_Adjusted", "min_pct": 0.03, "max_pct": 0.04, "index_source": "Statistics Canada", "reference_month": "July"}`).*
*   `notes` (TEXT)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**11. `security_deposits`** (Details of security/prepaid deposits)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `deposit_type` (VARCHAR(100), NOT NULL) - *e.g., 'Security Deposit', 'Prepaid Rent', 'Pet Deposit'. (N8n controlled vocabulary).*
*   `amount` (NUMERIC, NOT NULL)
*   `currency` (VARCHAR(10), NOT NULL)
*   `is_refundable` (BOOLEAN, DEFAULT FALSE)
*   `refund_conditions` (TEXT)
*   `interest_accrual_terms` (TEXT)
*   `payment_method_details_jsonb` (JSONB) - *e.g., `{"initial_payment_method": "Wire Transfer", "bank_name": "ABC Bank"}`).*
*   `notes` (TEXT)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**12. `lease_financial_terms`** (Other financial related terms beyond rent/deposits, **versioned with key attributes extracted**)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `term_type` (VARCHAR(100), NOT NULL) - *e.g., 'Operating Expenses', 'Taxes', 'Utilities', 'Late Payment', 'Admin Fees (General)', 'Insurance Liability'. (N8n controlled vocabulary).*
*   `effective_start_date` (DATE, NOT NULL)
*   `effective_end_date` (DATE, NULLABLE)
*   `responsible_party_role` (VARCHAR(100), NULLABLE) - *e.g., 'Tenant', 'Landlord'. (N8n controlled vocabulary).*
*   **Common Financial Attributes (extracted for queryability):**
    *   `annual_rate_pct` (NUMERIC, NULLABLE) - *For admin fees (15%), or tax rates (GST/QST).*
    *   `fixed_amount` (NUMERIC, NULLABLE) - *For fixed fees like late payment fee.*
    *   `is_tenant_responsible_for_increase` (BOOLEAN, NULLABLE) - *Common for OpEx/Taxes.*
    *   `calculation_method` (VARCHAR(100), NULLABLE) - *e.g., 'Proportionate Share', 'Metered', 'Fixed Rate'.*
    *   `currency` (VARCHAR(10), NULLABLE)
*   `details_jsonb` (JSONB) - **Remaining granular details and rule configurations.**
    *   *For 'Operating Expenses':* `{"includes_capital_exp": true, "capital_exp_amortization_years": 10, "capital_exp_interest_rate_above_prime_pct": 0.03, "gross_up_enabled": true, "gross_up_occupancy_pct": 100, "inclusions_text": "...", "exclusions_text": "..."}`
    *   *For 'Taxes':* `{"contestation_rights_details": "...", "reimbursement_terms_details": "...", "tax_breakdown_jsonb": {"municipal": 1000, "school": 200}}`
    *   *For 'Utilities':* `{"metering_type": "sub_metered", "utility_types_covered": ["electricity", "water", "gas"], "billing_frequency": "quarterly"}`
    *   *For 'Late Payment':* `{"interest_rate_formula": "Prime + 4%", "threshold_days": 5}`
    *   *For 'Insurance Liability':* `{"min_coverage_amount": 5000000, "named_insureds_roles": ["Landlord", "Mortgagee"], "waiver_of_subrogation_required": true, "insurer_notification_days": 30, "self_insured_items": ["plate glass"], "tenant_pays_increase_premium": true}`
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**13. `lease_terms_and_conditions`** (General clauses, **versioned with key attributes extracted**)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `clause_type` (VARCHAR(100), NOT NULL) - *e.g., 'Permitted Use', 'Maintenance Obligations', 'Default/Remedies', 'Signage Provisions', 'Assignment/Subletting', 'Destruction/Expropriation', 'Health Emergency', 'Overholding', 'Force Majeure', 'Notice Provisions', 'Indemnification', 'Estoppel Certificate', 'Subordination/Attornment', 'Power of Attorney', 'Landlord Release', 'Financial Info Provision'. (N8n controlled vocabulary).*
*   `effective_start_date` (DATE, NOT NULL)
*   `effective_end_date` (DATE, NULLABLE) - *Crucial for versioning: when a clause changes via amendment, the old record gets an `effective_end_date`, and a new record for the same `clause_type` with the new terms gets a new `effective_start_date`.*
*   `summary_description` (TEXT) - *A brief human-readable summary of the clause.*
*   `responsible_party_role` (VARCHAR(100), NULLABLE) - *Primary responsible party (e.g., 'Tenant' for maintenance).*
*   **Common Clause Attributes (extracted for queryability):**
    *   `notice_period_days` (INTEGER, NULLABLE) - *For notices, termination, options.*
    *   `is_transferrable` (BOOLEAN, NULLABLE) - *For assignment/subletting.*
    *   `cure_period_days` (INTEGER, NULLABLE) - *For defaults.*
    *   `is_recurring` (BOOLEAN, NULLABLE) - *For maintenance/cleaning obligations.*
    *   `is_prohibited` (BOOLEAN, NULLABLE) - *For use restrictions.*
    *   `fixed_amount_value` (NUMERIC, NULLABLE) - *For specific fixed amounts in clauses.*
*   `details_jsonb` (JSONB) - **Remaining granular details and complex logic.**
    *   *For 'Permitted Use':* `{"description_full": "...", "exclusivity_clause_details": "...", "no_continuous_operation_allowed": true, "parking_details": {"type": "reserved", "spots": 25, "cost_per_spot": 150.00}}`
    *   *For 'Maintenance Obligations':* `{"maintenance_type_specific": "HVAC", "scope_details": "entire HVAC system", "notification_requirements_text": "5 days written", "admin_fee_on_landlord_costs_rate_pct": 0.15}`
    *   *For 'Default/Remedies':* `{"default_trigger_events": "...", "accelerated_rent_months": 9, "remedy_description_full": "...", "pre_determined_service_charge_amount": 100, "interest_rate_formula": "Prime + 5%"}`
    *   *For 'Assignment/Subletting':* `{"consent_required": true, "admin_fee_amount": 1500, "landlord_can_terminate_in_lieu_of_consent": true, "tenant_remains_liable": true, "liability_duration": "initial_term_only", "permitted_transferee_definition": "..."}`
    *   *For 'Destruction/Expropriation':* `{"repair_timeline_days": 180, "rent_abatement_formula": "proportional_unusable_area", "landlord_termination_notice_days": 60, "expropriation_compensation_allocation": "landlord_retains_building_portion"}`
    *   *For 'Overholding':* `{"rent_multiplier": 1.5, "additional_rent_multiplier_pct": 0.0833, "no_tacit_renewal": true}`
    *   *For 'Signage Provisions':* `{"signage_type_specific": "Pylon", "approval_required": true, "cost_responsibility": "Tenant", "abatement_for_delay_details": "..."}`
    *   *For 'Health Emergency':* `{"landlord_rights": "...", "liability_exclusions": "..."}`
    *   *For 'Indemnification':* `{"indemnifying_party_role": "Tenant", "indemnified_party_role": "Landlord", "scope_of_indemnity": "all claims", "triggering_events": "breach of obligations"}`
    *   *For 'Estoppel Certificate':* `{"response_time_days": 5, "required_information_details": "..."}`
    *   *For 'Subordination/Attornment':* `{"subordination_type": "all existing and future", "attornment_required": true, "document_delivery_days": 10}`
    *   *For 'Power of Attorney':* `{"appointed_party_role": "Landlord", "granting_party_role": "Tenant", "scope_of_authority": "execute instruments", "trigger_condition": "failure to sign"}`
    *   *For 'Landlord Release':* `{"release_event_type": "sale", "release_details": "Landlord fully released from covenants"}`
    *   *For 'Financial Info Provision':* `{"party_required_to_provide_role": "Tenant", "information_type": "financial standing", "requesting_parties": "Landlord, Mortgagee"}`
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**14. `lease_options`** (Renewal, Purchase, Early Termination options, **versioned**)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `option_type` (VARCHAR(100), NOT NULL) - *e.g., 'Renewal', 'Purchase', 'Early Termination', 'Right of First Refusal'. (N8n controlled vocabulary).*
*   `option_number` (INTEGER, NULLABLE) - *For 1st renewal, 2nd renewal, etc.*
*   `effective_start_date` (DATE, NOT NULL)
*   `effective_end_date` (DATE, NULLABLE)
*   `notice_period_days` (INTEGER)
*   `exercise_conditions_text` (TEXT)
*   **Common Option Attributes (extracted):**
    *   `option_term_months` (INTEGER, NULLABLE) - *e.g., 60 months for a 5-year renewal.*
    *   `rent_calculation_method` (VARCHAR(100), NULLABLE) - *e.g., 'Fair Market Rent', 'Fixed Increase', 'CPI Adjusted'.*
    *   `penalty_amount` (NUMERIC, NULLABLE) - *For early termination.*
    *   `purchase_price_formula` (TEXT, NULLABLE) - *For purchase options.*
*   `details_jsonb` (JSONB) - *Remaining specific parameters, e.g., escalation specifics for renewal, specific exclusions for inducements on renewal, exact formulas.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**15. `lease_amendments`** (Tracks executed amendments to leases - **addresses Req #5**)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `amendment_document_id` (FK to `documents.id`, ON DELETE SET NULL) - *Points to the executed amendment document.*
*   `amendment_date` (DATE, NOT NULL)
*   `description` (TEXT) - *What was amended (e.g., "Updated rent for year 3", "Added new unit to lease").*
*   `applies_to_lease_version` (INTEGER, NULLABLE) - *If the amendment specifically refers to a lease version.*
*   `is_major_amendment` (BOOLEAN, DEFAULT FALSE)
*   `details_jsonb` (JSONB) - *Any additional context from the amendment.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

---

#### **IV. Tenant & Applicant Management (Addresses Req #3: Manage Applicants)**

**16. `tenants`** (Dedicated table for tenant-specific data)

*   `id` (PK, UUID)
*   `party_id` (FK to `parties.id`, ON DELETE CASCADE, UNIQUE) - *Each `tenant` record must correspond to a `party`.*
*   `tenant_type` (VARCHAR(50), NOT NULL) - *e.g., 'Individual', 'Commercial', 'Government'. (N8n controlled vocabulary).*
*   `current_lease_id` (FK to `leases.id`, NULLABLE, UNIQUE) - *Optional shortcut to the tenant's primary active lease (useful for quick lookups; can be managed by n8n).*
*   `credit_score` (INTEGER, NULLABLE)
*   `annual_income` (NUMERIC, NULLABLE)
*   `employment_status` (VARCHAR(100), NULLABLE)
*   `employer_name` (VARCHAR(255), NULLABLE)
*   `move_in_date` (DATE, NULLABLE) - *Effective move-in date for *current* active tenancy, if applicable.*
*   `move_out_date` (DATE, NULLABLE) - *Effective move-out date for *current* active tenancy.*
*   `emergency_contact_jsonb` (JSONB) - *`{"name": "John Doe", "phone": "************", "relationship": "Spouse"}`.*
*   `notes` (TEXT)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**17. `rental_applications`** (Logging applicant requests - **addresses Req #3**)

*   `id` (PK, UUID)
*   `applicant_party_id` (FK to `parties.id`, ON DELETE CASCADE) - *Links to the party who is applying.*
*   `property_id` (FK to `properties.id`, ON DELETE CASCADE)
*   `unit_id` (FK to `property_units.id`, NULLABLE) - *Specific unit applied for (or NULL if general application for any unit).*
*   `application_date` (DATE, DEFAULT CURRENT_DATE)
*   `desired_move_in_date` (DATE)
*   `application_status` (VARCHAR(50), NOT NULL) - *e.g., 'Pending', 'Approved', 'Rejected', 'Withdrawn', 'Converted to Lease'. (N8n controlled vocabulary).*
*   `screening_status_jsonb` (JSONB) - *`{"background_check_status": "completed", "credit_check_status": "clear", "reference_check_status": "pending"}`.*
*   `application_fee_paid` (BOOLEAN, DEFAULT FALSE)
*   `application_fee_amount` (NUMERIC, NULLABLE)
*   `notes` (TEXT)
*   `decision_date` (DATE, NULLABLE)
*   `decision_reason` (TEXT, NULLABLE)
*   `analysis_data_jsonb` (JSONB) - *Results from automated analysis (e.g., `{"risk_score": 75, "match_criteria": ["income", "pets"], "alerts": ["past eviction"]}`).* **This is key for "automated analysis".**
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**18. `tenant_communications`** (All communications with tenants/applicants/parties)

*   `id` (PK, UUID)
*   `communication_type` (VARCHAR(50), NOT NULL) - *e.g., 'Email', 'Phone Call', 'Internal Note', 'SMS'. (N8n controlled vocabulary).*
*   `subject` (VARCHAR(255), NULLABLE)
*   `content` (TEXT, NOT NULL)
*   `sent_at` (TIMESTAMPTZ, DEFAULT NOW())
*   `status` (VARCHAR(50), NOT NULL) - *e.g., 'Sent', 'Draft', 'Read', 'Failed'. (N8n controlled vocabulary).*
*   `priority` (VARCHAR(50), NOT NULL) - *e.g., 'Normal', 'High', 'Urgent'. (N8n controlled vocabulary).*
*   `follow_up_required` (BOOLEAN, DEFAULT FALSE)
*   `follow_up_date` (DATE, NULLABLE)
*   `attachments_jsonb` (JSONB) - *URLs or IDs of attached files.*
*   `created_by_user_id` (FK to `users.id`)
*   `linked_entities_jsonb` (JSONB) - *Generic way to link to other entities: `[{"entity_type": "party", "entity_id": "uuid"}, {"entity_type": "lease", "entity_id": "uuid"}, {"entity_type": "property", "entity_id": "uuid"}, {"entity_type": "work_order", "entity_id": "uuid"}]`.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

---

#### **V. Financial Management (Payments & Expenses - Addresses Req #7: Manage Payments)**

**19. `tenant_payments`** (Logging payments from tenants - **addresses Req #7**)

*   `id` (PK, UUID)
*   `lease_id` (FK to `leases.id`, ON DELETE CASCADE)
*   `paying_party_id` (FK to `parties.id`, ON DELETE CASCADE) - *The actual party who made the payment.*
*   `payment_date` (DATE, NOT NULL)
*   `amount` (NUMERIC, NOT NULL)
*   `currency` (VARCHAR(10), NOT NULL)
*   `payment_method` (VARCHAR(50), NOT NULL) - *e.g., 'EFT', 'Cheque', 'Credit Card', 'Wire Transfer'. (N8n controlled vocabulary).*
*   `reference_number` (VARCHAR(100), NULLABLE) - *Cheque number, transaction ID.*
*   `applies_to_period_start_date` (DATE, NULLABLE) - *Which rent period this payment primarily covers.*
*   `applies_to_period_end_date` (DATE, NULLABLE)
*   `payment_type` (VARCHAR(100), NOT NULL) - *e.g., 'Base Rent', 'Additional Rent', 'Security Deposit', 'Late Fee', 'Utility Reimbursement'. (N8n controlled vocabulary).*
*   `is_late` (BOOLEAN, DEFAULT FALSE)
*   `notes` (TEXT)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**20. `property_expenses`** (All expenses related to a property, distinct from tenant-specific lease financials)

*   `id` (PK, UUID)
*   `property_id` (FK to `properties.id`, ON DELETE CASCADE)
*   `expense_type` (VARCHAR(100), NOT NULL) - *e.g., 'Maintenance', 'Utilities', 'Taxes', 'Management Fee', 'Capital Improvement', 'Insurance Premium', 'Janitorial'. (N8n controlled vocabulary).*
*   `description` (TEXT)
*   `amount` (NUMERIC, NOT NULL)
*   `currency` (VARCHAR(10), NOT NULL)
*   `expense_date` (DATE, NOT NULL)
*   `vendor_party_id` (FK to `parties.id`, NULLABLE) - *The party providing the service/goods.*
*   `invoice_document_id` (FK to `documents.id`, NULLABLE) - *Link to the invoice document.*
*   `payment_status` (VARCHAR(50), NOT NULL) - *e.g., 'Pending', 'Paid', 'Overdue', 'Partially Paid'. (N8n controlled vocabulary).*
*   `payment_date` (DATE, NULLABLE)
*   `is_recurring` (BOOLEAN, DEFAULT FALSE)
*   `recurring_frequency` (VARCHAR(50), NULLABLE) - *e.g., 'Monthly', 'Annually', 'Quarterly'.*
*   `notes` (TEXT)
*   `created_by_user_id` (FK to `users.id`)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

---

#### **VI. Operational Work Management (Addresses Req #6: Manage All Work)**

**21. `work_orders`** (Centralized table for all work requests - **addresses Req #6**)

*   `id` (PK, UUID)
*   `property_id` (FK to `properties.id`, ON DELETE CASCADE)
*   `unit_id` (FK to `property_units.id`, NULLABLE) - *If work is unit-specific.*
*   `work_type` (VARCHAR(100), NOT NULL) - *e.g., 'Maintenance', 'Cleaning', 'HVAC Service', 'Plumbing Repair', 'Tenant Improvement', 'Inspection Follow-up'. (N8n controlled vocabulary).*
*   `description` (TEXT, NOT NULL)
*   `requested_by_party_id` (FK to `parties.id`, NULLABLE) - *Who requested (tenant, property manager, staff).*
*   `assigned_to_party_id` (FK to `parties.id`, NULLABLE) - *Contractor or internal team.*
*   `status` (VARCHAR(50), NOT NULL) - *e.g., 'New', 'Assigned', 'In Progress', 'Completed', 'On Hold', 'Canceled', 'Needs Quote'. (N8n controlled vocabulary).*
*   `priority` (VARCHAR(50), NOT NULL) - *e.g., 'Normal', 'High', 'Urgent'. (N8n controlled vocabulary).*
*   `requested_date` (DATE, DEFAULT CURRENT_DATE)
*   `scheduled_date` (DATE, NULLABLE)
*   `due_date` (DATE, NULLABLE)
*   `completion_date` (DATE, NULLABLE)
*   `estimated_cost` (NUMERIC, NULLABLE)
*   `actual_cost` (NUMERIC, NULLABLE)
*   `invoice_document_id` (FK to `documents.id`, NULLABLE)
*   `is_recurring` (BOOLEAN, DEFAULT FALSE)
*   `recurring_schedule_jsonb` (JSONB, NULLABLE) - *e.g., `{"frequency": "monthly", "day_of_month": 15, "start_date": "YYYY-MM-DD"}`.*
*   `linked_lease_clause_id` (FK to `lease_terms_and_conditions.id`, NULLABLE) - *If this work fulfills a specific lease obligation (e.g., a recurring cleaning schedule specified in the lease).*
*   `notes` (TEXT)
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**22. `work_order_updates`** (Updates/comments on work orders)

*   `id` (PK, UUID)
*   `work_order_id` (FK to `work_orders.id`, ON DELETE CASCADE)
*   `update_date` (TIMESTAMPTZ, DEFAULT NOW())
*   `status_update` (VARCHAR(50), NULLABLE) - *The new status name for this update. (N8n controlled vocabulary).*
*   `comment` (TEXT, NOT NULL)
*   `created_by_user_id` (FK to `users.id`)
*   `created_at` (TIMESTAMPTZ)

**23. `property_inspections`** (General inspections of properties/units - **addresses Req #4**)

*   `id` (PK, UUID)
*   `property_id` (FK to `properties.id`, ON DELETE CASCADE)
*   `unit_id` (FK to `property_units.id`, NULLABLE) - *If inspection is unit-specific.*
*   `inspection_type` (VARCHAR(100), NOT NULL) - *e.g., 'Acquisition Due Diligence', 'Routine Maintenance', 'Move-in/out', 'Damage Assessment'. (N8n controlled vocabulary).*
*   `inspection_date` (DATE, NOT NULL)
*   `inspector_party_id` (FK to `parties.id`, NULLABLE)
*   `status` (VARCHAR(50), NOT NULL) - *e.g., 'Scheduled', 'In Progress', 'Completed', 'Needs Follow-up'. (N8n controlled vocabulary).*
*   `overall_condition_notes` (TEXT)
*   `issues_found_jsonb` (JSONB) - *`[{"category": "HVAC", "description": "AC unit noisy", "severity": "medium", "work_order_id": "uuid_if_created"}]`.*
*   `recommendations` (TEXT)
*   `follow_up_required` (BOOLEAN, DEFAULT FALSE)
*   `follow_up_date` (DATE, NULLABLE)
*   `report_document_id` (FK to `documents.id`, NULLABLE) - *Link to the full inspection report document.*
*   `photos_jsonb` (JSONB) - *URLs to inspection photos.*
*   `tenant_present` (BOOLEAN, DEFAULT FALSE)
*   `signatures_jsonb` (JSONB) - *e.g., `{"tenant_signature_url": "...", "inspector_signature_url": "..."}`.*
*   `linked_acquisition_pipeline_id` (FK to `property_acquisition_pipeline.id`, NULLABLE) - *If this inspection is part of an acquisition process.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

---

#### **VII. Acquisition Management (Addresses Req #4: Manage New Acquisitions)**

**24. `property_acquisition_pipeline`** (For potential new acquisitions - **addresses Req #4**)

*   `id` (PK, UUID)
*   `potential_property_name` (VARCHAR(255), NOT NULL)
*   `address_street` (VARCHAR(255))
*   `address_city` (VARCHAR(100))
*   `address_province` (VARCHAR(50))
*   `address_postal_code` (VARCHAR(20))
*   `status` (VARCHAR(50), NOT NULL) - *e.g., 'Identified', 'Under Review', 'Due Diligence', 'Offer Made', 'Acquired', 'Rejected', 'On Hold'. (N8n controlled vocabulary).*
*   `initial_contact_date` (DATE)
*   `target_acquisition_date` (DATE, NULLABLE)
*   `estimated_value` (NUMERIC, NULLABLE)
*   `acquisition_notes` (TEXT)
*   `due_diligence_details_jsonb` (JSONB) - *Any specific checklists or findings during due diligence.*
*   `acquired_property_id` (FK to `properties.id`, NULLABLE, UNIQUE) - *Once acquired, link to the actual `properties` record to prevent duplication.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

---

#### **VIII. Chat Functionality (Addresses Req #5 - *Correction: This is Chat Functionality, not Req #5 which is Amendments*)**

**25. `chat_sessions`**

*   `id` (PK, UUID)
*   `user_id` (FK to `users.id`) - *The internal user managing/participating in the session.*
*   `related_party_id` (FK to `parties.id`, NULLABLE) - *If chat is with a specific tenant/applicant/vendor.*
*   `session_type` (VARCHAR(50), NOT NULL) - *e.g., 'AI Assistant', 'Tenant Support', 'Internal Discussion', 'Acquisition Inquiry'. (N8n controlled vocabulary).*
*   `status` (VARCHAR(50), NOT NULL) - *e.g., 'Open', 'Closed', 'Archived'. (N8n controlled vocabulary).*
*   `last_message_at` (TIMESTAMPTZ, DEFAULT NOW())
*   `context_data_jsonb` (JSONB) - *Stores any session-specific context, like current lease_id, property_id, work_order_id.*
*   `created_at` (TIMESTAMPTZ)
*   `updated_at` (TIMESTAMPTZ)

**26. `messages`**

*   `id` (PK, UUID)
*   `session_id` (FK to `chat_sessions.id`, ON DELETE CASCADE)
*   `sender_type` (VARCHAR(50), NOT NULL) - *e.g., 'User', 'AI', 'Staff', 'System'. (N8n controlled vocabulary).*
*   `sender_user_id` (FK to `users.id`, NULLABLE) - *Internal user who sent the message.*
*   `sender_party_id` (FK to `parties.id`, NULLABLE) - *External party (e.g., tenant) who sent the message.*
*   `content` (TEXT, NOT NULL)
*   `sent_at` (TIMESTAMPTZ, DEFAULT NOW())
*   `message_type` (VARCHAR(50), NOT NULL) - *e.g., 'Text', 'Tool Output', 'System Message', 'Image Link'. (N8n controlled vocabulary).*
*   `metadata_jsonb` (JSONB) - *Any additional message metadata, like tool outputs, sentiment, n8n workflow data.*
*   `created_at` (TIMESTAMPTZ)

---

#### **IX. Audit Logging**

**27. `audit_logs`** (System-level logging for critical actions and data changes)

*   `id` (PK, UUID)
*   `timestamp` (TIMESTAMPTZ, DEFAULT NOW())
*   `event_type` (VARCHAR(100), NOT NULL) - *e.g., 'Data Insertion', 'Data Update', 'Document Upload', 'Lease Activated', 'Payment Recorded', 'Work Order Status Change'. (N8n controlled vocabulary).*
*   `user_id` (FK to `users.id`, NULLABLE) - *User who performed the action.*
*   `related_entity_type` (VARCHAR(100), NULLABLE) - *e.g., 'lease', 'property', 'party', 'document', 'work_order'. (N8n controlled vocabulary).*
*   `related_entity_id` (UUID, NULLABLE) - *ID of the entity affected.*
*   `description` (TEXT, NOT NULL) - *Human-readable summary of the event.*
*   `old_value_jsonb` (JSONB, NULLABLE) - *Old state of affected data (for specific fields, not full row).*
*   `new_value_jsonb` (JSONB, NULLABLE) - *New state of affected data (for specific fields, not full row).*
*   `details_jsonb` (JSONB, NULLABLE) - *Any additional context (e.g., workflow ID, IP address, specific extracted fields from N8n).*

---
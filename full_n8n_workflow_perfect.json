{"nodes": [{"parameters": {"model": "google/gemini-2.5-flash-preview-05-20:thinking", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-3740, 8180], "id": "f0027b61-2e85-4fcb-972d-30bfa42b6d64", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "N9BoqTeHPsx7qRBt", "name": "OpenRouter account"}}}, {"parameters": {"operation": "get", "tableId": "documents", "filters": {"conditions": [{"keyName": "id", "keyValue": "=3c7f667a-e645-49d6-8b64-eb66706f3815"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-4820, 7820], "id": "ac502af6-d060-4277-97a8-3511cf21d3c7", "name": "Get Document Content", "credentials": {"supabaseApi": {"id": "BEQlvIctl3TWAjRn", "name": "Brasswater"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "doc_id"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-4980, 7820], "id": "fdafdfe5-5f03-408f-a50e-7e3229197ccf", "name": "When Executed by Another Workflow"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e0a0568a-8864-4e70-bea8-babc353a0374", "leftValue": "={{ $json.complete_content }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-4580, 7820], "id": "a435e9b7-96b9-4b2d-9908-0dd2d0260190", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-4340, 8020], "id": "4d1e0c30-5dae-4622-9b47-87cc19c3ce8f", "name": "No Operation, do nothing"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"document_filename\": {\n      \"type\": \"string\",\n      \"description\": \"The filename exactly as shown: {{ $json.name }}\"\n    },\n    \"file_url\": {\n      \"type\": \"string\",\n      \"description\": \"The URL to the stored document file (S3, etc.)\"\n    },\n    \"lease_number\": {\n      \"type\": \"string\",\n      \"description\": \"The lease number (Numéro du bail/Lease Number)\"\n    },\n    \"pwgsc_region_file\": {\n      \"type\": \"string\",\n      \"description\": \"PWGSC Region File number (Dossier de la région de TPSGC)\"\n    },\n    \"client_info\": {\n      \"type\": \"string\",\n      \"description\": \"Client information exactly as written (e.g., Client: EDSC)\"\n    },\n    \"lease_status\": {\n      \"type\": \"string\",\n      \"description\": \"Lease status from filename (e.g., 'Fully Executed') or document, mapping to leases.current_status\"\n    },\n    \"lease_name\": {\n      \"type\": \"string\",\n      \"description\": \"A human-readable name for the lease (e.g., 'XYZ Corp Office Lease - Suite 100')\"\n    },\n    \"lease_type\": {\n      \"type\": \"string\",\n      \"description\": \"The type of lease (e.g., 'Main Lease', 'EV Charging Agreement', 'Sublease')\"\n    },\n    \"version_number\": {\n      \"type\": [\"number\", \"string\"],\n      \"description\": \"Version number if specified, otherwise 'Not specified in document'\"\n    },\n    \"original_lease_id\": {\n      \"type\": \"string\",\n      \"description\": \"If this lease is a renewal or replacement of a previous one\"\n    },\n    \"lease_language\": {\n      \"type\": \"string\",\n      \"description\": \"Primary language of the document (English or Français)\"\n    },\n    \"document_date\": {\n      \"type\": \"string\",\n      \"format\": \"date\",\n      \"description\": \"The overall date the physical document was issued/signed in YYYY-MM-DD format\"\n    },\n    \"notes\": {\n      \"type\": \"string\",\n      \"description\": \"General, unstructured notes about the lease.\"\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract basic lease identification EXACTLY as written. CRITICAL: Find lease number (Numéro du bail), PWGSC file (Dossier de la région), client info (Client:). Check the filename (provided as: {{ $json.name }}) for lease status like 'Fully Executed'. Identify the primary language, overall document date (YYYY-MM-DD), and document type. Capture any other unclassified document-level metadata. If any field is not found, state 'Not specified in document'. "}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-4340, 7800], "id": "8f3bb57b-3250-4264-8795-5f818b46d241", "name": "1. Document & Lease ID"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"execution_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date the lease was signed by all parties in YYYY-MM-DD format\"},\n    \"commencement_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Lease term commencement date (may precede rent start due to fixturing) in YYYY-MM-DD format\"},\n    \"term_start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Actual start of rent period (after fixturing, if any) in YYYY-MM-DD format\"},\n    \"term_end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Lease term end date in YYYY-MM-DD format\"},\n    \"possession_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date physical possession of premises was taken in YYYY-MM-DD format\"},\n    \"fixturing_period_start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Start date of fixturing period in YYYY-MM-DD format\"},\n    \"fixturing_period_end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"End date of fixturing period in YYYY-MM-DD format\"},\n    \"fixturing_period_months\": {\"type\": \"integer\", \"description\": \"Duration of fixturing period in months\"},\n    \"initial_term_months\": {\"type\": \"integer\", \"description\": \"Initial term duration in months\"},\n    \"initial_term_years\": {\"type\": \"integer\", \"description\": \"Initial term duration in years\"}\n  }\n}", "options": {"systemPromptTemplate": "Extract all dates related to the lease term and execution EXACTLY as written. CRITICAL: Output all dates in YYYY-MM-DD format. Look for SEPARATE execution dates (landlord and tenant may sign on different dates). Search for phrases like 'signed by Landlord on' and 'signed by Tenant on'. If possession date is not explicitly different from commencement, state 'Same as commencement date'. Focus on commencement date, term end date, and possession date."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 6560], "id": "8b9f7406-bb88-4e5e-b7d7-2df702296cf0", "name": "2. Dates & Terms"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"landlord\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"party_name\": {\"type\": \"string\"},\n        \"former_name\": {\"type\": \"string\"},\n        \"legal_status\": {\"type\": \"string\"},\n        \"address_street\": {\"type\": \"string\"},\n        \"address_city\": {\"type\": \"string\"},\n        \"address_province\": {\"type\": \"string\"},\n        \"address_postal_code\": {\"type\": \"string\"},\n        \"primary_email\": {\"type\": \"string\", \"format\": \"email\", \"description\": \"Primary email for the landlord\"},\n        \"primary_phone_number\": {\"type\": \"string\", \"description\": \"Primary phone number for the landlord\"},\n        \"representative_name\": {\"type\": \"string\"},\n        \"representative_title\": {\"type\": \"string\"},\n        \"authorization_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"}\n      }\n    },\n    \"tenant\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"party_name\": {\"type\": \"string\"},\n        \"legal_status\": {\"type\": \"string\"},\n        \"legal_address\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"street\": {\"type\": \"string\"},\n            \"city\": {\"type\": \"string\"},\n            \"province\": {\"type\": \"string\"},\n            \"postal_code\": {\"type\": \"string\"}\n          }\n        },\n        \"notice_address\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"street\": {\"type\": \"string\"},\n            \"city\": {\"type\": \"string\"},\n            \"province\": {\"type\": \"string\"},\n            \"postal_code\": {\"type\": \"string\"}\n          }\n        },\n        \"primary_email\": {\"type\": \"string\", \"format\": \"email\", \"description\": \"Primary email for the tenant\"},\n        \"primary_phone_number\": {\"type\": \"string\", \"description\": \"Primary phone number for the tenant\"},\n        \"representative_name\": {\"type\": \"string\"},\n        \"representative_title\": {\"type\": \"string\"},\n        \"delegation_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"}\n      }\n    },\n    \"board_resolution\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"resolution_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n        \"authorization_details\": {\"type\": \"string\"},\n        \"lease_reference\": {\"type\": \"string\"},\n        \"leased_premises_area_sqft\": {\"type\": \"number\"},\n        \"board_signatures\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}}\n      }\n    },\n    \"party_role\": {\"type\": \"string\", \"description\": \"Additional role specification for the party (e.g., 'Landlord', 'Tenant', 'Guarantor'). If it's the main party extracted, indicate 'Primary'.\"},\n    \"is_primary_role\": {\"type\": \"boolean\", \"description\": \"e.g., the main tenant, the main landlord.\"},\n    \"role_specific_details_jsonb\": {\"type\": \"object\", \"description\": \"For role-specific details not in parties (e.g., {\"representative_title\": \"CEO\", \"authorization_details\": \"Board Resolution XYZ\"}).\"},\n    \"additional_contact_info_jsonb\": {\"type\": \"object\", \"description\": \"For fax, secondary contacts, other addresses (e.g., notice_address, billing_address, head_office if different from primary), and their types.\"},\n    \"notes\": {\"type\": \"string\", \"description\": \"General, unstructured notes about the party.\"},\n    \"brokers\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"broker_name\": {\"type\": \"string\"},\n          \"firm_name\": {\"type\": \"string\"},\n          \"broker_type\": {\"type\": \"string\", \"description\": \"e.g., 'Tenant Broker', 'Landlord Broker', 'Co-Broker'\"},\n          \"commission_details\": {\"type\": \"string\", \"description\": \"Details about commission structure or payment\"},\n          \"contact_info_jsonb\": {\"type\": \"object\", \"description\": \"Email, phone, address etc.\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract party information EXACTLY as written. CRITICAL: Output all dates in YYYY-MM-DD format. For landlord, look for 'autrefois connue sous' for former names. For tenant, extract BOTH the general legal address AND the specific address from Section 31 'AVIS' for notices - these are different addresses. Extract primary email and phone numbers if available. Extract specific authorization/delegation dates. Also, extract details from the 'RESOLUTIONS OF THE BOARD OF DIRECTORS' section: resolution date, authorization details, lease reference, leased premises area in square feet, and names of board members who signed. Include party role (e.g., 'Landlord', 'Tenant', 'Guarantor' and indicate 'Primary' if it's the main party), primary role flag, role-specific details, additional contact info, general notes, and broker details in JSONB fields."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 6760], "id": "83fe23a3-1d81-4d62-9b18-057e37030d98", "name": "3. Complete Party Info"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"property_name\": {\"type\": \"string\", \"description\": \"The name of the building or property\"},\n    \"property_address_street\": {\"type\": \"string\"},\n    \"property_address_city\": {\"type\": \"string\"},\n    \"property_address_province\": {\"type\": \"string\"},\n    \"property_address_postal_code\": {\"type\": \"string\"},\n    \"property_address_country\": {\"type\": \"string\", \"default\": \"Canada\"},\n    \"property_type\": {\"type\": \"string\", \"description\": \"Type of property (e.g., 'Commercial Building', 'Shopping Center')\"},\n    \"property_current_status\": {\"type\": \"string\", \"description\": \"Current status of the property (e.g., 'Active', 'Under Acquisition')\"},\n    \"total_land_area_sqft\": {\"type\": \"number\"},\n    \"total_built_area_sqft\": {\"type\": \"number\"},\n    \"total_built_area_sqm\": {\"type\": \"number\"},\n    \"year_built\": {\"type\": \"integer\"},\n    \"number_of_floors\": {\"type\": \"integer\"},\n    \"total_parking_spaces\": {\"type\": \"number\", \"description\": \"Sum of all parking spaces on the property\"},\n    \"interior_parking_spaces\": {\"type\": \"number\"},\n    \"exterior_parking_spaces\": {\"type\": \"number\"},\n    \"property_manager_name\": {\"type\": \"string\", \"description\": \"Name of the party managing this property\"},\n    \"description\": {\"type\": \"string\", \"description\": \"General description of the property\"},\n    \"zoning_classification\": {\"type\": \"string\"},\n    \"legal_description\": {\"type\": \"string\", \"description\": \"Full lot numbers, cadastre details from any relevant document.\"},\n    \"amenities_jsonb\": {\"type\": \"object\", \"description\": \"Structured JSON for amenities: {\\\"common_areas\\\": [\\\"gym\\\", \\\"pool\\\"], \\\"security_features\\\": [\\\"24/7\\\", \\\"cameras\\\"]}\"},\n    \"media_urls_jsonb\": {\"type\": \"object\", \"description\": \"URLs to property photos/videos.\"},\n    \"external_reference_id\": {\"type\": \"string\"},\n    \"leased_units\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"unit_number\": {\"type\": \"string\"},\n          \"unit_type\": {\"type\": \"string\", \"description\": \"Type of unit (e.g., 'Office', 'Retail', 'Warehouse')\"},\n          \"floor_number\": {\"type\": \"integer\"},\n          \"rentable_area_sqft\": {\"type\": \"number\"},\n          \"rentable_area_sqm\": {\"type\": \"number\"},\n          \"unit_current_status\": {\"type\": \"string\", \"description\": \"Current status of the unit (e.g., 'Vacant', 'Occupied')\"},\n          \"is_leasable\": {\"type\": \"boolean\"},\n          \"move_in_ready\": {\"type\": \"boolean\"},\n          \"last_renovation_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"description\": {\"type\": \"string\", \"description\": \"Specific details about the unit.\"},\n          \"features_jsonb\": {\"type\": \"object\", \"description\": \"Structured JSON for unique unit features (e.g., {\\\"accessibility_features\\\": [\\\"ramp\\\", \\\"elevator\\\"], \\\"appliances_included\\\": [\\\"stove\\\", \\\"fridge\\\"]}).\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract detailed property and unit information EXACTLY as written. CRITICAL: Look for property name, full address components (street, city, province, postal code, country), property type, status, total land and built areas (in both sqft and sqm if available), year built, number of floors, property manager name. Distinguish between interior and exterior parking spaces; calculate total parking spaces. Extract zoning classification, legal descriptions (cadastre, lot number). Identify all leased units, extracting unit number, type, floor, rentable area (sqft/sqm), current status, leasable flag, move-in readiness, last renovation date, and any specific unit features. Note any discrepancies in area measurements. Include comprehensive details in JSONB fields for amenities, media URLs, and unit features."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 6960], "id": "be5f5996-a787-4a92-8fd6-bd7dd9d26a0e", "name": "4. Property & Building"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"rent_schedules\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"schedule_type\": {\"type\": \"string\", \"description\": \"Type of rent schedule (e.g., 'Base Rent', 'Semi-Gross Rent')\"},\n          \"effective_start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"effective_end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"annual_rent_per_sqft\": {\"type\": \"number\"},\n          \"annual_total_amount\": {\"type\": \"number\"},\n          \"monthly_installment\": {\"type\": \"number\"},\n          \"currency\": {\"type\": \"string\", \"description\": \"e.g., 'CAD', 'USD'\"},\n          \"escalation_details_jsonb\": {\"type\": \"object\", \"description\": \"Structured JSON for escalation rules (e.g., {\\\"type\\\": \\\"CPI\\\", \\\"method\\\": \\\"CPI_Adjusted\\\", \\\"min_pct\\\": 0.03, \\\"max_pct\\\": 0.04, \\\"index_source\\\": \\\"Statistics Canada\\\", \\\"reference_month\\\": \\\"July\\\"}).\"},\n          \"notes\": {\"type\": \"string\"}\n        }\n      }\n    },\n    \"lease_financial_terms\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"term_type\": {\"type\": \"string\", \"description\": \"Type of financial term (e.g., 'Operating Expenses', 'Taxes', 'Utilities', 'Late Payment')\"},\n          \"effective_start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"effective_end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"responsible_party_role\": {\"type\": \"string\", \"description\": \"e.g., 'Tenant', 'Landlord'\"},\n          \"annual_rate_pct\": {\"type\": \"number\"},\n          \"fixed_amount\": {\"type\": \"number\"},\n          \"is_tenant_responsible_for_increase\": {\"type\": \"boolean\"},\n          \"calculation_method\": {\"type\": \"string\"},\n          \"currency\": {\"type\": \"string\", \"description\": \"e.g., 'CAD', 'USD'\"},\n          \"details_jsonb\": {\"type\": \"object\", \"description\": \"Remaining granular details and rule configurations\"}\n        }\n      }\n    },\n    \"general_gst_rate\": {\"type\": [\"number\", \"string\"], \"description\": \"General GST rate if found outside specific terms\"},\n    \"general_qst_rate\": {\"type\": [\"number\", \"string\"], \"description\": \"General QST rate if found outside specific terms\"},\n    \"general_admin_fee_on_operating_expenses\": {\"type\": \"number\", \"description\": \"Admin fee on operating expenses if global\"},\n    \"general_admin_fee_on_taxes\": {\"type\": \"number\", \"description\": \"Admin fee on taxes if global\"},\n    \"general_late_payment_fee\": {\"type\": \"number\", \"description\": \"Late payment fee if global\"},\n    \"general_interest_rate_formula\": {\"type\": \"string\", \"description\": \"General interest rate formula if global\"},\n    \"admin_fee_structure\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"accessory_costs_rate\": {\"type\": \"number\"},\n        \"construction_low_rate\": {\"type\": \"number\"},\n        \"construction_mid_fixed\": {\"type\": \"number\"},\n        \"construction_high_rate\": {\"type\": \"number\"},\n        \"complete_structure\": {\"type\": \"string\"}\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract all detailed financial terms EXACTLY as written, separating into rent schedules and other financial terms. For rent schedules, include schedule type (e.g., 'Base Rent'), effective start/end dates (YYYY-MM-DD), annual rent per sqft, annual total amount, monthly installment, currency, escalation details as JSONB, and notes. For other financial terms, include term type (e.g., 'Operating Expenses', 'Taxes', 'Late Payment'), effective start/end dates (YYYY-MM-DD), responsible party role, annual rate percent, fixed amount, tenant responsibility for increase, calculation method, currency, and detailed JSONB. Capture general GST/QST rates, admin fees, and late payment fee formulas if globally specified rather than per-term. Also extract the complete tiered admin fee structure from Annex F: accessory costs 5%, construction up to $30K: 15%, $30K-$45K: fixed $4,500, >$45K: 10%. Do not simplify to single percentage."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 7160], "id": "24c1b169-6631-4460-8f07-cb07684609f7", "name": "5. Detailed Financial"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"deposits\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"deposit_type\": {\"type\": \"string\", \"description\": \"Type of deposit (e.g., 'Security Deposit', 'Prepaid Rent', 'Pet Deposit')\"},\n          \"amount\": {\"type\": \"number\"},\n          \"currency\": {\"type\": \"string\", \"description\": \"e.g., 'CAD', 'USD'\"},\n          \"is_refundable\": {\"type\": \"boolean\"},\n          \"refund_conditions\": {\"type\": \"string\"},\n          \"interest_accrual_terms\": {\"type\": \"string\"},\n          \"payment_method_details_jsonb\": {\"type\": \"object\", \"description\": \"JSON for payment method details\"},\n          \"notes\": {\"type\": \"string\"}\n        }\n      }\n    },\n    \"deposit_absent_note\": {\"type\": \"string\", \"description\": \"Note if no security deposit provisions found in the lease\"}\n  }\n}", "options": {"systemPromptTemplate": "Extract all security and prepaid deposit information. CRITICAL: Identify each deposit type (e.g., 'Security Deposit', 'Prepaid Rent', 'Pet Deposit'), its amount, and currency. Determine if it's refundable. Extract refund conditions, interest accrual terms, and payment method details as JSONB. If no deposit information is found, explicitly state 'No security deposit provisions found in the lease' for the deposit_absent_note, and leave the 'deposits' array empty. Do not assume or default to 0."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 7360], "id": "cafbecda-63c1-49f5-895c-cb9f638f75f7", "name": "6. Security & Deposits"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"lease_options\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"option_type\": {\"type\": \"string\", \"description\": \"Type of option (e.g., 'Renewal', 'Purchase', 'Early Termination', 'Right of First Refusal')\"},\n          \"option_number\": {\"type\": \"integer\", \"description\": \"For 1st Renewal, 2nd Renewal, etc.\"},\n          \"effective_start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"effective_end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"notice_period_days\": {\"type\": \"integer\"},\n          \"exercise_conditions_text\": {\"type\": \"string\"},\n          \"option_term_months\": {\"type\": \"integer\", \"description\": \"e.g., 60 months for a 5-year renewal\"},\n          \"rent_calculation_method\": {\"type\": \"string\", \"description\": \"e.g., 'Fair Market Rent', 'Fixed Increase', 'CPI Adjusted'\"},\n          \"penalty_amount\": {\"type\": \"number\", \"description\": \"For early termination\"},\n          \"purchase_price_formula\": {\"type\": \"string\", \"description\": \"For purchase options\"},\n          \"details_jsonb\": {\"type\": \"object\", \"description\": \"Remaining specific parameters, e.g., escalation specifics for renewal, specific exclusions for inducements on renewal, exact formulas\"}\n        }\n      }\n    },\n    \"holdover_provision\": {\"type\": \"object\", \"properties\": {\"rent_multiplier\": {\"type\": \"number\"}, \"additional_rent_multiplier_pct\": {\"type\": \"number\"}, \"no_tacit_renewal\": {\"type\": \"boolean\"}, \"details_jsonb\": {\"type\": \"object\"}}},\n    \"guarantors\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"guarantee_type\": {\"type\": \"string\"},\n          \"max_liability_amount\": {\"type\": \"number\"},\n          \"guarantee_duration_start\": {\"type\": \"string\", \"format\": \"date\"},\n          \"guarantee_duration_end\": {\"type\": \"string\", \"format\": \"date\"},\n          \"conditions\": {\"type\": \"string\"},\n          \"details_jsonb\": {\"type\": \"object\", \"description\": \"Any other guarantor-specific details\"}\n        }\n      }\n    },\n    \"renewal_discrepancy_note\": {\"type\": \"string\"}\n  }\n}", "options": {"systemPromptTemplate": "Extract all lease options including Renewal, Purchase, Early Termination, and Right of First Refusal. For each option, include option type, option number, effective start/end dates (YYYY-MM-DD), notice period days, exercise conditions, option term in months, rent calculation method, penalty amount (for early termination), purchase price formula (for purchase options), and any remaining structured or unstructured details in a JSONB field. Also extract holdover provisions (rent multiplier, additional rent, no tacit renewal) and all guarantor details including type, maximum liability amount, guarantee duration (start and end dates in YYYY-MM-DD), and conditions. Note any discrepancies found between summary and detailed renewal terms."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 7760], "id": "0abbf28b-4eb9-4296-b348-05feed3c4c75", "name": "8. Rights & Options"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"environmental_provisions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"provision_description\": {\"type\": \"string\"},\n          \"regulatory_references\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"e.g., 'ASHRAE Standard 62.1-2019', 'CSA Z94.4-18'\"},\n          \"responsible_party_role\": {\"type\": \"string\"},\n          \"notes\": {\"type\": \"string\"}\n        }\n      }\n    },\n    \"tenant_inducements\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"inducement_type\": {\"type\": \"string\", \"description\": \"e.g., 'Rent-Free Period', 'Tenant Improvement Allowance', 'Moving Allowance', 'Early Occupancy'\"},\n          \"amount\": {\"type\": \"number\"},\n          \"currency\": {\"type\": \"string\"},\n          \"start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"conditions\": {\"type\": \"string\"},\n          \"details_jsonb\": {\"type\": \"object\", \"description\": \"Any complex or additional inducement details\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract environmental provisions and tenant inducements. For environmental provisions, capture description, specific regulatory references (e.g., ASHRAE, CSA standards including full citations), responsible party, and notes. For tenant inducements, identify inducement type (e.g., 'Rent-Free Period', 'Tenant Improvement Allowance'), amount, currency, start/end dates (YYYY-MM-DD), conditions, and detailed JSONB."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 7960], "id": "7195f603-5a8f-466b-ad75-d839659f2a02", "name": "9. Environmental & Inducements"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"use_restrictions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"description_full\": {\"type\": \"string\"},\n          \"exclusivity_clause_details\": {\"type\": \"string\"},\n          \"no_continuous_operation_allowed\": {\"type\": \"boolean\"},\n          \"parking_details\": {\"type\": \"object\", \"properties\": {\"type\": {\"type\": \"string\"}, \"spots\": {\"type\": \"number\"}, \"cost_per_spot\": {\"type\": \"number\"}}}\n        }\n      }\n    },\n    \"signage_provisions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"signage_type_specific\": {\"type\": \"string\"},\n          \"approval_required\": {\"type\": \"boolean\"},\n          \"cost_responsibility\": {\"type\": \"string\"},\n          \"abatement_for_delay_details\": {\"type\": \"string\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract use restrictions and signage provisions. CRITICAL: For signage, distinguish between building identification and lessee visibility. Extract EXACT language about who provides vs installs signage. Look specifically for 'entièrement aux frais du Locateur' (entirely at Lessor's expense) for cost responsibility. "}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 8160], "id": "175af0f6-81af-4b24-85db-3f5015297799", "name": "10. Use & Signage"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"maintenance_obligations\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"maintenance_type_specific\": {\"type\": \"string\"},\n          \"scope_details\": {\"type\": \"string\"},\n          \"notification_requirements_text\": {\"type\": \"string\"},\n          \"admin_fee_on_landlord_costs_rate_pct\": {\"type\": \"number\"}\n        }\n      }\n    },\n    \"improvement_terms\": {\"type\": \"array\", \"items\": {\"type\": \"object\"}},\n    \"access_inspection_rights\": {\"type\": \"array\", \"items\": {\"type\": \"object\"}}\n  }\n}", "options": {"systemPromptTemplate": "Extract maintenance obligations and improvement terms. CRITICAL: For maintenance obligations, extract who is responsible for the cost (e.g., 'entirely assumed by the Tenant'). "}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 8360], "id": "09eb5b69-f40f-4d62-a05b-8f083d58fd6e", "name": "11. Maintenance & Improvements"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"assignment_subletting\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"consent_required\": {\"type\": \"boolean\"},\n          \"admin_fee_amount\": {\"type\": \"number\"},\n          \"landlord_can_terminate_in_lieu_of_consent\": {\"type\": \"boolean\"},\n          \"tenant_remains_liable\": {\"type\": \"boolean\"},\n          \"liability_duration\": {\"type\": \"string\"},\n          \"permitted_transferee_definition\": {\"type\": \"string\"}\n        }\n      }\n    },\n    \"default_remedies\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"default_trigger_events\": {\"type\": \"string\"},\n          \"accelerated_rent_months\": {\"type\": \"number\"},\n          \"remedy_description_full\": {\"type\": \"string\"},\n          \"pre_determined_service_charge_amount\": {\"type\": \"number\"},\n          \"interest_rate_formula\": {\"type\": \"string\"},\n          \"cure_period_description\": {\"type\": \"string\"},\n          \"cure_period_days\": {\"type\": \"number\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract assignment/subletting terms and default remedies. CRITICAL: For cure periods, use exact language like 'reasonable period' or 'as specified in notice' - only use specific days if explicitly stated as number. Do not use 0 days for variable periods."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 8560], "id": "dfe939cc-fbe6-4f32-8ffd-77e3f8c9d464", "name": "12. Assignment & Defaults"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"insurance_liability\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"min_coverage_amount\": {\"type\": \"number\"},\n          \"named_insureds_roles\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n          \"waiver_of_subrogation_required\": {\"type\": \"boolean\"},\n          \"insurer_notification_days\": {\"type\": \"integer\"},\n          \"self_insured_items\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n          \"tenant_pays_increase_premium\": {\"type\": \"boolean\"}\n        }\n      }\n    },\n    \"compliance_requirements\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"compliance_type\": {\"type\": \"string\"},\n          \"description\": {\"type\": \"string\"},\n          \"specific_standards_references\": {\"type\": \"string\"},\n          \"deficiency_correction_timeline\": {\"type\": \"string\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract insurance and compliance requirements. CRITICAL: Extract specific regulatory references (ASHRAE, CSA standards, etc.) in full. Look for deficiency correction timelines including 30% withholding provisions. Include additional insurance/liability details in a JSONB field."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 8760], "id": "6ee40289-eac8-433a-898f-572714afc044", "name": "13. Insurance & Compliance"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"force_majeure\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"description\": {\"type\": \"string\"}\n      }\n    },\n    \"destruction_expropriation\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"repair_timeline_days\": {\"type\": \"integer\"},\n        \"rent_abatement_formula\": {\"type\": \"string\"},\n        \"landlord_termination_notice_days\": {\"type\": \"integer\"},\n        \"expropriation_compensation_allocation\": {\"type\": \"string\"}\n      }\n    },\n    \"notice_provisions\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"notice_period_days\": {\"type\": \"integer\"}\n      }\n    },\n    \"dispute_resolution\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"description\": {\"type\": \"string\"}\n      }\n    },\n    \"estoppel_certificate\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"response_time_days\": {\"type\": \"integer\"},\n        \"required_information_details\": {\"type\": \"string\"}\n      }\n    },\n    \"subordination_attornment\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"subordination_type\": {\"type\": \"string\"},\n        \"attornment_required\": {\"type\": \"boolean\"},\n        \"document_delivery_days\": {\"type\": \"integer\"}\n      }\n    },\n    \"power_of_attorney\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"appointed_party_role\": {\"type\": \"string\"},\n        \"granting_party_role\": {\"type\": \"string\"},\n        \"scope_of_authority\": {\"type\": \"string\"},\n        \"trigger_condition\": {\"type\": \"string\"}\n      }\n    },\n    \"landlord_release\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"release_event_type\": {\"type\": \"string\"},\n        \"release_details\": {\"type\": \"string\"}\n      }\n    },\n    \"financial_info_provision\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"party_required_to_provide_role\": {\"type\": \"string\"},\n        \"information_type\": {\"type\": \"string\"},\n        \"requesting_parties\": {\"type\": \"string\"}\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract all legal provisions including force majeure, destruction/expropriation, notice provisions, dispute resolution, and advanced provisions (estoppel, subordination, indemnification, etc.). Include provision type, category, title, description, jurisdiction, requirements, and effective date."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 8960], "id": "2b329074-9020-4b1f-96f4-f1442cef37d5", "name": "14. Legal Provisions"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"signatures\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"party_type\": {\"type\": \"string\"},\n          \"signatory_name\": {\"type\": \"string\"},\n          \"signatory_title\": {\"type\": \"string\"},\n          \"execution_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"}\n        }\n      }\n    },\n    \"attachments\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"attachment_type\": {\"type\": \"string\"},\n          \"attachment_name\": {\"type\": \"string\"},\n          \"annex_reference\": {\"type\": \"string\"},\n          \"file_url\": {\"type\": \"string\", \"description\": \"URL to the stored attachment\"},\n          \"attachment_metadata\": {\"type\": \"object\", \"description\": \"Any other attachment-specific details\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract all signature details and document attachments. CRITICAL: Output all dates in YYYY-MM-DD format. For signatures, include party type, signatory name, title, and execution date. List all annexes, appendices, and attachments referenced. Include file URL and any other attachment metadata."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 9160], "id": "9bcb8989-0b23-4387-9903-79cacb745378", "name": "15. Signatures & Attachments"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"lease_unit_allocations\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"lease_id\": {\"type\": \"string\"},\n          \"unit_id\": {\"type\": \"string\"},\n          \"effective_start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"effective_end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"allocated_area_sqft\": {\"type\": \"number\"},\n          \"proportionate_share_pct\": {\"type\": \"number\"},\n          \"specific_use_details\": {\"type\": \"string\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract all lease unit allocations, including lease ID, unit ID, effective start and end dates (YYYY-MM-DD), allocated area in sqft, proportionate share percentage, and specific use details for each unit under the lease."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 7560], "id": "a0b1c2d3-e4f5-6789-abcd-ef0123456789", "name": "7. Lease Unit Allocations"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"lease_amendments\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"lease_id\": {\"type\": \"string\"},\n          \"amendment_document_id\": {\"type\": \"string\"},\n          \"amendment_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"description\": {\"type\": \"string\"},\n          \"applies_to_lease_version\": {\"type\": \"integer\"},\n          \"is_major_amendment\": {\"type\": \"boolean\"},\n          \"details_jsonb\": {\"type\": \"object\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract all lease amendments, including lease ID, amendment document ID, amendment date (YYYY-MM-DD), description, applicable lease version, whether it's a major amendment, and any additional details in JSONB."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 9360], "id": "b0c1d2e3-f4a5-6789-bcde-f01234567890", "name": "16. Lease Amendments"}, {"parameters": {"text": "={{ $('Get Document Content').first().json.complete_content }}", "schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"tenant_payments\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"lease_id\": {\"type\": \"string\"},\n          \"paying_party_id\": {\"type\": \"string\"},\n          \"payment_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"amount\": {\"type\": \"number\"},\n          \"currency\": {\"type\": \"string\"},\n          \"payment_method\": {\"type\": \"string\"},\n          \"reference_number\": {\"type\": \"string\"},\n          \"applies_to_period_start_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"applies_to_period_end_date\": {\"type\": \"string\", \"format\": \"date\", \"description\": \"Date in YYYY-MM-DD format\"},\n          \"payment_type\": {\"type\": \"string\"},\n          \"is_late\": {\"type\": \"boolean\"},\n          \"notes\": {\"type\": \"string\"}\n        }\n      }\n    }\n  }\n}", "options": {"systemPromptTemplate": "Extract all tenant payments, including lease ID, paying party ID, payment date (YYYY-MM-DD), amount, currency, payment method, reference number, period start and end dates (YYYY-MM-DD) the payment covers, payment type, whether it was late, and any notes."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-3020, 9560], "id": "c0d1e2f3-a4b5-6789-cdef-012345678901", "name": "17. Tenant Payments"}], "connections": {"OpenRouter Chat Model": {"ai_languageModel": [[{"node": "1. Document & Lease ID", "type": "ai_languageModel", "index": 0}, {"node": "2. Dates & Terms", "type": "ai_languageModel", "index": 0}, {"node": "3. Complete Party Info", "type": "ai_languageModel", "index": 0}, {"node": "4. Property & Building", "type": "ai_languageModel", "index": 0}, {"node": "5. Detailed Financial", "type": "ai_languageModel", "index": 0}, {"node": "6. Security & Deposits", "type": "ai_languageModel", "index": 0}, {"node": "7. Lease Unit Allocations", "type": "ai_languageModel", "index": 0}, {"node": "8. Rights & Options", "type": "ai_languageModel", "index": 0}, {"node": "9. Environmental & Inducements", "type": "ai_languageModel", "index": 0}, {"node": "10. Use & Signage", "type": "ai_languageModel", "index": 0}, {"node": "11. Maintenance & Improvements", "type": "ai_languageModel", "index": 0}, {"node": "12. Assignment & Defaults", "type": "ai_languageModel", "index": 0}, {"node": "13. Insurance & Compliance", "type": "ai_languageModel", "index": 0}, {"node": "14. Legal Provisions", "type": "ai_languageModel", "index": 0}, {"node": "15. Signatures & Attachments", "type": "ai_languageModel", "index": 0}, {"node": "16. Lease Amendments", "type": "ai_languageModel", "index": 0}, {"node": "17. Tenant Payments", "type": "ai_languageModel", "index": 0}]]}, "Get Document Content": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Get Document Content", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "1. Document & Lease ID", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "1. Document & Lease ID": {"main": [[{"node": "2. Dates & Terms", "type": "main", "index": 0}, {"node": "3. Complete Party Info", "type": "main", "index": 0}, {"node": "4. Property & Building", "type": "main", "index": 0}, {"node": "5. Detailed Financial", "type": "main", "index": 0}, {"node": "6. Security & Deposits", "type": "main", "index": 0}, {"node": "7. Lease Unit Allocations", "type": "main", "index": 0}, {"node": "8. Rights & Options", "type": "main", "index": 0}, {"node": "9. Environmental & Inducements", "type": "main", "index": 0}, {"node": "10. Use & Signage", "type": "main", "index": 0}, {"node": "11. Maintenance & Improvements", "type": "main", "index": 0}, {"node": "12. Assignment & Defaults", "type": "main", "index": 0}, {"node": "13. Insurance & Compliance", "type": "main", "index": 0}, {"node": "14. Legal Provisions", "type": "main", "index": 0}, {"node": "15. Signatures & Attachments", "type": "main", "index": 0}, {"node": "16. Lease Amendments", "type": "main", "index": 0}, {"node": "17. Tenant Payments", "type": "main", "index": 0}]]}, "2. Dates & Terms": {"main": [[]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "2faaf9ff911f9829d7f722a5ec919613e50308f0846f977c43de1cabcc499753"}}
const CACHE_NAME = 'brasswater-v2';
const STATIC_CACHE = 'brasswater-static-v2';
const DYNAMIC_CACHE = 'brasswater-dynamic-v2';
const API_CACHE = 'brasswater-api-v2';

// URLs to cache during install
const urlsToCache = [
  '/',
  '/dashboard',
  '/properties',
  '/leases',
  '/tenants',
  '/auth',
  '/manifest.json',
  '/lovable-uploads/d1576679-7628-47b0-a3b6-63c60a322061.png'
];

// API endpoints that should be cached
const apiCachePatterns = [
  /supabase\.co.*\/rest\/v1/,
  /supabase\.co.*\/auth\/v1/
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('[SW] Installing...');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Caching static resources');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('[SW] Skip waiting');
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE && 
              cacheName !== DYNAMIC_CACHE && 
              cacheName !== API_CACHE) {
            console.log('[SW] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('[SW] Claiming clients');
      return self.clients.claim();
    })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle different types of requests
  if (request.url.includes('/auth/v1/token')) {
    // Never cache auth tokens
    event.respondWith(fetch(request));
  } else if (apiCachePatterns.some(pattern => pattern.test(request.url))) {
    // API requests - Network First with cache fallback
    event.respondWith(networkFirstStrategy(request, API_CACHE));
  } else if (request.destination === 'image') {
    // Images - Cache First
    event.respondWith(cacheFirstStrategy(request, DYNAMIC_CACHE));
  } else if (url.origin === location.origin) {
    // Same origin requests - Stale While Revalidate
    event.respondWith(staleWhileRevalidateStrategy(request, DYNAMIC_CACHE));
  } else {
    // External requests - Network First
    event.respondWith(networkFirstStrategy(request, DYNAMIC_CACHE));
  }
});

// Caching strategies
async function cacheFirstStrategy(request, cacheName) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('[SW] Cache first strategy failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

async function networkFirstStrategy(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('[SW] Network failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    return new Response('Offline', { status: 503 });
  }
}

async function staleWhileRevalidateStrategy(request, cacheName) {
  const cachedResponse = await caches.match(request);
  
  const fetchPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      const cache = caches.open(cacheName);
      cache.then(c => c.put(request, networkResponse.clone()));
    }
    return networkResponse;
  }).catch(() => {
    // If network fails and we have cache, return cache
    return cachedResponse;
  });

  // Return cache immediately if available, otherwise wait for network
  return cachedResponse || fetchPromise;
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync-lease-data') {
    event.waitUntil(syncLeaseData());
  }
});

async function syncLeaseData() {
  try {
    // Implement background sync logic for lease data
    console.log('[SW] Syncing lease data in background');
    // This would sync any offline changes when connection is restored
  } catch (error) {
    console.log('[SW] Background sync failed:', error);
  }
}

// Push notifications
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New notification from Brasswater',
    icon: '/lovable-uploads/d1576679-7628-47b0-a3b6-63c60a322061.png',
    badge: '/lovable-uploads/d1576679-7628-47b0-a3b6-63c60a322061.png',
    vibrate: [200, 100, 200],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/lovable-uploads/d1576679-7628-47b0-a3b6-63c60a322061.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/lovable-uploads/d1576679-7628-47b0-a3b6-63c60a322061.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('Brasswater', options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked');
  event.notification.close();

  if (event.action === 'view') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

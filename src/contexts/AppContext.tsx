
import { createContext, useContext, useState, ReactNode } from 'react';

interface AppSettings {
  theme: 'light' | 'dark';
  notifications: boolean;
  currency: string;
}

interface AppContextType {
  settings: AppSettings;
  updateSettings: (settings: Partial<AppSettings>) => void;
  notifications: Array<{ id: string; message: string; type: 'info' | 'success' | 'warning' | 'error' }>;
  addNotification: (notification: { message: string; type: 'info' | 'success' | 'warning' | 'error' }) => void;
  removeNotification: (id: string) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider = ({ children }: AppProviderProps) => {
  const [settings, setSettings] = useState<AppSettings>({
    theme: 'light',
    notifications: true,
    currency: 'USD',
  });

  const [notifications, setNotifications] = useState<Array<{ 
    id: string; 
    message: string; 
    type: 'info' | 'success' | 'warning' | 'error' 
  }>>([]);

  const updateSettings = (newSettings: Partial<AppSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const addNotification = (notification: { message: string; type: 'info' | 'success' | 'warning' | 'error' }) => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { ...notification, id }]);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      removeNotification(id);
    }, 5000);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const value = {
    settings,
    updateSettings,
    notifications,
    addNotification,
    removeNotification,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

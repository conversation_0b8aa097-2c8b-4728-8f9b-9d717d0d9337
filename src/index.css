
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 11% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 11% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 11% 15%;

    --primary: 25 65% 55%;
    --primary-foreground: 210 40% 98%;

    --secondary: 25 20% 90%;
    --secondary-foreground: 25 30% 25%;

    --muted: 25 10% 95%;
    --muted-foreground: 25 8% 45%;

    --accent: 25 30% 85%;
    --accent-foreground: 25 40% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 25 15% 88%;
    --input: 25 15% 88%;
    --ring: 25 65% 55%;

    --radius: 0.5rem;

    --sidebar-background: 210 20% 12%;
    --sidebar-foreground: 25 15% 85%;
    --sidebar-primary: 25 65% 55%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 15% 18%;
    --sidebar-accent-foreground: 25 65% 55%;
    --sidebar-border: 210 15% 20%;
    --sidebar-ring: 25 65% 55%;
  }

  .dark {
    --background: 210 20% 8%;
    --foreground: 25 15% 90%;

    --card: 210 20% 10%;
    --card-foreground: 25 15% 90%;

    --popover: 210 20% 10%;
    --popover-foreground: 25 15% 90%;

    --primary: 25 65% 55%;
    --primary-foreground: 210 20% 8%;

    --secondary: 210 15% 15%;
    --secondary-foreground: 25 15% 90%;

    --muted: 210 15% 15%;
    --muted-foreground: 25 10% 65%;

    --accent: 210 15% 20%;
    --accent-foreground: 25 65% 55%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 210 15% 20%;
    --input: 210 15% 20%;
    --ring: 25 65% 55%;
    
    --sidebar-background: 210 25% 6%;
    --sidebar-foreground: 25 15% 85%;
    --sidebar-primary: 25 65% 55%;
    --sidebar-primary-foreground: 210 25% 6%;
    --sidebar-accent: 210 20% 12%;
    --sidebar-accent-foreground: 25 65% 55%;
    --sidebar-border: 210 20% 15%;
    --sidebar-ring: 25 65% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile-first responsive design utilities */
@layer utilities {
  /* Safe area insets for iOS devices with notches */
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .pt-safe {
    padding-top: env(safe-area-inset-top);
  }
  
  .pl-safe {
    padding-left: env(safe-area-inset-left);
  }
  
  .pr-safe {
    padding-right: env(safe-area-inset-right);
  }

  /* Touch-friendly tap targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better scrolling on mobile */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent horizontal scroll on mobile */
  .no-horizontal-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Mobile-optimized grid */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .mobile-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  /* Better mobile typography */
  .mobile-text {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
  }

  /* Mobile-friendly buttons */
  .mobile-button {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 16px;
  }

  /* Card stack layout for mobile */
  .card-stack {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  @media (min-width: 768px) {
    .card-stack {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
  }

  /* Mobile navigation */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 8px 0;
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 50;
  }

  /* Hide scrollbars on mobile when not needed */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Backdrop blur for mobile overlays */
  .mobile-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Focus styles for accessibility */
  .focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .high-contrast {
      border: 2px solid;
      background: white;
      color: black;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .respect-motion {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Dark mode improvements */
  @media (prefers-color-scheme: dark) {
    .auto-dark {
      background: hsl(var(--background));
      color: hsl(var(--foreground));
    }
  }

  /* Landscape orientation adjustments */
  @media (orientation: landscape) and (max-height: 500px) {
    .landscape-compact {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-only {
      display: block !important;
    }
  }
}

/* PWA specific styles */
@media (display-mode: standalone) {
  /* Styles for when app is installed as PWA */
  body {
    user-select: none; /* Prevent text selection in PWA mode */
  }
  
  .pwa-only {
    display: block;
  }
  
  /* Remove browser chrome effects */
  .standalone-app {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
}

/* iOS specific adjustments */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari specific styles */
  .ios-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Fix iOS zoom on input focus */
  input, select, textarea {
    font-size: 16px !important;
  }
}

/* Android specific adjustments */
@media screen and (-webkit-min-device-pixel-ratio: 2) and (orientation: portrait) {
  /* Android Chrome specific styles */
  .android-chrome {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Touch device optimizations */
@media (pointer: coarse) {
  /* Larger touch targets for touch devices */
  button, 
  input[type="button"], 
  input[type="submit"],
  input[type="reset"],
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Better spacing for touch interfaces */
  .touch-spacing {
    margin: 8px 0;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders and shadows on high DPI displays */
  .crisp-edges {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

import { useEffect } from 'react';
import { useMessages } from './hooks/useMessages';
import { useRealtimeMessages } from './hooks/useRealtimeMessages';
import { useChatState } from './hooks/useChatState';
import { useChatOperations } from './hooks/useChatOperations';
import { ChatContainer } from './ChatContainer';
import { useAuth } from '@/contexts/AuthContext';

interface ChatInterfaceProps {
  leaseId: string;
  threadId: string | null;
  initialMessage?: string | null;
  onThreadCreated: (threadId: string) => void;
  onInitialMessageSent?: () => void;
}

export const ChatInterface = ({ 
  leaseId, 
  threadId, 
  initialMessage, 
  onThreadCreated, 
  onInitialMessageSent 
}: ChatInterfaceProps) => {
  const { user, supabaseUser } = useAuth();
  
  // Load messages and manage state
  const { messages, messagesLoading } = useMessages(threadId, leaseId);
  
  // State management hook
  const {
    currentSessionId,
    setCurrentSessionId,
    isWaitingForAssistant,
    setIsWaitingForAssistant,
    shouldProcessInitialMessage,
    resetInitialMessageFlag
  } = useChatState({ threadId, initialMessage, messages });
  
  // Chat operations hook
  const { handleSendMessage, isLoading } = useChatOperations({
    leaseId,
    currentSessionId,
    onThreadCreated,
    onInitialMessageSent,
    setCurrentSessionId,
    setIsWaitingForAssistant
  });
  
  // Subscribe to new messages
  useRealtimeMessages(currentSessionId);
  
  console.log('🔍 ChatInterface Debug:', {
    leaseId,
    threadId,
    currentSessionId,
    initialMessage,
    user,
    supabaseUser,
    messagesCount: messages.length,
    messagesLoading,
    shouldProcessInitialMessage
  });

  // Handle initial message when needed
  useEffect(() => {
    if (shouldProcessInitialMessage && initialMessage) {
      console.log('🎬 Processing initial message:', initialMessage);
      handleSendMessage(initialMessage, isWaitingForAssistant);
      resetInitialMessageFlag();
    }
  }, [shouldProcessInitialMessage, initialMessage, handleSendMessage, isWaitingForAssistant, resetInitialMessageFlag]);

  const handleMessageSend = (content: string) => {
    handleSendMessage(content, isWaitingForAssistant);
  };

  console.log('🎯 ChatInterface render state:', {
    isLoading,
    isWaitingForAssistant,
    messagesCount: messages.length,
    hasCurrentSession: !!currentSessionId
  });

  return (
    <ChatContainer
      messages={messages}
      messagesLoading={messagesLoading}
      onSendMessage={handleMessageSend}
      isLoading={isLoading}
      isWaitingForAssistant={isWaitingForAssistant}
    />
  );
};

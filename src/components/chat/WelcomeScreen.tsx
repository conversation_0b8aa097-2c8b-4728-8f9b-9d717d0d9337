
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Sparkles, FileText, Search, Calculator } from 'lucide-react';
import { ChatInput } from './ChatInput';

interface WelcomeScreenProps {
  onStartChat: (content?: string) => void;
}

export const WelcomeScreen = ({ onStartChat }: WelcomeScreenProps) => {
  console.log('🏠 WelcomeScreen rendered with onStartChat:', typeof onStartChat);

  const examples = [
    {
      icon: FileText,
      title: "Analyze lease terms",
      description: "What are the key terms and conditions in this lease?",
      prompt: "Can you analyze the key terms and conditions in this lease document?"
    },
    {
      icon: Calculator,
      title: "Calculate rent details",
      description: "Help me understand the rent calculation and payment schedule",
      prompt: "Can you explain the rent calculation and payment schedule for this lease?"
    },
    {
      icon: Search,
      title: "Find specific clauses",
      description: "Help me locate specific clauses or provisions",
      prompt: "Can you help me find and explain the maintenance obligations in this lease?"
    },
    {
      icon: <PERSON>rk<PERSON>,
      title: "General assistance",
      description: "Ask me anything about this lease document",
      prompt: "I have questions about this lease document. Can you help me understand it?"
    }
  ];

  const handleChatInput = (content: string) => {
    console.log('🏠 WelcomeScreen handleChatInput called with:', content);
    console.log('🏠 Calling onStartChat...');
    onStartChat(content);
    console.log('🏠 onStartChat called successfully');
  };

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-6 max-w-3xl mx-auto">
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-3">
          <Sparkles className="h-6 w-6 text-purple-500" />
        </div>
        <h1 className="text-xl font-bold text-gray-900 mb-2">
          AI Lease Assistant
        </h1>
        <p className="text-sm text-gray-600 max-w-xl">
          Ask me anything about this lease document. I can help you understand terms, 
          calculate costs, find specific clauses, and much more.
        </p>
      </div>

      <div className="w-full max-w-2xl mb-6">
        <ChatInput 
          onSendMessage={handleChatInput}
          placeholder="Message AI Assistant..."
        />
      </div>

      <div className="w-full max-w-3xl">
        <h2 className="text-sm font-medium text-gray-900 mb-3">
          Get started with an example below
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {examples.map((example, index) => (
            <Card 
              key={index}
              className="cursor-pointer hover:shadow-sm transition-shadow border border-gray-200 hover:border-purple-300"
              onClick={() => {
                console.log('🏠 Example card clicked:', example.prompt);
                onStartChat(example.prompt);
              }}
            >
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm">
                  <example.icon className="h-4 w-4 text-purple-500 mr-2" />
                  {example.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-gray-600">{example.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

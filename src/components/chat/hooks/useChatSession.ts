
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ChatSession } from '../types';

export const useChatSession = (leaseId: string) => {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  const { data: session, isLoading: sessionLoading } = useQuery({
    queryKey: ['chat-session', leaseId],
    queryFn: async (): Promise<ChatSession> => {
      // First try to find existing session
      const { data: existingSession } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('lease_id', leaseId)
        .single();

      if (existingSession) {
        setCurrentSessionId(existingSession.id);
        return existingSession;
      }

      // Create new session if none exists
      const { data: newSession, error } = await supabase
        .from('chat_sessions')
        .insert({
          lease_id: leaseId,
          user_id: (await supabase.auth.getUser()).data.user?.id,
        })
        .select()
        .single();

      if (error) throw error;
      setCurrentSessionId(newSession.id);
      return newSession;
    },
  });

  return {
    session,
    sessionLoading,
    currentSessionId,
    setCurrentSessionId
  };
};


import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useRealtimeMessages = (currentSessionId: string | null) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!currentSessionId) return;

    const channel = supabase
      .channel(`messages-${currentSessionId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `chat_session_id=eq.${currentSessionId}`,
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ['messages', currentSessionId] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [currentSessionId, queryClient]);
};

import { useState, useEffect } from 'react';
import { Message } from '../types';

interface UseChatStateProps {
  threadId: string | null;
  initialMessage?: string | null;
  messages: Message[];
}

export const useChatState = ({ threadId, initialMessage, messages }: UseChatStateProps) => {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(threadId);
  const [isWaitingForAssistant, setIsWaitingForAssistant] = useState(false);
  const [shouldProcessInitialMessage, setShouldProcessInitialMessage] = useState(false);

  // Update current session when threadId changes
  useEffect(() => {
    console.log('🔄 Thread ID changed:', { from: currentSessionId, to: threadId });
    setCurrentSessionId(threadId);
  }, [threadId]);

  // Handle initial message processing
  useEffect(() => {
    if (initialMessage && !currentSessionId) {
      console.log('🎬 Processing initial message:', initialMessage);
      setShouldProcessInitialMessage(true);
    }
  }, [initialMessage, currentSessionId]);

  // Check if we're waiting for assistant response
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      const hasUserMessageWithoutAssistantResponse = 
        lastMessage.sender === 'user' && 
        !messages.some(msg => msg.sender === 'assistant' && msg.created_at > lastMessage.created_at);
      
      console.log('📝 Message check:', {
        lastMessage: lastMessage.sender,
        hasUserMessageWithoutAssistantResponse,
        totalMessages: messages.length
      });
      
      setIsWaitingForAssistant(hasUserMessageWithoutAssistantResponse);
    } else {
      setIsWaitingForAssistant(false);
    }
  }, [messages]);

  const resetInitialMessageFlag = () => {
    setShouldProcessInitialMessage(false);
  };

  return {
    currentSessionId,
    setCurrentSessionId,
    isWaitingForAssistant,
    setIsWaitingForAssistant,
    shouldProcessInitialMessage,
    resetInitialMessageFlag
  };
};

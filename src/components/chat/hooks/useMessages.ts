
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Message } from '../types';
import { useAuth } from '@/contexts/AuthContext';

export const useMessages = (currentSessionId: string | null, leaseId: string) => {
  const { user, supabaseUser } = useAuth();

  // Get messages for the current session
  const { data: messages = [], isLoading: messagesLoading } = useQuery({
    queryKey: ['messages', currentSessionId],
    queryFn: async (): Promise<Message[]> => {
      console.log('🔍 useMessages Debug:', {
        currentSessionId,
        leaseId,
        user,
        supabaseUser
      });

      if (!currentSessionId) {
        console.log('⏭️ No session ID, returning empty array');
        return [];
      }
      
      console.log('📥 Fetching messages for session:', currentSessionId);
      
      // Check if user is authenticated
      if (!user) {
        console.error('❌ No authenticated user in useMessages');
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('chat_session_id', currentSessionId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Error fetching messages:', error);
        throw error;
      }
      
      console.log('✅ Fetched messages:', data);
      
      // Type cast the sender field to ensure it matches our interface
      return data.map(message => ({
        ...message,
        sender: message.sender as 'user' | 'bot' | 'assistant'
      })) as Message[];
    },
    enabled: !!currentSessionId && !!user,
  });

  // Remove the sendMessageMutation as message sending is now handled in ChatInterface
  const sendMessageMutation = {
    isPending: false,
    mutateAsync: async () => {},
  };

  console.log('📊 useMessages result:', {
    messagesCount: messages.length,
    messagesLoading,
    currentSessionId,
    enabled: !!currentSessionId && !!user
  });

  return {
    messages,
    messagesLoading,
    sendMessageMutation
  };
};

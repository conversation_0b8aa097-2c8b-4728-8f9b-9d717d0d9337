import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface UseChatOperationsProps {
  leaseId: string;
  currentSessionId: string | null;
  onThreadCreated: (threadId: string) => void;
  onInitialMessageSent?: () => void;
  setCurrentSessionId: (id: string | null) => void;
  setIsWaitingForAssistant: (waiting: boolean) => void;
}

export const useChatOperations = ({ 
  leaseId, 
  currentSessionId, 
  onThreadCreated, 
  onInitialMessageSent,
  setCurrentSessionId,
  setIsWaitingForAssistant
}: UseChatOperationsProps) => {
  const queryClient = useQueryClient();
  const { user, supabaseUser } = useAuth();

  // Create new thread mutation
  const createThreadMutation = useMutation({
    mutationFn: async (firstMessageContent: string) => {
      console.log('🚀 Creating new thread with message:', firstMessageContent);
      
      if (!user) {
        console.error('❌ User not authenticated');
        throw new Error('User not authenticated');
      }

      const userId = supabaseUser?.id || user.id;

      // Create new chat session
      const { data: newSession, error: sessionError } = await supabase
        .from('chat_sessions')
        .insert({
          lease_id: leaseId,
          user_id: userId,
          title: null,
          metadata: {}
        })
        .select()
        .single();

      if (sessionError) throw sessionError;

      // Create the first message
      const { data: newMessage, error: messageError } = await supabase
        .from('messages')
        .insert({
          chat_session_id: newSession.id,
          lease_id: leaseId,
          user_id: userId,
          sender: 'user',
          content: firstMessageContent,
          metadata: {}
        })
        .select()
        .single();

      if (messageError) throw messageError;

      // Update thread title based on first message
      const threadTitle = firstMessageContent.length > 50 
        ? firstMessageContent.substring(0, 50) + '...' 
        : firstMessageContent;
      
      const { error: updateError } = await supabase
        .from('chat_sessions')
        .update({ title: threadTitle })
        .eq('id', newSession.id);

      if (updateError) {
        console.error('⚠️ Title update error:', updateError);
      }

      return { session: newSession, message: newMessage };
    },
    onSuccess: ({ session }) => {
      console.log('🎉 Thread creation successful:', session.id);
      setCurrentSessionId(session.id);
      onThreadCreated(session.id);
      onInitialMessageSent?.();
      queryClient.invalidateQueries({ queryKey: ['chat-threads', leaseId] });
      queryClient.invalidateQueries({ queryKey: ['messages', session.id] });
      setIsWaitingForAssistant(true);
    },
    onError: (error) => {
      console.error('💥 Thread creation failed:', error);
    },
  });

  // Send message to existing thread mutation
  const sendToExistingThreadMutation = useMutation({
    mutationFn: async (content: string) => {
      if (!currentSessionId || !user) {
        throw new Error('No active chat session or user not authenticated');
      }

      const userId = supabaseUser?.id || user.id;

      const { data, error } = await supabase
        .from('messages')
        .insert({
          chat_session_id: currentSessionId,
          lease_id: leaseId,
          user_id: userId,
          sender: 'user',
          content,
          metadata: {}
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', currentSessionId] });
      setIsWaitingForAssistant(true);
    },
    onError: (error) => {
      console.error('💥 Message sending failed:', error);
    },
  });

  const handleSendMessage = async (content: string, isWaitingForAssistant: boolean) => {
    if (isWaitingForAssistant) {
      console.log('⏳ Already waiting for assistant, ignoring new message');
      return;
    }

    if (!currentSessionId) {
      await createThreadMutation.mutateAsync(content);
    } else {
      await sendToExistingThreadMutation.mutateAsync(content);
    }
  };

  const isLoading = createThreadMutation.isPending || sendToExistingThreadMutation.isPending;

  return {
    handleSendMessage,
    isLoading,
    createThreadMutation,
    sendToExistingThreadMutation
  };
};

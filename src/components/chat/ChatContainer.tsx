
import { useEffect, useRef } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ChatMessage } from './ChatMessage';
import { ChatInput } from './ChatInput';
import { MessageSquare } from 'lucide-react';
import { Message } from './types';

interface ChatContainerProps {
  messages: Message[];
  messagesLoading: boolean;
  onSendMessage: (content: string) => void;
  isLoading: boolean;
  isWaitingForAssistant?: boolean;
}

export const ChatContainer = ({ 
  messages, 
  messagesLoading, 
  onSendMessage, 
  isLoading,
  isWaitingForAssistant = false
}: ChatContainerProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className="flex flex-col h-full">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messagesLoading ? (
          <div className="flex items-center justify-center h-32">
            <LoadingSpinner />
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Start a conversation</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              Ask me anything about this lease - I can help you understand terms, obligations, dates, and more.
            </p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
            {isWaitingForAssistant && (
              <div className="flex justify-start">
                <div className="max-w-[80%]">
                  <div className="bg-gray-50 border-gray-200 p-3 rounded-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t p-4 bg-white">
        <ChatInput 
          onSendMessage={onSendMessage}
          isLoading={isLoading || isWaitingForAssistant}
          placeholder="Message AI Assistant..."
        />
      </div>
    </div>
  );
};

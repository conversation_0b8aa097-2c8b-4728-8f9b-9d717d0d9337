
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ChatSession } from './types';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { MessageSquare, MoreHorizontal } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface ChatThreadsListProps {
  leaseId: string;
  currentThreadId: string | null;
  onSelectThread: (threadId: string) => void;
}

export const ChatThreadsList = ({ leaseId, currentThreadId, onSelectThread }: ChatThreadsListProps) => {
  const { user } = useAuth();

  const { data: threads = [], isLoading } = useQuery({
    queryKey: ['chat-threads', leaseId],
    queryFn: async (): Promise<ChatSession[]> => {
      if (!user) {
        console.log('No user authenticated, returning empty threads');
        return [];
      }

      console.log('Fetching chat threads for lease:', leaseId, 'user:', user.id);

      const { data, error } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('lease_id', leaseId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching chat threads:', error);
        throw error;
      }
      
      console.log('Fetched threads:', data);
      return data || [];
    },
    enabled: !!user && !!leaseId,
  });

  if (isLoading) {
    return (
      <div className="p-4 flex justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (threads.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No conversations yet</p>
      </div>
    );
  }

  return (
    <div className="p-2">
      <h3 className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
        Recent
      </h3>
      <div className="space-y-1 mt-2">
        {threads.map((thread) => (
          <div
            key={thread.id}
            className={`group relative rounded-lg p-3 cursor-pointer transition-colors ${
              currentThreadId === thread.id
                ? 'bg-purple-50 border border-purple-200'
                : 'hover:bg-gray-50'
            }`}
            onClick={() => onSelectThread(thread.id)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {thread.title || 'Untitled Conversation'}
                </h4>
                <p className="text-xs text-gray-500 mt-1">
                  {formatDistanceToNow(new Date(thread.created_at), { addSuffix: true })}
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6"
                onClick={(e) => {
                  e.stopPropagation();
                  // TODO: Add thread options menu
                }}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

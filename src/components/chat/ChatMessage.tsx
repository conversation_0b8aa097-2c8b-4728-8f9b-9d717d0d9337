
import { Card } from '@/components/ui/card';
import { format } from 'date-fns';
import ReactMarkdown from 'react-markdown';

interface ChatMessageProps {
  message: {
    id: string;
    sender: 'user' | 'bot' | 'assistant';
    content: string;
    created_at: string;
  };
}

export const ChatMessage = ({ message }: ChatMessageProps) => {
  const isUser = message.sender === 'user';
  const isAssistant = message.sender === 'assistant' || message.sender === 'bot';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className="max-w-[80%]">
        <Card className={`p-3 ${
          isUser 
            ? 'bg-blue-500 text-white' 
            : 'bg-gray-50 border-gray-200'
        }`}>
          <div className="text-sm">
            {isAssistant ? (
              <ReactMarkdown 
                components={{
                  p: ({ children }) => <p className={`mb-2 last:mb-0 prose prose-sm max-w-none ${isUser ? 'prose-invert' : ''}`}>{children}</p>,
                  ul: ({ children }) => <ul className={`mb-2 last:mb-0 pl-4 prose prose-sm max-w-none ${isUser ? 'prose-invert' : ''}`}>{children}</ul>,
                  ol: ({ children }) => <ol className={`mb-2 last:mb-0 pl-4 prose prose-sm max-w-none ${isUser ? 'prose-invert' : ''}`}>{children}</ol>,
                  li: ({ children }) => <li className="mb-1">{children}</li>,
                  code: ({ children, className }) => {
                    const isInline = !className;
                    return isInline ? (
                      <code className={`px-1 py-0.5 rounded text-xs font-mono ${isUser ? 'bg-blue-600 text-blue-100' : 'bg-gray-200 text-gray-800'}`}>
                        {children}
                      </code>
                    ) : (
                      <code className="block bg-gray-800 text-gray-100 p-2 rounded text-xs font-mono overflow-x-auto">
                        {children}
                      </code>
                    );
                  },
                  pre: ({ children }) => <div className="my-2">{children}</div>,
                  strong: ({ children }) => <strong className={isUser ? 'text-white font-bold' : 'text-gray-900 font-bold'}>{children}</strong>,
                  em: ({ children }) => <em className={isUser ? 'text-blue-100' : 'text-gray-700'}>{children}</em>,
                }}
              >
                {message.content}
              </ReactMarkdown>
            ) : (
              <span className="whitespace-pre-wrap">{message.content}</span>
            )}
          </div>
          <p className={`text-xs mt-1 ${
            isUser ? 'text-blue-100' : 'text-gray-500'
          }`}>
            {format(new Date(message.created_at), 'HH:mm')}
          </p>
        </Card>
      </div>
    </div>
  );
};

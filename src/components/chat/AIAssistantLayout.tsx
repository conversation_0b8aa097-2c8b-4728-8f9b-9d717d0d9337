import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Home, MessageSquare, Plus, Menu, X } from 'lucide-react';
import { ChatThreadsList } from './ChatThreadsList';
import { ChatInterface } from './ChatInterface';
import { WelcomeScreen } from './WelcomeScreen';
interface AIAssistantLayoutProps {
  leaseId: string;
}
export const AIAssistantLayout = ({
  leaseId
}: AIAssistantLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);
  const [showWelcome, setShowWelcome] = useState(true);
  const [initialMessage, setInitialMessage] = useState<string | null>(null);
  console.log('🏗️ AIAssistantLayout Debug:', {
    leaseId,
    currentThreadId,
    showWelcome,
    sidebarOpen,
    initialMessage
  });
  const handleNewThread = () => {
    console.log('🆕 Handling new thread');
    setCurrentThreadId(null);
    setShowWelcome(true);
    setInitialMessage(null);
    setSidebarOpen(false);
  };
  const handleSelectThread = (threadId: string) => {
    console.log('🎯 Selecting thread:', threadId);
    setCurrentThreadId(threadId);
    setShowWelcome(false);
    setInitialMessage(null);
    setSidebarOpen(false);
  };
  const handleStartNewChat = (content?: string) => {
    console.log('💬 Starting new chat:', content);
    console.log('💬 Setting showWelcome to false and storing initial message');
    setShowWelcome(false);
    setInitialMessage(content || null);
    console.log('💬 handleStartNewChat completed');
  };
  const handleThreadCreated = (threadId: string) => {
    console.log('✨ Thread created:', threadId);
    setCurrentThreadId(threadId);
    setInitialMessage(null); // Clear initial message after thread is created
  };
  const handleInitialMessageSent = () => {
    console.log('📤 Initial message sent, clearing from state');
    setInitialMessage(null);
  };
  return <div className="flex h-full bg-white">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-12'} transition-all duration-300 border-r border-gray-200 flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(!sidebarOpen)} className="flex-shrink-0">
              {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <div className="p-2 space-y-1">
          <Button onClick={handleNewThread} variant="ghost" size={sidebarOpen ? "sm" : "icon"} className={`w-full ${sidebarOpen ? 'justify-start' : 'justify-center'}`}>
            <Plus className="h-4 w-4" />
            {sidebarOpen && <span className="ml-2 text-sm">New thread</span>}
          </Button>
          
          <Button variant="ghost" size={sidebarOpen ? "sm" : "icon"} className={`w-full ${sidebarOpen ? 'justify-start' : 'justify-center'}`}>
            <MessageSquare className="h-4 w-4" />
            {sidebarOpen && <span className="ml-2 text-sm">History</span>}
          </Button>
        </div>

        {/* Chat Threads List */}
        {sidebarOpen && <div className="flex-1 overflow-y-auto">
            <ChatThreadsList leaseId={leaseId} currentThreadId={currentThreadId} onSelectThread={handleSelectThread} />
          </div>}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {showWelcome ? <WelcomeScreen onStartChat={handleStartNewChat} /> : <ChatInterface leaseId={leaseId} threadId={currentThreadId} initialMessage={initialMessage} onThreadCreated={handleThreadCreated} onInitialMessageSent={handleInitialMessageSent} />}
      </div>
    </div>;
};
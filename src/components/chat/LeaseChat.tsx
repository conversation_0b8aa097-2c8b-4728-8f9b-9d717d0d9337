import { AIAssistantLayout } from './AIAssistantLayout';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageSquare } from 'lucide-react';

interface LeaseChatProps {
  leaseId: string;
}

export const LeaseChat = ({ leaseId }: LeaseChatProps) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">AI Assistant</h2>
          <p className="text-gray-600 mt-1">Get intelligent insights and assistance for this lease</p>
        </div>
        <Badge variant="outline" className="text-sm flex items-center gap-1">
          <MessageSquare className="h-3 w-3" />
          Assistant
        </Badge>
      </div>

      <div className="border rounded-lg">
        <Card>
          <CardContent className="p-0">
            <div className="h-[calc(100vh-16rem)]">
              <AIAssistantLayout leaseId={leaseId} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};


import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send } from 'lucide-react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
}

export const ChatInput = ({ onSendMessage, isLoading, placeholder = "Ask me anything about this lease..." }: ChatInputProps) => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🎯 ChatInput handleSubmit called');
    console.log('🎯 Message content:', message);
    console.log('🎯 Message trimmed:', message.trim());
    console.log('🎯 IsLoading:', isLoading);
    console.log('🎯 OnSendMessage function:', typeof onSendMessage);
    
    if (message.trim() && !isLoading) {
      console.log('🚀 Calling onSendMessage with:', message.trim());
      onSendMessage(message.trim());
      setMessage('');
      console.log('✅ Message sent, input cleared');
    } else {
      console.log('❌ Message not sent - conditions not met');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    console.log('⌨️ Key pressed:', e.key, 'Shift:', e.shiftKey);
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      console.log('🎯 Enter pressed, calling handleSubmit');
      handleSubmit(e);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex gap-3">
      <Textarea
        value={message}
        onChange={(e) => {
          console.log('📝 Message changed to:', e.target.value);
          setMessage(e.target.value);
        }}
        onKeyPress={handleKeyPress}
        placeholder={placeholder}
        className="flex-1 min-h-[50px] max-h-[120px] resize-none border border-gray-300 rounded-xl"
        disabled={isLoading}
      />
      <Button 
        type="submit" 
        disabled={!message.trim() || isLoading}
        className="self-end rounded-xl px-4"
        onClick={(e) => {
          console.log('🖱️ Send button clicked');
          // Don't call handleSubmit here as it will be called by form submission
        }}
      >
        <Send className="h-4 w-4" />
      </Button>
    </form>
  );
};

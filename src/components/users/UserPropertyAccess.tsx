
import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { X, Building, Check, User } from 'lucide-react';

interface Lease {
  lease_id: string;
  parties: Array<{ party_name: string; party_type: string }>;
  properties: Array<{ building_address: string; leased_premises_address: string }>;
}

interface UserPropertyAccessProps {
  userId: string;
  leases: Lease[];
  onClose: () => void;
  showAsFullPage?: boolean;
}

export const UserPropertyAccess = ({ userId, leases, onClose, showAsFullPage = false }: UserPropertyAccessProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedLeases, setSelectedLeases] = useState<Set<string>>(new Set());

  const { data: userAccess, isLoading } = useQuery({
    queryKey: ['user-access', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('user_property_access')
        .select('lease_id')
        .eq('user_id', userId);

      if (error) throw error;
      
      const accessSet = new Set(data.map(item => item.lease_id));
      setSelectedLeases(accessSet);
      return accessSet;
    },
    enabled: !!userId,
  });

  const { data: user } = useQuery({
    queryKey: ['user', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('name, email')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
  });

  const updateAccessMutation = useMutation({
    mutationFn: async (leaseIds: string[]) => {
      // First, remove all existing access for this user
      const { error: deleteError } = await supabase
        .from('user_property_access')
        .delete()
        .eq('user_id', userId);

      if (deleteError) throw deleteError;

      // Then, add the new access permissions
      if (leaseIds.length > 0) {
        const currentUser = await supabase.auth.getUser();
        const { error: insertError } = await supabase
          .from('user_property_access')
          .insert(
            leaseIds.map(leaseId => ({
              user_id: userId,
              lease_id: leaseId,
              granted_by: currentUser.data.user?.id
            }))
          );

        if (insertError) throw insertError;
      }
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Property access updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['user-access', userId] });
      if (!showAsFullPage) {
        onClose();
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update property access.',
        variant: 'destructive',
      });
    },
  });

  const handleLeaseToggle = (leaseId: string, checked: boolean) => {
    const newSelected = new Set(selectedLeases);
    if (checked) {
      newSelected.add(leaseId);
    } else {
      newSelected.delete(leaseId);
    }
    setSelectedLeases(newSelected);
  };

  const handleSave = () => {
    updateAccessMutation.mutate(Array.from(selectedLeases));
  };

  const getPropertyAddress = (lease: Lease) => {
    const property = lease.properties?.[0];
    return property?.building_address || property?.leased_premises_address || 'Unknown Property';
  };

  const getTenantName = (lease: Lease) => {
    const tenant = lease.parties?.find(p => p.party_type === 'tenant');
    return tenant?.party_name || 'Unknown Tenant';
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  const CardComponent = showAsFullPage ? 'div' : Card;
  const CardHeaderComponent = showAsFullPage ? 'div' : CardHeader;
  const CardContentComponent = showAsFullPage ? 'div' : CardContent;

  return (
    <CardComponent className={showAsFullPage ? 'space-y-6' : ''}>
      <CardHeaderComponent className={showAsFullPage ? '' : undefined}>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {showAsFullPage ? <User className="h-6 w-6" /> : <Building className="h-5 w-5" />}
            {showAsFullPage ? (
              <div>
                <h2 className="text-2xl font-bold">Property Access for {user?.name}</h2>
                <p className="text-sm text-gray-600 font-normal mt-1">{user?.email}</p>
              </div>
            ) : (
              `Property Access for ${user?.name}`
            )}
          </CardTitle>
          {!showAsFullPage && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        {!showAsFullPage && (
          <p className="text-sm text-gray-600">
            Select which properties this user can access
          </p>
        )}
      </CardHeaderComponent>
      
      <CardContentComponent className={showAsFullPage ? '' : 'space-y-4'}>
        {showAsFullPage && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Property Access Permissions
              </CardTitle>
              <p className="text-sm text-gray-600">
                Select which properties this user can access
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Building className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-blue-900">Total Properties Available</h3>
                      <p className="text-sm text-blue-700">{leases.length} properties in system</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-white">
                    {selectedLeases.size} selected
                  </Badge>
                </div>
              </div>
              
              <div className="max-h-96 overflow-y-auto space-y-3">
                {leases.map((lease) => (
                  <div key={lease.lease_id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                    <Checkbox
                      id={lease.lease_id}
                      checked={selectedLeases.has(lease.lease_id)}
                      onCheckedChange={(checked) => handleLeaseToggle(lease.lease_id, !!checked)}
                    />
                    <div className="flex-1">
                      <label htmlFor={lease.lease_id} className="cursor-pointer">
                        <div className="font-medium">{getPropertyAddress(lease)}</div>
                        <div className="text-sm text-gray-600">Tenant: {getTenantName(lease)}</div>
                        <div className="text-xs text-gray-500">ID: {lease.lease_id}</div>
                      </label>
                    </div>
                    {selectedLeases.has(lease.lease_id) && (
                      <Check className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                ))}
              </div>

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-gray-600">
                  {selectedLeases.size} of {leases.length} properties selected
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={onClose}>
                    {showAsFullPage ? 'Back' : 'Cancel'}
                  </Button>
                  <Button 
                    onClick={handleSave}
                    disabled={updateAccessMutation.isPending}
                  >
                    {updateAccessMutation.isPending ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        
        {!showAsFullPage && (
          <>
            <div className="max-h-96 overflow-y-auto space-y-3">
              {leases.map((lease) => (
                <div key={lease.lease_id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <Checkbox
                    id={lease.lease_id}
                    checked={selectedLeases.has(lease.lease_id)}
                    onCheckedChange={(checked) => handleLeaseToggle(lease.lease_id, !!checked)}
                  />
                  <div className="flex-1">
                    <label htmlFor={lease.lease_id} className="cursor-pointer">
                      <div className="font-medium">{getPropertyAddress(lease)}</div>
                      <div className="text-sm text-gray-600">Tenant: {getTenantName(lease)}</div>
                      <div className="text-xs text-gray-500">ID: {lease.lease_id}</div>
                    </label>
                  </div>
                  {selectedLeases.has(lease.lease_id) && (
                    <Check className="h-4 w-4 text-green-500" />
                  )}
                </div>
              ))}
            </div>

            <div className="flex items-center justify-between pt-4 border-t">
              <div className="text-sm text-gray-600">
                {selectedLeases.size} of {leases.length} properties selected
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleSave}
                  disabled={updateAccessMutation.isPending}
                >
                  {updateAccessMutation.isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContentComponent>
    </CardComponent>
  );
};

import { Loader2 } from 'lucide-react';

interface LoadingFallbackProps {
  message?: string;
}

export const LoadingFallback = ({ message = "Loading..." }: LoadingFallbackProps) => (
  <div className="flex items-center justify-center min-h-[400px] flex-col gap-4">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
    <p className="text-sm text-muted-foreground">{message}</p>
  </div>
);

export const PageLoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen flex-col gap-4">
    <Loader2 className="h-12 w-12 animate-spin text-primary" />
    <p className="text-lg text-muted-foreground">Loading page...</p>
  </div>
);


import { cn } from '@/lib/utils';

interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled';
  className?: string;
}

const statusConfig = {
  active: { bg: 'bg-emerald-100', text: 'text-emerald-800', label: 'Active' },
  inactive: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Inactive' },
  pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
  overdue: { bg: 'bg-red-100', text: 'text-red-800', label: 'Overdue' },
  completed: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Completed' },
  cancelled: { bg: 'bg-gray-100', text: 'text-gray-600', label: 'Cancelled' },
};

export const StatusBadge = ({ status, className }: StatusBadgeProps) => {
  // Handle unknown status values gracefully
  const config = statusConfig[status] || { 
    bg: 'bg-gray-100', 
    text: 'text-gray-800', 
    label: status || 'Unknown' 
  };
  
  console.log('StatusBadge received status:', status, 'config:', config);
  
  return (
    <span
      className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        config.bg,
        config.text,
        className
      )}
    >
      {config.label}
    </span>
  );
};


import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Building } from 'lucide-react';

interface PortfolioMapProps {
  data: any[];
}

export const PortfolioMap = ({ data }: PortfolioMapProps) => {
  const getUniqueProperties = () => {
    if (!data) return [];
    
    const propertyMap = new Map();
    
    data.forEach(lease => {
      const address = lease.properties?.[0]?.building_address || 'Unknown Address';
      const rent = lease.financial_terms?.[0]?.monthly_base_rent || 0;
      const isActive = lease.lease_status?.toLowerCase() === 'active';
      
      if (propertyMap.has(address)) {
        const existing = propertyMap.get(address);
        existing.totalRent += rent;
        existing.totalUnits += 1;
        if (isActive) existing.occupiedUnits += 1;
      } else {
        propertyMap.set(address, {
          address,
          totalRent: rent,
          totalUnits: 1,
          occupiedUnits: isActive ? 1 : 0
        });
      }
    });
    
    return Array.from(propertyMap.values());
  };

  const properties = getUniqueProperties();

  const getOccupancyColor = (rate: number) => {
    if (rate >= 95) return 'bg-green-500';
    if (rate >= 90) return 'bg-yellow-500';
    if (rate >= 80) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-4">
      <div className="text-center text-gray-600 mb-4">
        <Building className="h-8 w-8 mx-auto mb-2" />
        <p className="text-sm">Interactive map visualization would be here</p>
        <p className="text-xs text-gray-500">Properties sized by revenue, colored by occupancy</p>
      </div>
      
      <div className="grid gap-3 max-h-64 overflow-y-auto">
        {properties.map((property, index) => {
          const occupancyRate = (property.occupiedUnits / property.totalUnits) * 100;
          
          return (
            <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="font-medium text-sm">{property.address}</p>
                      <p className="text-xs text-gray-500">
                        {property.totalUnits} units • ${(property.totalRent/1000).toFixed(1)}K/mo
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      className={`text-white ${getOccupancyColor(occupancyRate)}`}
                    >
                      {occupancyRate.toFixed(0)}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

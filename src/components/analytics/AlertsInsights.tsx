
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, TrendingUp, Clock, DollarSign } from 'lucide-react';

interface AlertsInsightsProps {
  data: any[];
}

export const AlertsInsights = ({ data }: AlertsInsightsProps) => {
  const generateInsights = () => {
    if (!data || data.length === 0) return [];

    const insights = [];
    
    // Expiring leases
    const expiringLeases = data.filter(lease => {
      if (!lease.end_date) return false;
      const endDate = new Date(lease.end_date);
      const threeMonthsFromNow = new Date();
      threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);
      return endDate <= threeMonthsFromNow;
    });

    if (expiringLeases.length > 0) {
      const totalRevenue = expiringLeases.reduce((sum, lease) => {
        return sum + (lease.financial_terms?.[0]?.monthly_base_rent || 0);
      }, 0);

      insights.push({
        type: 'warning',
        icon: Clock,
        title: 'Lease Expirations',
        message: `${expiringLeases.length} leases totaling $${(totalRevenue/1000).toFixed(1)}K/mo expire in next 90 days`,
        action: 'Review Renewals',
        priority: 'high'
      });
    }

    // Portfolio performance
    const totalRevenue = data.reduce((sum, lease) => {
      return sum + (lease.financial_terms?.[0]?.monthly_base_rent || 0);
    }, 0);

    insights.push({
      type: 'info',
      icon: TrendingUp,
      title: 'Revenue Growth',
      message: `Portfolio generating $${(totalRevenue/1000).toFixed(1)}K monthly revenue across ${data.length} leases`,
      action: 'View Details',
      priority: 'medium'
    });

    // Occupancy insight
    const activeLeases = data.filter(lease => lease.lease_status?.toLowerCase() === 'active').length;
    const occupancyRate = (activeLeases / data.length) * 100;

    insights.push({
      type: occupancyRate >= 95 ? 'success' : 'warning',
      icon: DollarSign,
      title: 'Occupancy Status',
      message: `Current occupancy at ${occupancyRate.toFixed(1)}% with ${data.length - activeLeases} vacant units`,
      action: 'Marketing Plan',
      priority: occupancyRate >= 95 ? 'low' : 'medium'
    });

    return insights;
  };

  const insights = generateInsights();

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'warning': return 'border-l-orange-500 bg-orange-50';
      case 'success': return 'border-l-green-500 bg-green-50';
      case 'info': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high': return <Badge className="bg-red-500 text-white">High</Badge>;
      case 'medium': return <Badge className="bg-yellow-500 text-white">Medium</Badge>;
      case 'low': return <Badge className="bg-green-500 text-white">Low</Badge>;
      default: return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          <CardTitle>Alerts & Insights</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight, index) => {
          const Icon = insight.icon;
          
          return (
            <div 
              key={index}
              className={`border-l-4 p-4 rounded-r-lg ${getInsightColor(insight.type)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex gap-3">
                  <Icon className="h-5 w-5 mt-0.5 text-gray-600" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{insight.title}</h4>
                      {getPriorityBadge(insight.priority)}
                    </div>
                    <p className="text-sm text-gray-700">{insight.message}</p>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  {insight.action}
                </Button>
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

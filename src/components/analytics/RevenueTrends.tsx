
import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface RevenueTrendsProps {
  data: any[];
}

export const RevenueTrends = ({ data }: RevenueTrendsProps) => {
  // Generate mock trend data based on actual portfolio data
  const generateTrendData = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentRevenue = data?.reduce((sum, lease) => {
      return sum + (lease.financial_terms?.[0]?.monthly_base_rent || 0);
    }, 0) || 0;

    return months.map((month, index) => {
      const variance = (Math.random() - 0.5) * 0.1; // ±5% variance
      const seasonalFactor = 1 + Math.sin(index * Math.PI / 6) * 0.05; // Seasonal pattern
      const growthFactor = 1 + (index * 0.01); // 1% monthly growth
      
      return {
        month,
        revenue: Math.round(currentRevenue * seasonalFactor * growthFactor * (1 + variance)),
        occupancy: Math.min(100, 85 + Math.random() * 15),
        newLeases: Math.floor(Math.random() * 5) + 1,
        terminations: Math.floor(Math.random() * 3)
      };
    });
  };

  const trendData = generateTrendData();

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={trendData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip 
            formatter={(value: any, name: string) => {
              if (name === 'revenue') return [`$${(value/1000).toFixed(0)}K`, 'Revenue'];
              if (name === 'occupancy') return [`${value.toFixed(1)}%`, 'Occupancy'];
              return [value, name];
            }}
          />
          <Line 
            type="monotone" 
            dataKey="revenue" 
            stroke="#3b82f6" 
            strokeWidth={3}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
          />
          <Line 
            type="monotone" 
            dataKey="occupancy" 
            stroke="#10b981" 
            strokeWidth={2}
            dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};


import React from 'react';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';

interface RevenueWaterfallProps {
  data: any[];
}

export const RevenueWaterfall = ({ data }: RevenueWaterfallProps) => {
  const calculateWaterfallData = () => {
    const baseRent = data?.reduce((sum, lease) => {
      return sum + (lease.financial_terms?.[0]?.monthly_base_rent || 0);
    }, 0) || 0;

    const escalations = baseRent * 0.025; // 2.5% escalations
    const camCharges = baseRent * 0.05; // 5% CAM charges
    const vacancies = baseRent * 0.03; // 3% vacancy loss
    const concessions = baseRent * 0.01; // 1% concessions

    let cumulative = 0;
    const waterfallData = [
      { name: 'Base Rent', value: baseRent, cumulative: cumulative += baseRent, type: 'positive' },
      { name: 'Escalations', value: escalations, cumulative: cumulative += escalations, type: 'positive' },
      { name: 'CAM Charges', value: camCharges, cumulative: cumulative += camCharges, type: 'positive' },
      { name: 'Vacancies', value: -vacancies, cumulative: cumulative -= vacancies, type: 'negative' },
      { name: 'Concessions', value: -concessions, cumulative: cumulative -= concessions, type: 'negative' },
      { name: 'Net Revenue', value: cumulative, cumulative: cumulative, type: 'total' }
    ];

    return waterfallData;
  };

  const waterfallData = calculateWaterfallData();

  const getBarColor = (type: string) => {
    switch (type) {
      case 'positive': return '#10b981';
      case 'negative': return '#ef4444';
      case 'total': return '#3b82f6';
      default: return '#6b7280';
    }
  };

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={waterfallData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="name" 
            angle={-45}
            textAnchor="end"
            height={80}
            interval={0}
          />
          <YAxis 
            tickFormatter={(value) => `$${(value/1000).toFixed(0)}K`}
          />
          <Tooltip 
            formatter={(value: any) => [`$${(Math.abs(value)/1000).toFixed(1)}K`, 'Amount']}
          />
          <Bar dataKey="value">
            {waterfallData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getBarColor(entry.type)} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

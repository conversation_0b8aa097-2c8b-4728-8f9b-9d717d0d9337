
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TrendingUp, TrendingDown, DollarSign, Building, Calendar, Percent } from 'lucide-react';

interface HeroMetricsProps {
  data: any[];
}

export const HeroMetrics = ({ data }: HeroMetricsProps) => {
  const calculateMetrics = () => {
    if (!data || data.length === 0) {
      return {
        portfolioValue: 0,
        monthlyRevenue: 0,
        occupancyRate: 0,
        avgLeaseTerm: 0
      };
    }

    const monthlyRevenue = data.reduce((sum, lease) => {
      const rent = lease.financial_terms?.[0]?.monthly_base_rent || 0;
      return sum + rent;
    }, 0);

    const totalUnits = data.length;
    const activeLeases = data.filter(lease => 
      lease.lease_status?.toLowerCase() === 'active'
    ).length;
    
    const occupancyRate = totalUnits > 0 ? (activeLeases / totalUnits) * 100 : 0;
    
    const avgLeaseTerm = data.reduce((sum, lease) => {
      return sum + (lease.lease_term_months || 0);
    }, 0) / data.length / 12; // Convert to years

    const portfolioValue = monthlyRevenue * 12 * 20; // Rough valuation using 20x multiplier

    return {
      portfolioValue,
      monthlyRevenue,
      occupancyRate,
      avgLeaseTerm
    };
  };

  const metrics = calculateMetrics();

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    }
    if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(0)}K`;
    }
    return `$${amount.toFixed(0)}`;
  };

  const kpis = [
    {
      title: 'Portfolio Value',
      value: formatCurrency(metrics.portfolioValue),
      change: '+12% YoY',
      trend: 'up',
      icon: Building,
      color: 'text-blue-600'
    },
    {
      title: 'Monthly Revenue',
      value: formatCurrency(metrics.monthlyRevenue),
      change: '+$12.3K MoM',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      title: 'Occupancy Rate',
      value: `${metrics.occupancyRate.toFixed(1)}%`,
      change: '-0.8% MoM',
      trend: 'down',
      icon: Percent,
      color: 'text-orange-600'
    },
    {
      title: 'Avg Lease Term',
      value: `${metrics.avgLeaseTerm.toFixed(1)} years`,
      change: '+0.3 yr',
      trend: 'up',
      icon: Calendar,
      color: 'text-purple-600'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {kpis.map((kpi, index) => {
        const Icon = kpi.icon;
        const TrendIcon = kpi.trend === 'up' ? TrendingUp : TrendingDown;
        
        return (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <Icon className={`h-8 w-8 ${kpi.color}`} />
                <div className={`flex items-center gap-1 text-sm ${
                  kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  <TrendIcon className="h-4 w-4" />
                  {kpi.change}
                </div>
              </div>
              <div>
                <p className="text-2xl font-bold">{kpi.value}</p>
                <p className="text-sm text-gray-600">{kpi.title}</p>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

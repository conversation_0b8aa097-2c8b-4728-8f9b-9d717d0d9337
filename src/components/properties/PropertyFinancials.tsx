
import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { DollarSign, TrendingUp, Calculator } from 'lucide-react';

interface PropertyFinancialsProps {
  property: any;
}

export const PropertyFinancials = ({ property }: PropertyFinancialsProps) => {
  const annualRent = property.totalRent * 12;
  const rentPerSqFt = property.rental_area_sqft ? property.totalRent / Number(property.rental_area_sqft) : 0;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                <p className="text-2xl font-bold text-emerald-600">
                  CAD {property.totalRent.toLocaleString()}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-emerald-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Annual Revenue</p>
                <p className="text-2xl font-bold">
                  CAD {annualRent.toLocaleString()}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rent per Sq Ft</p>
                <p className="text-2xl font-bold">
                  CAD {rentPerSqFt.toFixed(2)}
                </p>
              </div>
              <Calculator className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Tenant Revenue Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          {property.tenants && property.tenants.length > 0 ? (
            <div className="space-y-3">
              {property.tenants.map((tenant: any) => (
                <div key={tenant.party_id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{tenant.party_name}</p>
                    <p className="text-sm text-gray-600">{tenant.lease_status}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-emerald-600">
                      {tenant.rent_currency} {tenant.monthly_rent.toLocaleString()}/mo
                    </p>
                    <p className="text-sm text-gray-600">
                      {tenant.rent_currency} {(tenant.monthly_rent * 12).toLocaleString()}/yr
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">No tenant revenue data available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

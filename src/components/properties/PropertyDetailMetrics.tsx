
import { Card, CardContent } from '@/components/ui/card';
import { DollarSign, Building, Users, Percent } from 'lucide-react';

interface PropertyDetailMetricsProps {
  property: any;
}

export const PropertyDetailMetrics = ({ property }: PropertyDetailMetricsProps) => {
  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Monthly Rent</p>
              <p className="text-2xl font-bold text-emerald-600">
                CAD {property.totalRent.toLocaleString()}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-emerald-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Area</p>
              <p className="text-2xl font-bold">
                {property.rental_area_sqft ? Number(property.rental_area_sqft).toLocaleString() : 'N/A'} sq ft
              </p>
            </div>
            <Building className="w-8 h-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Occupancy</p>
              <p className="text-2xl font-bold">
                {property.activeTenants}/{property.totalUnits}
              </p>
              <p className="text-xs text-gray-500 mt-1">units</p>
            </div>
            <Users className="w-8 h-8 text-purple-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Occupancy Rate</p>
              <p className="text-2xl font-bold">
                {property.occupancyRate.toFixed(1)}%
              </p>
            </div>
            <Percent className="w-8 h-8 text-orange-600" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

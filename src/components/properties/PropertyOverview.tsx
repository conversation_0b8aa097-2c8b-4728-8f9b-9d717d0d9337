
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, MapPin, Ruler, FileText } from 'lucide-react';

interface PropertyOverviewProps {
  property: any;
}

export const PropertyOverview = ({ property }: PropertyOverviewProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="w-5 h-5" />
            Property Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-3">
            <div>
              <p className="text-sm text-gray-600">Building Address</p>
              <p className="font-medium">{property.building_address || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Leased Premises</p>
              <p className="font-medium">{property.leased_premises_address || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Rental Area</p>
              <p className="font-medium">
                {property.rental_area_sqft ? `${Number(property.rental_area_sqft).toLocaleString()} sq ft` : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Measurement Method</p>
              <p className="font-medium">{property.measurement_method || 'N/A'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Legal Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {property.cadastre_details && (
            <div>
              <p className="text-sm text-gray-600">Cadastre Details</p>
              <p className="font-medium">{property.cadastre_details}</p>
            </div>
          )}
          {property.land_lot_number && (
            <div>
              <p className="text-sm text-gray-600">Lot Number</p>
              <p className="font-medium">{property.land_lot_number}</p>
            </div>
          )}
          {property.plan_reference && (
            <div>
              <p className="text-sm text-gray-600">Plan Reference</p>
              <p className="font-medium">{property.plan_reference}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {property.property_condition && (
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Property Condition
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{property.property_condition}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

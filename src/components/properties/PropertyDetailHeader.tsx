import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, MapPin, Building, Users, ExternalLink } from 'lucide-react';

interface PropertyDetailHeaderProps {
  property: any;
}

export const PropertyDetailHeader = ({ property }: PropertyDetailHeaderProps) => {
  const navigate = useNavigate();

  const getOccupancyBadge = (rate: number) => {
    if (rate >= 90) return <Badge className="bg-green-600">High Occupancy</Badge>;
    if (rate >= 70) return <Badge className="bg-yellow-600">Medium Occupancy</Badge>;
    return <Badge variant="destructive">Low Occupancy</Badge>;
  };

  const images = property.media_urls?.images || [];
  const mainImage = images.length > 0 ? images[0] : null;

  return (
    <div className="space-y-4">
      {/* Navigation and Title */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/properties')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Properties
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {property.property_name || property.building_address?.split(',')[0] || 'Property Details'}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <MapPin className="w-4 h-4 text-gray-500" />
              <p className="text-gray-600">{property.building_address}</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {property.available_spaces?.length > 0 && (
            <Badge className="bg-emerald-600">
              {property.available_spaces.length} Available Suite{property.available_spaces.length !== 1 ? 's' : ''}
            </Badge>
          )}
          {property.property_url && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(property.property_url, '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              View on Brasswater
            </Button>
          )}
        </div>
      </div>

      {/* Property Image */}
      {mainImage && (
        <div className="w-full">
          <div className="relative h-64 md:h-80 lg:h-96 rounded-lg overflow-hidden bg-gray-100">
            <img
              src={mainImage}
              alt={property.property_name || 'Property'}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
            {images.length > 1 && (
              <div className="absolute bottom-4 right-4 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-sm">
                1 of {images.length} photos
              </div>
            )}
          </div>
          {images.length > 1 && (
            <div className="flex gap-2 mt-3 overflow-x-auto">
              {images.slice(1, 5).map((image: string, index: number) => (
                <img
                  key={index}
                  src={image}
                  alt={`Property view ${index + 2}`}
                  className="h-16 w-24 object-cover rounded border flex-shrink-0 cursor-pointer hover:opacity-80"
                  onClick={() => {
                    // Could implement a modal gallery here
                    window.open(image, '_blank');
                  }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              ))}
              {images.length > 5 && (
                <div className="h-16 w-24 bg-gray-200 rounded border flex items-center justify-center text-sm text-gray-600 flex-shrink-0">
                  +{images.length - 4} more
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Property Type and Description */}
      {property.property_description && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-lg mb-2">About This Property</h3>
          <p className="text-gray-700">{property.property_description}</p>
        </div>
      )}
    </div>
  );
};

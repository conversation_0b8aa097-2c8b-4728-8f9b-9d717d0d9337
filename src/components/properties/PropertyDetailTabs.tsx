
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { PropertyOverview } from './PropertyOverview';
import { PropertyTenants } from './PropertyTenants';
import { PropertyFinancials } from './PropertyFinancials';
import { PropertyMaintenance } from './PropertyMaintenance';
import { PropertyDocuments } from './PropertyDocuments';

interface PropertyDetailTabsProps {
  property: any;
}

export const PropertyDetailTabs = ({ property }: PropertyDetailTabsProps) => {
  return (
    <Tabs defaultValue="overview" className="space-y-6">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="tenants">Tenants</TabsTrigger>
        <TabsTrigger value="financials">Financials</TabsTrigger>
        <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        <TabsTrigger value="documents">Documents</TabsTrigger>
      </TabsList>

      <TabsContent value="overview">
        <PropertyOverview property={property} />
      </TabsContent>

      <TabsContent value="tenants">
        <PropertyTenants property={property} />
      </TabsContent>

      <TabsContent value="financials">
        <PropertyFinancials property={property} />
      </TabsContent>

      <TabsContent value="maintenance">
        <PropertyMaintenance property={property} />
      </TabsContent>

      <TabsContent value="documents">
        <PropertyDocuments property={property} />
      </TabsContent>
    </Tabs>
  );
};

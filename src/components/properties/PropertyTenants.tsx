
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, Mail, Phone, DollarSign } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface PropertyTenantsProps {
  property: any;
}

export const PropertyTenants = ({ property }: PropertyTenantsProps) => {
  const navigate = useNavigate();

  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-600">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status || 'Unknown'}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Current Tenants ({property.tenants?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {property.tenants && property.tenants.length > 0 ? (
            <div className="space-y-4">
              {property.tenants.map((tenant: any) => (
                <div key={tenant.party_id} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-lg">{tenant.party_name}</h4>
                      {tenant.representative_name && (
                        <p className="text-sm text-gray-600">
                          Contact: {tenant.representative_name}
                          {tenant.representative_title && ` (${tenant.representative_title})`}
                        </p>
                      )}
                    </div>
                    {getStatusBadge(tenant.lease_status)}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-emerald-600" />
                      <span className="font-medium">
                        {tenant.rent_currency} {tenant.monthly_rent.toLocaleString()}/mo
                      </span>
                    </div>
                    {tenant.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-gray-500" />
                        <span className="text-sm">{tenant.email}</span>
                      </div>
                    )}
                    {tenant.phone_number && (
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-500" />
                        <span className="text-sm">{tenant.phone_number}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => navigate(`/tenants/${tenant.party_id}`)}
                    >
                      View Details
                    </Button>
                    <Button size="sm" variant="outline">
                      Contact
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">No tenants currently assigned to this property.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

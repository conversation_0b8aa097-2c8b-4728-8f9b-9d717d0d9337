
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Download, Eye } from 'lucide-react';

interface PropertyDocumentsProps {
  property: any;
}

export const PropertyDocuments = ({ property }: PropertyDocumentsProps) => {
  // Collect all document attachments from all leases
  const allDocuments = property.leases?.flatMap((lease: any) => 
    lease.document_attachments?.map((doc: any) => ({
      ...doc,
      lease_id: lease.lease_id,
      tenant_name: lease.parties?.find((p: any) => p.party_type === 'tenant')?.party_name
    })) || []
  ) || [];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Property Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          {allDocuments.length > 0 ? (
            <div className="space-y-3">
              {allDocuments.map((doc: any) => (
                <div key={doc.attachment_id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">{doc.attachment_name}</p>
                      <p className="text-sm text-gray-600">{doc.attachment_type}</p>
                      {doc.tenant_name && (
                        <p className="text-sm text-gray-500">Tenant: {doc.tenant_name}</p>
                      )}
                      {doc.attachment_description && (
                        <p className="text-sm text-gray-500">{doc.attachment_description}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-2" />
                      View
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">No documents attached to this property.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

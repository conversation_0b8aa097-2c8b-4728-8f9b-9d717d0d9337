
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, MapPin, Ruler, FileText } from 'lucide-react';

interface TenantPropertyProps {
  lease: any;
}

export const TenantProperty = ({ lease }: TenantPropertyProps) => {
  const property = lease.properties?.[0];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="w-5 h-5" />
            Property Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Building Address</p>
              <p className="font-medium">{property?.building_address || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Leased Premises</p>
              <p className="font-medium">{property?.leased_premises_address || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Rental Area</p>
              <p className="font-medium">{property?.rental_area_sqft ? `${Number(property.rental_area_sqft).toLocaleString()} sq ft` : 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Measurement Method</p>
              <p className="font-medium">{property?.measurement_method || 'N/A'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {property?.cadastre_details && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Legal Description
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Cadastre Details:</span>
              <span className="font-medium">{property.cadastre_details}</span>
            </div>
            {property.land_lot_number && (
              <div className="flex justify-between">
                <span className="text-gray-600">Lot Number:</span>
                <span className="font-medium">{property.land_lot_number}</span>
              </div>
            )}
            {property.plan_reference && (
              <div className="flex justify-between">
                <span className="text-gray-600">Plan Reference:</span>
                <span className="font-medium">{property.plan_reference}</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {property?.property_condition && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Property Condition
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{property.property_condition}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};


import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Building, Users, FileText } from 'lucide-react';
import type { Tenant } from '@/types/tenant';

interface TenantOverviewProps {
  tenant: Tenant & { lease: any };
}

export const TenantOverview = ({ tenant }: TenantOverviewProps) => {
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="w-5 h-5" />
            Lease Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between">
            <span className="text-gray-600">Lease Status:</span>
            <Badge variant={tenant.lease_status === 'active' ? 'default' : 'secondary'}>
              {tenant.lease_status}
            </Badge>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Commencement Date:</span>
            <span className="font-medium">{formatDate(tenant.commencement_date)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">End Date:</span>
            <span className="font-medium">{formatDate(tenant.end_date)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Lease Term:</span>
            <span className="font-medium">{tenant.lease_term_months} months</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Tenant Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between">
            <span className="text-gray-600">Legal Name:</span>
            <span className="font-medium">{tenant.party_name}</span>
          </div>
          {tenant.representative_name && (
            <div className="flex justify-between">
              <span className="text-gray-600">Representative:</span>
              <span className="font-medium">{tenant.representative_name}</span>
            </div>
          )}
          {tenant.representative_title && (
            <div className="flex justify-between">
              <span className="text-gray-600">Title:</span>
              <span className="font-medium">{tenant.representative_title}</span>
            </div>
          )}
          <div className="pt-2 border-t">
            <p className="text-sm text-gray-600">Address:</p>
            <p className="font-medium">
              {tenant.address_street}<br />
              {tenant.address_city}, {tenant.address_province} {tenant.address_postal_code}
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Property Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600">Property Address</p>
              <p className="font-medium">{tenant.property_address}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Rental Area</p>
              <p className="font-medium">{tenant.rental_area_sqft.toLocaleString()} sq ft</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Monthly Rent</p>
              <p className="font-medium text-emerald-600">
                {tenant.rent_currency} {tenant.monthly_rent.toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { Tenant } from '@/types/tenant';

export const useTenants = (searchTerm: string, statusFilter: string, sortBy: string) => {
  return useQuery({
    queryKey: ['tenants', searchTerm, statusFilter, sortBy],
    queryFn: async () => {
      const { data: leases, error } = await supabase
        .from('lease_documents')
        .select(`
          lease_id,
          lease_status,
          commencement_date,
          end_date,
          lease_term_months,
          parties!inner(
            party_id,
            party_name,
            address_street,
            address_city,
            address_province,
            address_postal_code,
            representative_name,
            representative_title
          ),
          properties(
            building_address,
            leased_premises_address,
            rental_area_sqft
          ),
          financial_terms(
            monthly_base_rent,
            rent_currency
          )
        `)
        .eq('parties.party_type', 'tenant');

      if (error) throw error;

      const processedTenants: Tenant[] = (leases || []).map(lease => {
        const tenant = lease.parties[0];
        const property = lease.properties?.[0];
        const financialTerms = lease.financial_terms?.[0];
        
        const endDate = lease.end_date ? new Date(lease.end_date) : null;
        const today = new Date();
        const daysUntilExpiry = endDate ? Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)) : 0;

        return {
          party_id: tenant.party_id,
          party_name: tenant.party_name || 'Unknown Tenant',
          address_street: tenant.address_street || '',
          address_city: tenant.address_city || '',
          address_province: tenant.address_province || '',
          address_postal_code: tenant.address_postal_code || '',
          representative_name: tenant.representative_name,
          representative_title: tenant.representative_title,
          lease_id: lease.lease_id,
          lease_status: lease.lease_status || 'unknown',
          commencement_date: lease.commencement_date || '',
          end_date: lease.end_date || '',
          monthly_rent: Number(financialTerms?.monthly_base_rent) || 0,
          rent_currency: financialTerms?.rent_currency || 'CAD',
          property_address: property?.building_address || property?.leased_premises_address || 'Unknown Property',
          rental_area_sqft: Number(property?.rental_area_sqft) || 0,
          days_until_expiry: daysUntilExpiry,
          lease_term_months: lease.lease_term_months || 0
        };
      });

      // Apply filters and sorting
      let filteredTenants = processedTenants;

      if (searchTerm) {
        filteredTenants = filteredTenants.filter(tenant =>
          tenant.party_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tenant.property_address.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tenant.address_city.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      if (statusFilter !== 'all') {
        filteredTenants = filteredTenants.filter(tenant =>
          tenant.lease_status.toLowerCase() === statusFilter.toLowerCase()
        );
      }

      // Sort tenants
      filteredTenants.sort((a, b) => {
        switch (sortBy) {
          case 'name':
            return a.party_name.localeCompare(b.party_name);
          case 'rent':
            return b.monthly_rent - a.monthly_rent;
          case 'expiry':
            return a.days_until_expiry - b.days_until_expiry;
          case 'property':
            return a.property_address.localeCompare(b.property_address);
          default:
            return 0;
        }
      });

      return filteredTenants;
    },
  });
};

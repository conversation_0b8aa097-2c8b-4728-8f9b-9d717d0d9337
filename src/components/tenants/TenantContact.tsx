
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mail, Phone, MapPin, User } from 'lucide-react';
import type { Tenant } from '@/types/tenant';

interface TenantContactProps {
  tenant: Tenant;
}

export const TenantContact = ({ tenant }: TenantContactProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Primary Contact
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm text-gray-600">Name</p>
            <p className="font-medium text-lg">{tenant.representative_name || tenant.party_name}</p>
            {tenant.representative_title && (
              <p className="text-sm text-gray-500">{tenant.representative_title}</p>
            )}
          </div>

          <div className="space-y-3">
            {tenant.email && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <span>{tenant.email}</span>
                </div>
                <Button size="sm" variant="outline">
                  Send Email
                </Button>
              </div>
            )}

            {tenant.phone_number && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <span>{tenant.phone_number}</span>
                </div>
                <Button size="sm" variant="outline">
                  Call
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Mailing Address
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p className="font-medium">{tenant.party_name}</p>
            {tenant.address_street && <p>{tenant.address_street}</p>}
            <p>
              {tenant.address_city}, {tenant.address_province} {tenant.address_postal_code}
            </p>
          </div>
          <Button className="mt-4" variant="outline" size="sm">
            View on Map
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};


import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Mail, Phone, MapPin, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import type { Tenant } from '@/types/tenant';

interface TenantDetailHeaderProps {
  tenant: Tenant;
}

export const TenantDetailHeader = ({ tenant }: TenantDetailHeaderProps) => {
  const navigate = useNavigate();

  const getStatusBadge = (status: string, daysUntilExpiry: number) => {
    const statusLower = status.toLowerCase();
    
    if (daysUntilExpiry < 0) {
      return <Badge variant="destructive" className="flex items-center gap-1"><AlertCircle className="w-3 h-3" />Expired</Badge>;
    }
    if (daysUntilExpiry < 90) {
      return <Badge variant="outline" className="border-yellow-500 text-yellow-700 flex items-center gap-1"><Clock className="w-3 h-3" />Expiring Soon</Badge>;
    }
    if (statusLower === 'active') {
      return <Badge variant="default" className="bg-green-600 flex items-center gap-1"><CheckCircle className="w-3 h-3" />Active</Badge>;
    }
    
    return <Badge variant="secondary">{status}</Badge>;
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/tenants')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Tenants
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{tenant.party_name}</h1>
          <div className="flex items-center gap-2 mt-1">
            <MapPin className="w-4 h-4 text-gray-500" />
            <p className="text-gray-600">{tenant.property_address}</p>
          </div>
        </div>
      </div>
      
      <div className="flex items-center gap-3">
        {getStatusBadge(tenant.lease_status, tenant.days_until_expiry)}
        <div className="flex gap-2">
          {tenant.email && (
            <Button size="sm" variant="outline" className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              Contact
            </Button>
          )}
          {tenant.phone_number && (
            <Button size="sm" variant="outline" className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              Call
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

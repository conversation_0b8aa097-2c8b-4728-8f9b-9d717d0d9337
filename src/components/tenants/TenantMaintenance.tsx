
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Wrench, AlertTriangle, CheckCircle } from 'lucide-react';

interface TenantMaintenanceProps {
  lease: any;
}

export const TenantMaintenance = ({ lease }: TenantMaintenanceProps) => {
  const maintenanceObligations = lease.maintenance_obligations || [];

  const getResponsibilityBadge = (party: string) => {
    switch (party?.toLowerCase()) {
      case 'tenant':
        return <Badge variant="outline" className="border-blue-500 text-blue-700">Tenant</Badge>;
      case 'landlord':
        return <Badge variant="outline" className="border-green-500 text-green-700">Landlord</Badge>;
      case 'shared':
        return <Badge variant="outline" className="border-orange-500 text-orange-700">Shared</Badge>;
      default:
        return <Badge variant="secondary">{party || 'Not specified'}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="w-5 h-5" />
            Maintenance Responsibilities
          </CardTitle>
        </CardHeader>
        <CardContent>
          {maintenanceObligations.length > 0 ? (
            <div className="space-y-4">
              {maintenanceObligations.map((obligation: any) => (
                <div key={obligation.maintenance_id} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{obligation.maintenance_type}</h4>
                      {getResponsibilityBadge(obligation.responsible_party)}
                    </div>
                  </div>
                  
                  {obligation.maintenance_description && (
                    <p className="text-gray-700 mb-3">{obligation.maintenance_description}</p>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    {obligation.cost_responsibility && (
                      <div>
                        <span className="font-medium text-gray-600">Cost Responsibility:</span>
                        <p className="text-gray-700">{obligation.cost_responsibility}</p>
                      </div>
                    )}
                    {obligation.notification_requirements && (
                      <div>
                        <span className="font-medium text-gray-600">Notification:</span>
                        <p className="text-gray-700">{obligation.notification_requirements}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">No specific maintenance obligations defined for this lease.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

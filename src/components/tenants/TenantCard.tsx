
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  DollarSign,
  Building,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import type { Tenant } from '@/types/tenant';

interface TenantCardProps {
  tenant: Tenant;
}

export const TenantCard = ({ tenant }: TenantCardProps) => {
  const navigate = useNavigate();

  const getStatusBadge = (status: string, daysUntilExpiry: number) => {
    const statusLower = status.toLowerCase();
    
    if (daysUntilExpiry < 0) {
      return <Badge variant="destructive" className="flex items-center gap-1"><AlertCircle className="w-3 h-3" />Expired</Badge>;
    }
    if (daysUntilExpiry < 90) {
      return <Badge variant="outline" className="border-yellow-500 text-yellow-700 flex items-center gap-1"><Clock className="w-3 h-3" />Expiring Soon</Badge>;
    }
    if (statusLower === 'active') {
      return <Badge variant="default" className="bg-green-600 flex items-center gap-1"><CheckCircle className="w-3 h-3" />Active</Badge>;
    }
    
    return <Badge variant="secondary">{status}</Badge>;
  };

  return (
    <Card 
      className="hover:shadow-lg transition-all duration-200 cursor-pointer group"
      onClick={() => navigate(`/tenants/${tenant.party_id}`)}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg group-hover:text-emerald-600 transition-colors">
              {tenant.party_name}
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">{tenant.property_address}</p>
          </div>
          {getStatusBadge(tenant.lease_status, tenant.days_until_expiry)}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <DollarSign className="w-4 h-4 text-emerald-600" />
            <span className="font-medium">{tenant.rent_currency} {tenant.monthly_rent.toLocaleString()}/mo</span>
          </div>
          <div className="flex items-center gap-2">
            <Building className="w-4 h-4 text-gray-500" />
            <span>{tenant.rental_area_sqft.toLocaleString()} sq ft</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-start gap-2">
            <MapPin className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-700">
              <p>{tenant.address_street}</p>
              <p>{tenant.address_city}, {tenant.address_province} {tenant.address_postal_code}</p>
            </div>
          </div>
        </div>

        {tenant.representative_name && (
          <div className="pt-2 border-t border-gray-100">
            <p className="text-xs text-gray-600">Contact</p>
            <p className="text-sm font-medium">{tenant.representative_name}</p>
            <p className="text-xs text-gray-600">{tenant.representative_title}</p>
          </div>
        )}

        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <div className="text-xs text-gray-500">
            {tenant.days_until_expiry > 0 ? (
              <span className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {tenant.days_until_expiry} days left
              </span>
            ) : tenant.days_until_expiry < 0 ? (
              <span className="text-red-600 font-medium">
                {Math.abs(tenant.days_until_expiry)} days overdue
              </span>
            ) : (
              <span>Expires today</span>
            )}
          </div>
          <div className="flex gap-1" onClick={(e) => e.stopPropagation()}>
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
              <Mail className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
              <Phone className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
              <FileText className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

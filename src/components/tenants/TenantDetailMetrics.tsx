
import { Card, CardContent } from '@/components/ui/card';
import { DollarSign, Building, Calendar, FileText } from 'lucide-react';
import type { Tenant } from '@/types/tenant';

interface TenantDetailMetricsProps {
  tenant: Tenant;
}

export const TenantDetailMetrics = ({ tenant }: TenantDetailMetricsProps) => {
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Rent</p>
              <p className="text-2xl font-bold text-emerald-600">
                {tenant.rent_currency} {tenant.monthly_rent.toLocaleString()}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-emerald-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rental Area</p>
              <p className="text-2xl font-bold">
                {tenant.rental_area_sqft.toLocaleString()} sq ft
              </p>
            </div>
            <Building className="w-8 h-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Lease Expires</p>
              <p className="text-2xl font-bold">
                {formatDate(tenant.end_date)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {tenant.days_until_expiry > 0 ? `${tenant.days_until_expiry} days left` : 
                 tenant.days_until_expiry < 0 ? `${Math.abs(tenant.days_until_expiry)} days overdue` : 'Expires today'}
              </p>
            </div>
            <Calendar className="w-8 h-8 text-yellow-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Lease Term</p>
              <p className="text-2xl font-bold">
                {tenant.lease_term_months} months
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Started {formatDate(tenant.commencement_date)}
              </p>
            </div>
            <FileText className="w-8 h-8 text-purple-600" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

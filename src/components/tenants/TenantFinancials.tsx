
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, CreditCard, Receipt, TrendingUp } from 'lucide-react';

interface TenantFinancialsProps {
  lease: any;
}

export const TenantFinancials = ({ lease }: TenantFinancialsProps) => {
  const financialTerms = lease.financial_terms?.[0];
  const securityDeposit = lease.security_deposits?.[0];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Rent</p>
                <p className="text-2xl font-bold text-emerald-600">
                  {financialTerms?.rent_currency || 'CAD'} {Number(financialTerms?.monthly_base_rent || 0).toLocaleString()}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-emerald-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Annual Rent</p>
                <p className="text-2xl font-bold">
                  {financialTerms?.rent_currency || 'CAD'} {Number(financialTerms?.annual_base_rent || 0).toLocaleString()}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Security Deposit</p>
                <p className="text-2xl font-bold">
                  {financialTerms?.rent_currency || 'CAD'} {Number(securityDeposit?.security_deposit_total || 0).toLocaleString()}
                </p>
              </div>
              <CreditCard className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Per Sq Ft</p>
                <p className="text-2xl font-bold">
                  {financialTerms?.rent_currency || 'CAD'} {Number(financialTerms?.annual_rent_per_sqft || 0).toFixed(2)}
                </p>
              </div>
              <Receipt className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Payment Terms</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Payment Frequency:</span>
              <span className="font-medium">{financialTerms?.rent_payment_frequency || 'Monthly'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Due Day:</span>
              <span className="font-medium">{financialTerms?.rent_due_day || 1} of each month</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Late Fee:</span>
              <span className="font-medium">
                {financialTerms?.late_payment_fee ? 
                  `${financialTerms.rent_currency} ${Number(financialTerms.late_payment_fee).toLocaleString()}` : 
                  'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Late Threshold:</span>
              <span className="font-medium">{financialTerms?.late_payment_threshold || 0} days</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tax Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">GST Rate:</span>
              <span className="font-medium">{financialTerms?.gst_rate || 0}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">QST Rate:</span>
              <span className="font-medium">{financialTerms?.qst_rate || 0}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Operating Expenses (est.):</span>
              <span className="font-medium">
                {financialTerms?.estimated_operating_expenses_per_sqft ? 
                  `${financialTerms.rent_currency} ${Number(financialTerms.estimated_operating_expenses_per_sqft).toFixed(2)}/sq ft` : 
                  'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Property Taxes (est.):</span>
              <span className="font-medium">
                {financialTerms?.estimated_taxes_per_sqft ? 
                  `${financialTerms.rent_currency} ${Number(financialTerms.estimated_taxes_per_sqft).toFixed(2)}/sq ft` : 
                  'N/A'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};


import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TenantOverview } from './TenantOverview';
import { TenantContact } from './TenantContact';
import { TenantFinancials } from './TenantFinancials';
import { TenantProperty } from './TenantProperty';
import { TenantDocuments } from './TenantDocuments';
import { TenantMaintenance } from './TenantMaintenance';
import type { Tenant } from '@/types/tenant';

interface TenantDetailTabsProps {
  tenant: Tenant & { lease: any };
}

export const TenantDetailTabs = ({ tenant }: TenantDetailTabsProps) => {
  return (
    <Tabs defaultValue="overview" className="space-y-6">
      <TabsList className="grid w-full grid-cols-6">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="contact">Contact</TabsTrigger>
        <TabsTrigger value="financials">Financials</TabsTrigger>
        <TabsTrigger value="property">Property</TabsTrigger>
        <TabsTrigger value="documents">Documents</TabsTrigger>
        <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
      </TabsList>

      <TabsContent value="overview">
        <TenantOverview tenant={tenant} />
      </TabsContent>

      <TabsContent value="contact">
        <TenantContact tenant={tenant} />
      </TabsContent>

      <TabsContent value="financials">
        <TenantFinancials lease={tenant.lease} />
      </TabsContent>

      <TabsContent value="property">
        <TenantProperty lease={tenant.lease} />
      </TabsContent>

      <TabsContent value="documents">
        <TenantDocuments lease={tenant.lease} />
      </TabsContent>

      <TabsContent value="maintenance">
        <TenantMaintenance lease={tenant.lease} />
      </TabsContent>
    </Tabs>
  );
};

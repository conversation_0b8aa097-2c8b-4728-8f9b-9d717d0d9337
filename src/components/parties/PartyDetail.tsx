import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail, Phone, MapPin, Building, User, Shield, Briefcase, CreditCard, FileText } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface Party {
  party_id: string;
  party_name: string;
  party_type: string;
  legal_status: string;
  address_street: string;
  address_city: string;
  address_province: string;
  address_postal_code: string;
  representative_name: string;
  representative_title: string;
  authorization_details: string;
  email: string;
  phone_number: string;
}

interface Guarantor {
  id: string;
  party_id: string;
  guarantee_type: string;
  max_liability_amount: number;
  guarantee_duration_start: string;
  guarantee_duration_end: string;
  conditions: string;
}

interface Broker {
  id: string;
  broker_name: string;
  company_name: string;
  broker_type: string;
  commission_details: string;
}

interface Lender {
  id: string;
  lender_name: string;
  contact_person: string;
  loan_type: string;
  notes: string;
}

interface LeaseRelationship {
  lease_id: string;
  lease_name: string;
  property_address: string;
  role: string;
  status: string;
  start_date: string;
  end_date: string;
}

interface PartyDetailProps {
  party: Party;
  guarantors?: Guarantor[];
  brokers?: Broker[];
  lenders?: Lender[];
  leaseRelationships?: LeaseRelationship[];
}

export const PartyDetail = ({ 
  party, 
  guarantors = [], 
  brokers = [], 
  lenders = [], 
  leaseRelationships = [] 
}: PartyDetailProps) => {
  
  const totalRelations = guarantors.length + brokers.length + lenders.length + leaseRelationships.length;

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{party.party_name}</h1>
          <div className="flex items-center gap-3 mt-2">
            <Badge variant="secondary" className="text-sm">
              {party.party_type}
            </Badge>
            <Badge variant="outline" className="text-sm">
              {party.legal_status}
            </Badge>
          </div>
        </div>
        <Badge variant="outline" className="text-sm">
          {totalRelations} Relationships
        </Badge>
      </div>

      {/* Core Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 rounded-lg bg-blue-50">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium">{party.address_street}</p>
                  <p className="text-gray-600">{party.address_city}, {party.address_province} {party.address_postal_code}</p>
                </div>
              </div>
              
              {party.email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{party.email}</span>
                </div>
              )}
              
              {party.phone_number && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{party.phone_number}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Representative Information */}
        {party.representative_name && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-green-50">
                  <Briefcase className="h-5 w-5 text-green-600" />
                </div>
                Representative
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{party.representative_name}</h3>
                <p className="text-gray-600">{party.representative_title}</p>
              </div>
              
              {party.authorization_details && (
                <div className="p-3 bg-green-50 rounded border border-green-200">
                  <p className="text-sm text-gray-700 font-medium mb-1">Authorization Details:</p>
                  <p className="text-sm text-gray-600">{party.authorization_details}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 rounded-lg bg-purple-50">
                <FileText className="h-5 w-5 text-purple-600" />
              </div>
              Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-blue-600">{leaseRelationships.length}</p>
                <p className="text-xs text-gray-600">Active Leases</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600">{guarantors.length}</p>
                <p className="text-xs text-gray-600">Guarantees</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-orange-600">{brokers.length}</p>
                <p className="text-xs text-gray-600">Broker Relations</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-600">{lenders.length}</p>
                <p className="text-xs text-gray-600">Lender Relations</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Relationships */}
      <Accordion type="multiple" className="space-y-4">
        {/* Lease Relationships */}
        {leaseRelationships.length > 0 && (
          <AccordionItem value="leases" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-blue-50">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Lease Relationships</h3>
                    <p className="text-sm text-gray-600">
                      {leaseRelationships.length} lease{leaseRelationships.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {leaseRelationships.map((lease) => (
                    <div key={lease.lease_id} className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-blue-900">{lease.lease_name || 'Unnamed Lease'}</h4>
                            <p className="text-blue-700">{lease.property_address}</p>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline" className="mb-1">
                              {lease.role}
                            </Badge>
                            <p className="text-xs text-blue-700">{lease.status}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-blue-700">
                          <span>Start: {new Date(lease.start_date).toLocaleDateString()}</span>
                          <span>End: {new Date(lease.end_date).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Guarantors */}
        {guarantors.length > 0 && (
          <AccordionItem value="guarantors" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-green-50">
                    <Shield className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Guarantor Relationships</h3>
                    <p className="text-sm text-gray-600">
                      {guarantors.length} guarantee{guarantors.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {guarantors.map((guarantor) => (
                    <div key={guarantor.id} className="p-6 bg-green-50 border border-green-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-green-900">{guarantor.guarantee_type}</h4>
                            <p className="text-green-700">Max Liability: ${guarantor.max_liability_amount?.toLocaleString() || 'N/A'}</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-white rounded border border-green-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Duration:</p>
                            <p className="text-sm text-gray-600">
                              {new Date(guarantor.guarantee_duration_start).toLocaleDateString()} - 
                              {new Date(guarantor.guarantee_duration_end).toLocaleDateString()}
                            </p>
                          </div>
                          {guarantor.conditions && (
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Conditions:</p>
                              <p className="text-sm text-gray-600">{guarantor.conditions}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Broker Relationships */}
        {brokers.length > 0 && (
          <AccordionItem value="brokers" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-orange-50">
                    <Briefcase className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Broker Relationships</h3>
                    <p className="text-sm text-gray-600">
                      {brokers.length} broker relationship{brokers.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {brokers.map((broker) => (
                    <div key={broker.id} className="p-6 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-orange-900">{broker.broker_name}</h4>
                            <p className="text-orange-700">{broker.company_name}</p>
                          </div>
                          <Badge variant="outline">
                            {broker.broker_type}
                          </Badge>
                        </div>
                        
                        {broker.commission_details && (
                          <div className="p-4 bg-white rounded border border-orange-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Commission Details:</p>
                            <p className="text-sm text-gray-600">{broker.commission_details}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Lender Relationships */}
        {lenders.length > 0 && (
          <AccordionItem value="lenders" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-purple-50">
                    <CreditCard className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Lender Relationships</h3>
                    <p className="text-sm text-gray-600">
                      {lenders.length} lender relationship{lenders.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {lenders.map((lender) => (
                    <div key={lender.id} className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-purple-900">{lender.lender_name}</h4>
                            <p className="text-purple-700">Contact: {lender.contact_person}</p>
                          </div>
                          <Badge variant="outline">
                            {lender.loan_type}
                          </Badge>
                        </div>
                        
                        {lender.notes && (
                          <div className="p-4 bg-white rounded border border-purple-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Notes:</p>
                            <p className="text-sm text-gray-600">{lender.notes}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}
      </Accordion>

      {totalRelations === 0 && (
        <div className="text-center p-8 text-gray-500">
          No relationships found for this party
        </div>
      )}
    </div>
  );
};

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface FinancialData {
  monthly_base_rent: string;
  annual_base_rent: string;
  rent_currency: string;
  rent_due_day: string;
  security_deposit: string;
}

interface LeaseFinancialFormProps {
  data: FinancialData;
  onChange: (field: string, value: string) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export const LeaseFinancialForm = ({ data, onChange, onNext, onPrevious }: LeaseFinancialFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Financial Terms</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="monthly_base_rent">Monthly Base Rent *</Label>
            <Input
              id="monthly_base_rent"
              type="number"
              step="0.01"
              value={data.monthly_base_rent}
              onChange={(e) => onChange('monthly_base_rent', e.target.value)}
              placeholder="5000.00"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="annual_base_rent">Annual Base Rent</Label>
            <Input
              id="annual_base_rent"
              type="number"
              step="0.01"
              value={data.annual_base_rent}
              onChange={(e) => onChange('annual_base_rent', e.target.value)}
              placeholder="60000.00"
              readOnly
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="rent_currency">Currency</Label>
            <Select value={data.rent_currency} onValueChange={(value) => onChange('rent_currency', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CAD">CAD</SelectItem>
                <SelectItem value="USD">USD</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="rent_due_day">Rent Due Day</Label>
            <Select value={data.rent_due_day} onValueChange={(value) => onChange('rent_due_day', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 28 }, (_, i) => i + 1).map(day => (
                  <SelectItem key={day} value={day.toString()}>{day}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="security_deposit">Security Deposit</Label>
            <Input
              id="security_deposit"
              type="number"
              step="0.01"
              value={data.security_deposit}
              onChange={(e) => onChange('security_deposit', e.target.value)}
              placeholder="10000.00"
            />
          </div>
        </div>

        <div className="flex justify-between">
          <Button variant="outline" onClick={onPrevious}>
            Previous
          </Button>
          <Button onClick={onNext}>
            Next: Lease Terms
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

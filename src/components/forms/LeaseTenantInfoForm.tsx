import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface TenantData {
  tenant_name: string;
  tenant_type: string;
  tenant_address: string;
  tenant_phone: string;
  tenant_email: string;
}

interface LeaseTenantInfoFormProps {
  data: TenantData;
  onChange: (field: string, value: string) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export const LeaseTenantInfoForm = ({ data, onChange, onNext, onPrevious }: LeaseTenantInfoFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Tenant Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="tenant_name">Tenant Name *</Label>
            <Input
              id="tenant_name"
              value={data.tenant_name}
              onChange={(e) => onChange('tenant_name', e.target.value)}
              placeholder="GESTION MARCAN INC."
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="tenant_type">Tenant Type</Label>
            <Select value={data.tenant_type} onValueChange={(value) => onChange('tenant_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select tenant type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="corporation">Corporation</SelectItem>
                <SelectItem value="individual">Individual</SelectItem>
                <SelectItem value="partnership">Partnership</SelectItem>
                <SelectItem value="trust">Trust</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="tenant_address">Tenant Address</Label>
          <Input
            id="tenant_address"
            value={data.tenant_address}
            onChange={(e) => onChange('tenant_address', e.target.value)}
            placeholder="123 Main Street, Montreal, QC"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="tenant_phone">Phone Number</Label>
            <Input
              id="tenant_phone"
              value={data.tenant_phone}
              onChange={(e) => onChange('tenant_phone', e.target.value)}
              placeholder="(*************"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="tenant_email">Email Address</Label>
            <Input
              id="tenant_email"
              type="email"
              value={data.tenant_email}
              onChange={(e) => onChange('tenant_email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div className="flex justify-between">
          <Button variant="outline" onClick={onPrevious}>
            Previous
          </Button>
          <Button onClick={onNext}>
            Next: Financial Terms
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

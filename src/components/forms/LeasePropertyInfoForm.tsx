import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface PropertyData {
  building_address: string;
  leased_premises_address: string;
  rental_area_sqft: string;
  property_description: string;
}

interface LeasePropertyInfoFormProps {
  data: PropertyData;
  onChange: (field: string, value: string) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export const LeasePropertyInfoForm = ({ data, onChange, onNext, onPrevious }: LeasePropertyInfoFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Property Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="building_address">Building Address *</Label>
          <Input
            id="building_address"
            value={data.building_address}
            onChange={(e) => onChange('building_address', e.target.value)}
            placeholder="2550 Daniel-Johnson Blvd"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="leased_premises_address">Leased Premises Address</Label>
          <Input
            id="leased_premises_address"
            value={data.leased_premises_address}
            onChange={(e) => onChange('leased_premises_address', e.target.value)}
            placeholder="Suite 350 & 375"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="rental_area_sqft">Rental Area (sq ft) *</Label>
            <Input
              id="rental_area_sqft"
              type="number"
              value={data.rental_area_sqft}
              onChange={(e) => onChange('rental_area_sqft', e.target.value)}
              placeholder="2500"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="property_description">Property Description</Label>
          <Textarea
            id="property_description"
            value={data.property_description}
            onChange={(e) => onChange('property_description', e.target.value)}
            placeholder="Additional property details..."
            rows={3}
          />
        </div>

        <div className="flex justify-between">
          <Button variant="outline" onClick={onPrevious}>
            Previous
          </Button>
          <Button onClick={onNext}>
            Next: Tenant Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Save } from 'lucide-react';

interface TermsData {
  renewal_terms: string;
  termination_notice: string;
  use_restrictions: string;
  maintenance_responsibilities: string;
  additional_terms: string;
  notes: string;
}

interface LeaseTermsFormProps {
  data: TermsData;
  onChange: (field: string, value: string) => void;
  onSubmit: () => void;
  onSaveDraft?: () => void;
  onPrevious: () => void;
  isSubmitting: boolean;
  showSaveDraft?: boolean;
  submitLabel?: string;
}

export const LeaseTermsForm = ({ 
  data, 
  onChange, 
  onSubmit, 
  onSaveDraft, 
  onPrevious, 
  isSubmitting, 
  showSaveDraft = false,
  submitLabel = 'Create Lease'
}: LeaseTermsFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Lease Terms & Conditions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="renewal_terms">Renewal Terms</Label>
          <Textarea
            id="renewal_terms"
            value={data.renewal_terms}
            onChange={(e) => onChange('renewal_terms', e.target.value)}
            placeholder="Terms for lease renewal..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="termination_notice">Termination Notice</Label>
          <Textarea
            id="termination_notice"
            value={data.termination_notice}
            onChange={(e) => onChange('termination_notice', e.target.value)}
            placeholder="Notice requirements for termination..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="use_restrictions">Use Restrictions</Label>
          <Textarea
            id="use_restrictions"
            value={data.use_restrictions}
            onChange={(e) => onChange('use_restrictions', e.target.value)}
            placeholder="Permitted uses and restrictions..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="maintenance_responsibilities">Maintenance Responsibilities</Label>
          <Textarea
            id="maintenance_responsibilities"
            value={data.maintenance_responsibilities}
            onChange={(e) => onChange('maintenance_responsibilities', e.target.value)}
            placeholder="Who is responsible for what maintenance..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="additional_terms">Additional Terms</Label>
          <Textarea
            id="additional_terms"
            value={data.additional_terms}
            onChange={(e) => onChange('additional_terms', e.target.value)}
            placeholder="Any additional terms and conditions..."
            rows={4}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="notes">Internal Notes</Label>
          <Textarea
            id="notes"
            value={data.notes}
            onChange={(e) => onChange('notes', e.target.value)}
            placeholder="Internal notes (not included in lease document)..."
            rows={3}
          />
        </div>

        <div className="flex justify-between">
          <Button variant="outline" onClick={onPrevious}>
            Previous
          </Button>
          <div className="flex gap-3">
            {showSaveDraft && onSaveDraft && (
              <Button variant="outline" onClick={onSaveDraft} disabled={isSubmitting}>
                <Save className="w-4 h-4 mr-2" />
                Save as Draft
              </Button>
            )}
            <Button onClick={onSubmit} disabled={isSubmitting}>
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Processing...' : submitLabel}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

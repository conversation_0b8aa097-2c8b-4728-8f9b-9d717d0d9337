import { Button } from '@/components/ui/button';
import { ArrowLeft, Save, FileText } from 'lucide-react';

interface LeaseFormHeaderProps {
  title: string;
  subtitle: string;
  onBack: () => void;
  onSave?: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  showSaveDraft?: boolean;
  submitLabel?: string;
}

export const LeaseFormHeader = ({ 
  title, 
  subtitle, 
  onBack, 
  onSave, 
  onSubmit, 
  isSubmitting, 
  showSaveDraft = false,
  submitLabel = 'Create Lease'
}: LeaseFormHeaderProps) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Leases
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{title}</h1>
          <p className="text-gray-600 mt-1">{subtitle}</p>
        </div>
      </div>
      <div className="flex gap-3">
        {showSaveDraft && onSave && (
          <Button variant="outline" onClick={onSave} disabled={isSubmitting}>
            <Save className="w-4 h-4 mr-2" />
            Save as Draft
          </Button>
        )}
        <Button onClick={onSubmit} disabled={isSubmitting}>
          <FileText className="w-4 h-4 mr-2" />
          {isSubmitting ? 'Processing...' : submitLabel}
        </Button>
      </div>
    </div>
  );
};

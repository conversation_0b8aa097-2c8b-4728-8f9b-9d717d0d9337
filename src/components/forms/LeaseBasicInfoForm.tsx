import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface LeaseData {
  lease_title: string;
  lease_type: string;
  commencement_date: string;
  end_date: string;
  lease_term_months: string;
  lease_status: string;
}

interface LeaseBasicInfoFormProps {
  data: LeaseData;
  onChange: (field: string, value: string) => void;
  onNext: () => void;
  showStatus?: boolean;
}

export const LeaseBasicInfoForm = ({ data, onChange, onNext, showStatus = false }: LeaseBasicInfoFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Lease Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="lease_title">Lease Title *</Label>
            <Input
              id="lease_title"
              value={data.lease_title}
              onChange={(e) => onChange('lease_title', e.target.value)}
              placeholder="e.g., Office Lease - Suite 350"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lease_type">Lease Type</Label>
            <Select value={data.lease_type} onValueChange={(value) => onChange('lease_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select lease type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="commercial">Commercial</SelectItem>
                <SelectItem value="office">Office</SelectItem>
                <SelectItem value="retail">Retail</SelectItem>
                <SelectItem value="industrial">Industrial</SelectItem>
                <SelectItem value="residential">Residential</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className={`grid grid-cols-1 md:grid-cols-${showStatus ? '4' : '3'} gap-4`}>
          <div className="space-y-2">
            <Label htmlFor="commencement_date">Commencement Date *</Label>
            <Input
              id="commencement_date"
              type="date"
              value={data.commencement_date}
              onChange={(e) => onChange('commencement_date', e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="end_date">End Date *</Label>
            <Input
              id="end_date"
              type="date"
              value={data.end_date}
              onChange={(e) => onChange('end_date', e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lease_term_months">Term (Months)</Label>
            <Input
              id="lease_term_months"
              type="number"
              value={data.lease_term_months}
              onChange={(e) => onChange('lease_term_months', e.target.value)}
              placeholder="12"
            />
          </div>
          {showStatus && (
            <div className="space-y-2">
              <Label htmlFor="lease_status">Status</Label>
              <Select value={data.lease_status} onValueChange={(value) => onChange('lease_status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="pending_signature">Pending Signature</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="terminated">Terminated</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button onClick={onNext}>
            Next: Property Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

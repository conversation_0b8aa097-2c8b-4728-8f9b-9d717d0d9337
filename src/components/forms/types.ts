export interface LeaseFormData {
  // Basic Information
  lease_title: string;
  lease_type: string;
  commencement_date: string;
  end_date: string;
  lease_term_months: string;
  lease_status: string;
  
  // Property Information
  building_address: string;
  leased_premises_address: string;
  rental_area_sqft: string;
  property_description: string;
  
  // Tenant Information
  tenant_name: string;
  tenant_type: string;
  tenant_address: string;
  tenant_phone: string;
  tenant_email: string;
  
  // Financial Terms
  monthly_base_rent: string;
  annual_base_rent: string;
  rent_currency: string;
  rent_due_day: string;
  security_deposit: string;
  
  // Lease Terms
  renewal_terms: string;
  termination_notice: string;
  use_restrictions: string;
  maintenance_responsibilities: string;
  
  // Notes
  additional_terms: string;
  notes: string;
}

export const initialLeaseData: LeaseFormData = {
  // Basic Information
  lease_title: '',
  lease_type: '',
  commencement_date: '',
  end_date: '',
  lease_term_months: '',
  lease_status: 'draft',
  
  // Property Information
  building_address: '',
  leased_premises_address: '',
  rental_area_sqft: '',
  property_description: '',
  
  // Tenant Information
  tenant_name: '',
  tenant_type: 'corporation',
  tenant_address: '',
  tenant_phone: '',
  tenant_email: '',
  
  // Financial Terms
  monthly_base_rent: '',
  annual_base_rent: '',
  rent_currency: 'CAD',
  rent_due_day: '1',
  security_deposit: '',
  
  // Lease Terms
  renewal_terms: '',
  termination_notice: '',
  use_restrictions: '',
  maintenance_responsibilities: '',
  
  // Notes
  additional_terms: '',
  notes: ''
};

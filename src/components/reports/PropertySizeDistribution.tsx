
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { ReportsData } from './useReportsData';

interface PropertySizeDistributionProps {
  data: ReportsData['propertyTypeData'];
}

export const PropertySizeDistribution = ({ data }: PropertySizeDistributionProps) => {
  if (data.length === 0) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Property Size Distribution</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {data.map((item, index) => (
            <div key={index} className="text-center p-4 border rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{item.count}</p>
              <p className="text-sm text-gray-600">{item.type} Properties</p>
              <Badge variant="outline" className="mt-2">
                {item.type === 'Large' ? '> 5,000 sq ft' : 
                 item.type === 'Medium' ? '2,000 - 5,000 sq ft' : 
                 '< 2,000 sq ft'}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

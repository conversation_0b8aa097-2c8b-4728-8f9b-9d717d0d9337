
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import type { ReportsData } from './useReportsData';

interface LeaseStatusChartProps {
  data: ReportsData['statusChartData'];
}

export const LeaseStatusChart = ({ data }: LeaseStatusChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Lease Status Distribution</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ status, count }: { status: string; count: number }) => `${status}: ${count}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};


import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface ReportsData {
  totalLeases: number;
  activeLeases: number;
  totalRevenue: number;
  revenueChartData: Array<{ month: string; revenue: number }>;
  statusChartData: Array<{ status: string; count: number; color: string }>;
  propertyTypeData: Array<{ type: string; count: number }>;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active': return '#10b981';
    case 'expired': return '#ef4444';
    case 'pending': return '#f59e0b';
    default: return '#6b7280';
  }
};

export const useReportsData = () => {
  return useQuery({
    queryKey: ['reports-data'],
    queryFn: async (): Promise<ReportsData> => {
      const { data: leases, error } = await supabase
        .from('lease_documents')
        .select(`
          *,
          parties!inner(party_name, party_type),
          properties(building_address, leased_premises_address, rental_area_sqft),
          financial_terms(monthly_base_rent, rent_currency)
        `);

      if (error) throw error;

      // Generate revenue by month (last 12 months)
      const monthlyRevenue: Record<string, number> = {};
      const today = new Date();
      
      for (let i = 11; i >= 0; i--) {
        const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthKey = date.toISOString().substring(0, 7); // YYYY-MM format
        monthlyRevenue[monthKey] = 0;
      }

      // Calculate revenue for active leases
      leases?.forEach(lease => {
        if (lease.lease_status?.toLowerCase() === 'active') {
          const rent = Number(lease.financial_terms?.[0]?.monthly_base_rent) || 0;
          Object.keys(monthlyRevenue).forEach(month => {
            monthlyRevenue[month] += rent;
          });
        }
      });

      const revenueChartData = Object.entries(monthlyRevenue).map(([month, revenue]) => ({
        month: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short', year: '2-digit' }),
        revenue: revenue
      }));

      // Lease status distribution
      const statusCounts: Record<string, number> = {};
      leases?.forEach(lease => {
        const status = lease.lease_status || 'Unknown';
        statusCounts[status] = (statusCounts[status] || 0) + 1;
      });

      const statusChartData = Object.entries(statusCounts).map(([status, count]) => ({
        status,
        count,
        color: getStatusColor(status)
      }));

      // Property type analysis
      const propertyTypes: Record<string, number> = {};
      leases?.forEach(lease => {
        const property = lease.properties?.[0];
        const area = Number(property?.rental_area_sqft) || 0;
        const type = area > 5000 ? 'Large' : area > 2000 ? 'Medium' : 'Small';
        propertyTypes[type] = (propertyTypes[type] || 0) + 1;
      });

      const propertyTypeData = Object.entries(propertyTypes).map(([type, count]) => ({
        type,
        count
      }));

      const totalRevenue = Object.values(monthlyRevenue).reduce((sum: number, revenue: number) => sum + revenue, 0) / 12;

      return {
        totalLeases: leases?.length || 0,
        activeLeases: leases?.filter(l => l.lease_status?.toLowerCase() === 'active').length || 0,
        totalRevenue,
        revenueChartData,
        statusChartData,
        propertyTypeData
      };
    },
  });
};


import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, ResponsiveContainer } from 'recharts';

interface FinancialData {
  lease_id: string;
  monthly_rent: number;
  due_day: number;
  lease_status: string;
}

interface CollectionChartProps {
  data: FinancialData[];
}

export const CollectionChart = ({ data }: CollectionChartProps) => {
  // Generate collection data by day of month based on real lease data
  const collectionData = Array.from({ length: 31 }, (_, i) => {
    const day = i + 1;
    const activeLeases = data.filter(lease => 
      lease.lease_status?.toLowerCase() === 'active' && lease.due_day === day
    );
    const expected = activeLeases.reduce((sum, lease) => sum + lease.monthly_rent, 0);
    
    return {
      day: day.toString(),
      expected,
      collected: 0, // This would come from actual payment records
      collectionRate: 0
    };
  }).filter(item => item.expected > 0);

  const chartConfig = {
    expected: {
      label: 'Expected',
    },
    collected: {
      label: 'Collected',
    },
  };

  if (collectionData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Collection Performance by Due Date</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-80 text-muted-foreground">
            <p>No active leases with due dates found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Collection Performance by Due Date</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={collectionData}>
              <XAxis 
                dataKey="day" 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value: number, name: string) => [
                  `$${value.toLocaleString()}`,
                  name === 'expected' ? 'Expected' : 'Collected'
                ]}
              />
              <Bar 
                dataKey="expected" 
                fill="#e5e7eb" 
                radius={[2, 2, 0, 0]}
                name="Expected"
              />
              <Bar 
                dataKey="collected" 
                fill="#10b981" 
                radius={[2, 2, 0, 0]}
                name="Collected"
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

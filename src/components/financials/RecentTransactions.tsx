
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowUpRight } from 'lucide-react';

export const RecentTransactions = () => {
  // This would fetch actual payment records from Supabase
  const recentTransactions: any[] = [];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Transactions</CardTitle>
        <Button variant="outline" size="sm">
          <ArrowUpRight className="h-4 w-4 mr-2" />
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-gray-500">
          <p>No transactions found</p>
          <p className="text-sm mt-1">Payment records will appear here once payments are recorded</p>
        </div>
      </CardContent>
    </Card>
  );
};

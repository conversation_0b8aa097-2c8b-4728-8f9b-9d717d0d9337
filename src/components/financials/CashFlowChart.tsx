
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';

interface FinancialData {
  monthly_rent: number;
  lease_status: string;
}

interface CashFlowChartProps {
  data: FinancialData[];
}

export const CashFlowChart = ({ data }: CashFlowChartProps) => {
  const activeLeases = data.filter(lease => lease.lease_status?.toLowerCase() === 'active');
  const totalMonthlyRevenue = activeLeases.reduce((sum, lease) => sum + lease.monthly_rent, 0);
  
  // Generate 12 months of data - this would ideally come from actual payment records
  const months = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];
  
  const cashFlowData = months.map((month) => ({
    month,
    revenue: totalMonthlyRevenue,
    netCashFlow: totalMonthlyRevenue,
  }));

  const chartConfig = {
    revenue: {
      label: 'Revenue',
    },
    netCashFlow: {
      label: 'Net Cash Flow',
    },
  };

  if (totalMonthlyRevenue === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>12-Month Cash Flow Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-80 text-muted-foreground">
            <p>No active leases found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>12-Month Cash Flow Trend</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={cashFlowData}>
              <XAxis 
                dataKey="month" 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value: number, name: string) => [
                  `$${value.toLocaleString()}`,
                  name === 'revenue' ? 'Revenue' : 'Net Cash Flow'
                ]}
              />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#3b82f6" 
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                name="Revenue"
              />
              <Line 
                type="monotone" 
                dataKey="netCashFlow" 
                stroke="#8b5cf6" 
                strokeWidth={3}
                dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                name="Net Cash Flow"
                strokeDasharray="5 5"
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

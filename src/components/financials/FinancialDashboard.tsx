
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FinancialMetrics } from './FinancialMetrics';
import { CollectionChart } from './CollectionChart';
import { RecentTransactions } from './RecentTransactions';
import { FinancialAlerts } from './FinancialAlerts';
import { CashFlowChart } from './CashFlowChart';

interface FinancialData {
  lease_id: string;
  tenant_name: string;
  property_address: string;
  monthly_rent: number;
  currency: string;
  due_day: number;
  lease_status: string;
  commencement_date: string;
  end_date: string;
}

export const FinancialDashboard = () => {
  const { data: financialData, isLoading } = useQuery({
    queryKey: ['financial-dashboard'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('lease_documents')
        .select(`
          lease_id,
          lease_status,
          commencement_date,
          end_date,
          parties!inner(party_name, party_type),
          properties(building_address, leased_premises_address),
          financial_terms(monthly_base_rent, rent_currency, rent_due_day)
        `)
        .eq('parties.party_type', 'tenant');

      if (error) throw error;

      return data?.map(lease => {
        const tenant = lease.parties?.find(p => p.party_type === 'tenant');
        const property = lease.properties?.[0];
        const financials = lease.financial_terms?.[0];

        return {
          lease_id: lease.lease_id,
          tenant_name: tenant?.party_name || 'Unknown Tenant',
          property_address: property?.building_address || property?.leased_premises_address || 'Unknown Address',
          monthly_rent: financials?.monthly_base_rent || 0,
          currency: financials?.rent_currency || 'CAD',
          due_day: financials?.rent_due_day || 1,
          lease_status: lease.lease_status || 'unknown',
          commencement_date: lease.commencement_date || '',
          end_date: lease.end_date || ''
        } as FinancialData;
      }) || [];
    },
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map(i => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Financial Metrics */}
      <FinancialMetrics data={financialData || []} />

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CollectionChart data={financialData || []} />
        <CashFlowChart data={financialData || []} />
      </div>

      {/* Bottom Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentTransactions />
        <FinancialAlerts data={financialData || []} />
      </div>
    </div>
  );
};

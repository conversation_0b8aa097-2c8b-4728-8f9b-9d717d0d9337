
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Clock, Calendar } from 'lucide-react';

interface FinancialData {
  lease_id: string;
  tenant_name: string;
  monthly_rent: number;
  due_day: number;
  lease_status: string;
  end_date: string;
}

interface FinancialAlertsProps {
  data: FinancialData[];
}

export const FinancialAlerts = ({ data }: FinancialAlertsProps) => {
  const today = new Date();
  const currentDay = today.getDate();
  
  // Generate alerts based on real data only
  const alerts = [];
  
  // Due today
  const dueToday = data.filter(lease => 
    lease.lease_status?.toLowerCase() === 'active' && lease.due_day === currentDay
  );
  
  if (dueToday.length > 0) {
    alerts.push({
      id: 'due-today',
      type: 'warning',
      icon: Clock,
      title: 'Payments Due Today',
      description: `${dueToday.length} payments expected today`,
      amount: dueToday.reduce((sum, lease) => sum + lease.monthly_rent, 0),
      action: 'Track Collection'
    });
  }
  
  // Leases expiring soon (within 60 days)
  const expiringLeases = data.filter(lease => {
    if (!lease.end_date) return false;
    const endDate = new Date(lease.end_date);
    const daysUntilExpiry = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry > 0 && daysUntilExpiry <= 60;
  });
  
  if (expiringLeases.length > 0) {
    alerts.push({
      id: 'expiring',
      type: 'info',
      icon: Calendar,
      title: 'Leases Expiring Soon',
      description: `${expiringLeases.length} leases expire within 60 days`,
      amount: expiringLeases.reduce((sum, lease) => sum + lease.monthly_rent, 0),
      action: 'Review Renewals'
    });
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-amber-200 bg-amber-50';
      case 'info':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getBadgeColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-amber-100 text-amber-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getIconColor = (type: string) => {
    switch (type) {
      case 'urgent':
        return 'text-red-600';
      case 'warning':
        return 'text-amber-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Financial Alerts</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>No alerts at this time</p>
              <p className="text-sm">All financial metrics are on track</p>
            </div>
          ) : (
            alerts.map((alert) => (
              <div key={alert.id} className={`p-4 border rounded-lg ${getAlertColor(alert.type)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <alert.icon className={`h-5 w-5 mt-0.5 ${getIconColor(alert.type)}`} />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{alert.title}</h4>
                        <Badge className={`text-xs ${getBadgeColor(alert.type)}`}>
                          {alert.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{alert.description}</p>
                      {alert.amount > 0 && (
                        <p className="text-sm font-medium">
                          Amount: CAD ${alert.amount.toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                  <Button size="sm" variant="outline">
                    {alert.action}
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

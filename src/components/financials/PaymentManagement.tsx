import { useState } from 'react';
import { PaymentsGrid } from './PaymentsGrid';
import { PaymentRecorder } from './PaymentRecorder';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CreditCard, Mail, FileText, Download } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface FinancialData {
  lease_id: string;
  tenant_name: string;
  property_address: string;
  monthly_rent: number;
  currency: string;
  due_day: number;
  lease_status: string;
}

export const PaymentManagement = () => {
  const [showPaymentRecorder, setShowPaymentRecorder] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const { data: leases, isLoading } = useQuery({
    queryKey: ['payment-management-leases'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('lease_documents')
        .select(`
          lease_id,
          lease_status,
          parties!inner(party_name, party_type),
          properties(building_address, leased_premises_address),
          financial_terms(monthly_base_rent, rent_currency, rent_due_day)
        `)
        .eq('parties.party_type', 'tenant');

      if (error) throw error;

      return data?.map(lease => {
        const tenant = lease.parties?.find(p => p.party_type === 'tenant');
        const property = lease.properties?.[0];
        const financials = lease.financial_terms?.[0];

        return {
          lease_id: lease.lease_id,
          tenant_name: tenant?.party_name || 'Unknown Tenant',
          property_address: property?.building_address || property?.leased_premises_address || 'Unknown Address',
          monthly_rent: financials?.monthly_base_rent || 0,
          currency: financials?.rent_currency || 'CAD',
          due_day: financials?.rent_due_day || 1,
          lease_status: lease.lease_status || 'unknown'
        } as FinancialData;
      }) || [];
    },
  });

  const handleSendPaymentReminders = () => {
    const overdueLeases = leases?.filter(lease => {
      const today = new Date();
      const dueDate = new Date(today.getFullYear(), today.getMonth(), lease.due_day);
      return today > dueDate && lease.lease_status === 'active';
    }) || [];

    if (overdueLeases.length === 0) {
      toast.info('No overdue payments found. All tenants are up to date!');
      return;
    }

    // Send reminders to overdue tenants
    overdueLeases.forEach(lease => {
      const emailSubject = `Payment Reminder - Lease ${lease.lease_id}`;
      const emailBody = `Dear ${lease.tenant_name},%0D%0A%0D%0AThis is a friendly reminder that your rent payment of ${lease.currency} ${lease.monthly_rent.toLocaleString()} was due on the ${lease.due_day}${lease.due_day === 1 ? 'st' : lease.due_day === 2 ? 'nd' : lease.due_day === 3 ? 'rd' : 'th'} of this month.%0D%0A%0D%0AProperty: ${lease.property_address}%0D%0ALease ID: ${lease.lease_id}%0D%0A%0D%0APlease submit your payment at your earliest convenience.%0D%0A%0D%0AThank you,%0D%0AProperty Management`;
      
      // In a real implementation, this would send actual emails
      const mailtoUrl = `mailto:<EMAIL>?subject=${emailSubject}&body=${emailBody}`;
      window.open(mailtoUrl, '_blank');
    });

    toast.success(`Payment reminders sent to ${overdueLeases.length} tenant(s)!`);
  };

  const handleGenerateStatements = async () => {
    if (!leases || leases.length === 0) {
      toast.error('No lease data available to generate statements.');
      return;
    }

    try {
      toast.success('Generating financial statements...');
      
      // Create comprehensive statements for all active leases
      const statementsData = leases
        .filter(lease => lease.lease_status === 'active')
        .map(lease => {
          const today = new Date();
          const currentMonth = today.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
          
          return {
            lease_id: lease.lease_id,
            tenant_name: lease.tenant_name,
            property_address: lease.property_address,
            statement_period: currentMonth,
            monthly_rent: lease.monthly_rent,
            currency: lease.currency,
            due_date: `${lease.due_day}${lease.due_day === 1 ? 'st' : lease.due_day === 2 ? 'nd' : lease.due_day === 3 ? 'rd' : 'th'} of each month`,
            generated_date: today.toLocaleDateString()
          };
        });

      // Generate HTML content for statements
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Monthly Financial Statements - ${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .statement { page-break-after: always; margin-bottom: 50px; border: 1px solid #ddd; padding: 20px; }
            .header { background: #f5f5f5; padding: 15px; margin-bottom: 20px; text-align: center; }
            .details { margin: 20px 0; }
            .amount { font-size: 24px; font-weight: bold; color: #2563eb; }
            .footer { margin-top: 30px; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          ${statementsData.map(statement => `
            <div class="statement">
              <div class="header">
                <h1>Monthly Rent Statement</h1>
                <h2>${statement.statement_period}</h2>
              </div>
              
              <div class="details">
                <p><strong>Tenant:</strong> ${statement.tenant_name}</p>
                <p><strong>Property:</strong> ${statement.property_address}</p>
                <p><strong>Lease ID:</strong> ${statement.lease_id}</p>
                <p><strong>Due Date:</strong> ${statement.due_date}</p>
                
                <div style="margin: 30px 0; text-align: center;">
                  <p><strong>Monthly Rent Amount:</strong></p>
                  <div class="amount">${statement.currency} ${statement.monthly_rent.toLocaleString()}</div>
                </div>
              </div>
              
              <div class="footer">
                <p>Statement generated on ${statement.generated_date}</p>
                <p>Please remit payment by the due date to avoid late fees.</p>
              </div>
            </div>
          `).join('')}
        </body>
        </html>
      `;

      // Create and download the statements file
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Financial-Statements-${new Date().toISOString().split('T')[0]}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success(`Financial statements generated for ${statementsData.length} active lease(s)!`);
    } catch (error) {
      toast.error('Failed to generate statements. Please try again.');
    }
  };

  const handleExportPayments = async (format: 'pdf' | 'excel') => {
    if (!leases || leases.length === 0) {
      toast.error('No payment data available to export.');
      return;
    }

    setIsExporting(true);
    try {
      if (format === 'excel') {
        // Generate CSV for Excel
        const csvHeaders = [
          'Lease ID', 'Tenant Name', 'Property Address', 'Monthly Rent', 'Currency', 
          'Due Day', 'Status', 'Last Payment Date', 'Amount Due', 'Days Overdue'
        ];

        const csvData = leases.map(lease => {
          const today = new Date();
          const dueDate = new Date(today.getFullYear(), today.getMonth(), lease.due_day);
          const daysOverdue = today > dueDate ? Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
          
          return [
            lease.lease_id,
            lease.tenant_name,
            lease.property_address,
            lease.monthly_rent,
            lease.currency,
            lease.due_day,
            lease.lease_status,
            'N/A', // Last payment date would come from payment_transactions
            daysOverdue > 0 ? lease.monthly_rent : 0,
            daysOverdue
          ];
        });

        const csvContent = [csvHeaders, ...csvData]
          .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
          .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `Payment-Export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast.success('Payment data exported to Excel/CSV format!');
      } else if (format === 'pdf') {
        // Generate HTML for PDF
        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Payment Report - ${new Date().toLocaleDateString()}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              table { width: 100%; border-collapse: collapse; margin: 20px 0; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f5f5f5; font-weight: bold; }
              .overdue { background-color: #fee; }
              .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>Payment Status Report</h1>
              <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            </div>
            
            <table>
              <thead>
                <tr>
                  <th>Lease ID</th>
                  <th>Tenant</th>
                  <th>Property</th>
                  <th>Monthly Rent</th>
                  <th>Due Day</th>
                  <th>Status</th>
                  <th>Days Overdue</th>
                </tr>
              </thead>
              <tbody>
                ${leases.map(lease => {
                  const today = new Date();
                  const dueDate = new Date(today.getFullYear(), today.getMonth(), lease.due_day);
                  const daysOverdue = today > dueDate ? Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
                  const isOverdue = daysOverdue > 0;
                  
                  return `
                    <tr class="${isOverdue ? 'overdue' : ''}">
                      <td>${lease.lease_id}</td>
                      <td>${lease.tenant_name}</td>
                      <td>${lease.property_address}</td>
                      <td>${lease.currency} ${lease.monthly_rent.toLocaleString()}</td>
                      <td>${lease.due_day}</td>
                      <td>${lease.lease_status}</td>
                      <td>${isOverdue ? daysOverdue : 'Current'}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
            
            <div class="footer">
              <p>Total Leases: ${leases.length} | Active: ${leases.filter(l => l.lease_status === 'active').length}</p>
              <p>Report generated by Brasswater Lease Management System</p>
            </div>
          </body>
          </html>
        `;

        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `Payment-Report-${new Date().toISOString().split('T')[0]}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // Auto-open print dialog
        setTimeout(() => {
          const printWindow = window.open(url, '_blank');
          if (printWindow) {
            printWindow.onload = () => {
              setTimeout(() => printWindow.print(), 500);
            };
          }
        }, 1000);

        toast.success('Payment report generated! Use browser print function to save as PDF.');
      }
    } catch (error) {
      toast.error('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Payment Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button onClick={() => setShowPaymentRecorder(true)}>
              <CreditCard className="h-4 w-4 mr-2" />
              Record Payment
            </Button>
            <Button variant="outline" onClick={handleSendPaymentReminders}>
              <Mail className="h-4 w-4 mr-2" />
              Send Payment Reminders
            </Button>
            <Button variant="outline" onClick={handleGenerateStatements}>
              <FileText className="h-4 w-4 mr-2" />
              Generate Statements
            </Button>
            <Button 
              variant="outline" 
              onClick={() => {
                const format = window.confirm('Choose format:\nOK for PDF\nCancel for Excel') ? 'pdf' : 'excel';
                handleExportPayments(format);
              }}
              disabled={isExporting}
            >
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? 'Exporting...' : 'Export Payments'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payments Grid */}
      <PaymentsGrid leases={leases || []} />

      {/* Payment Recorder Modal */}
      {showPaymentRecorder && (
        <PaymentRecorder 
          isOpen={showPaymentRecorder}
          onClose={() => setShowPaymentRecorder(false)}
          leases={leases || []}
        />
      )}
    </div>
  );
};


import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DollarSign, TrendingUp, Calendar, CreditCard, Users } from 'lucide-react';

interface FinancialData {
  lease_id: string;
  tenant_name: string;
  property_address: string;
  monthly_rent: number;
  currency: string;
  due_day: number;
  lease_status: string;
}

interface FinancialMetricsProps {
  data: FinancialData[];
}

export const FinancialMetrics = ({ data }: FinancialMetricsProps) => {
  const today = new Date();
  const currentDay = today.getDate();
  
  // Calculate metrics from real data
  const activeLeases = data.filter(lease => lease.lease_status?.toLowerCase() === 'active');
  const totalMonthlyRevenue = activeLeases.reduce((sum, lease) => sum + lease.monthly_rent, 0);
  const totalAnnualRevenue = totalMonthlyRevenue * 12;
  
  const dueToday = activeLeases.filter(lease => lease.due_day === currentDay);
  const expectedToday = dueToday.reduce((sum, lease) => sum + lease.monthly_rent, 0);
  
  const averageRent = activeLeases.length > 0 ? totalMonthlyRevenue / activeLeases.length : 0;

  const metrics = [
    {
      title: 'Monthly Revenue',
      value: `CAD ${totalMonthlyRevenue.toLocaleString()}`,
      subtitle: `${activeLeases.length} active leases`,
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100'
    },
    {
      title: 'Annual Revenue',
      value: `CAD ${totalAnnualRevenue.toLocaleString()}`,
      subtitle: 'Projected annual income',
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Expected Today',
      value: `CAD ${expectedToday.toLocaleString()}`,
      subtitle: `${dueToday.length} payments due`,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Collection Rate',
      value: 'N/A',
      subtitle: 'No payment data available',
      icon: CreditCard,
      color: 'text-muted-foreground',
      bgColor: 'bg-muted'
    },
    {
      title: 'Overdue Amount',
      value: 'CAD 0',
      subtitle: 'No payment tracking',
      icon: CreditCard,
      color: 'text-muted-foreground',
      bgColor: 'bg-muted'
    },
    {
      title: 'Average Rent',
      value: `CAD ${Math.round(averageRent).toLocaleString()}`,
      subtitle: 'Per lease monthly',
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {metrics.map((metric, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-muted-foreground mb-1">{metric.title}</p>
                <div className="flex items-center gap-2 mb-2">
                  <p className={`text-2xl font-bold ${metric.color}`}>
                    {metric.value}
                  </p>
                </div>
                <p className="text-xs text-muted-foreground">{metric.subtitle}</p>
              </div>
              <div className={`p-3 rounded-full ${metric.bgColor}`}>
                <metric.icon className={`w-6 h-6 ${metric.color}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

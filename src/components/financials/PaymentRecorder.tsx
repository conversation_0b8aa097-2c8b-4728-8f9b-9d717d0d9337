
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, AlertTriangle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FinancialData {
  lease_id: string;
  tenant_name: string;
  property_address: string;
  monthly_rent: number;
  currency: string;
  due_day: number;
  lease_status: string;
}

interface PaymentRecorderProps {
  isOpen: boolean;
  onClose: () => void;
  leases: FinancialData[];
  preselectedLeaseId?: string;
  prefilledAmount?: number;
}

export const PaymentRecorder = ({ isOpen, onClose, leases, preselectedLeaseId, prefilledAmount }: PaymentRecorderProps) => {
  const [selectedLeaseId, setSelectedLeaseId] = useState<string>('');
  const [paymentAmount, setPaymentAmount] = useState<string>('');
  const [paymentDate, setPaymentDate] = useState<Date>(new Date());
  const [paymentMethod, setPaymentMethod] = useState<string>('');
  const [referenceNumber, setReferenceNumber] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [isPartialPayment, setIsPartialPayment] = useState<boolean>(false);

  // Handle pre-filling when modal opens
  useEffect(() => {
    if (isOpen && preselectedLeaseId) {
      setSelectedLeaseId(preselectedLeaseId);
      
      // Pre-fill amount if provided, otherwise use monthly rent from the lease
      if (prefilledAmount !== undefined) {
        setPaymentAmount(prefilledAmount.toString());
      } else {
        const lease = leases.find(l => l.lease_id === preselectedLeaseId);
        if (lease?.monthly_rent) {
          setPaymentAmount(lease.monthly_rent.toString());
        }
      }
    }
    
    // Reset form when modal closes
    if (!isOpen) {
      setSelectedLeaseId('');
      setPaymentAmount('');
      setPaymentMethod('');
      setReferenceNumber('');
      setNotes('');
      setPaymentDate(new Date());
    }
  }, [isOpen, preselectedLeaseId, prefilledAmount, leases]);

  const selectedLease = leases.find(lease => lease.lease_id === selectedLeaseId);
  const expectedAmount = selectedLease?.monthly_rent || 0;
  const actualAmount = parseFloat(paymentAmount) || 0;
  const variance = actualAmount - expectedAmount;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Here you would save the payment to the database
    console.log('Recording payment:', {
      lease_id: selectedLeaseId,
      amount: actualAmount,
      payment_date: paymentDate,
      payment_method: paymentMethod,
      reference_number: referenceNumber,
      notes,
      is_partial: isPartialPayment
    });

    // Reset form and close
    setSelectedLeaseId('');
    setPaymentAmount('');
    setPaymentMethod('');
    setReferenceNumber('');
    setNotes('');
    onClose();
  };

  const filteredLeases = leases.filter(lease => 
    lease.lease_status?.toLowerCase() === 'active'
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Record Payment</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Lease Selection */}
          <div className="space-y-2">
            <Label htmlFor="lease">Select Lease</Label>
            <Select value={selectedLeaseId} onValueChange={setSelectedLeaseId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a lease..." />
              </SelectTrigger>
              <SelectContent>
                {filteredLeases.map((lease) => (
                  <SelectItem key={lease.lease_id} value={lease.lease_id}>
                    {lease.tenant_name} - {lease.property_address}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Lease Details Card */}
          {selectedLease && (
            <Card className="bg-gray-50">
              <CardContent className="p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Tenant</p>
                    <p className="font-medium">{selectedLease.tenant_name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Property</p>
                    <p className="font-medium">{selectedLease.property_address}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Expected Amount</p>
                    <p className="font-bold text-lg">
                      {selectedLease.currency} {expectedAmount.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Due Day</p>
                    <p className="font-medium">{selectedLease.due_day} of each month</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-2 gap-6">
            {/* Payment Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount">Payment Amount</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={paymentAmount}
                onChange={(e) => {
                  setPaymentAmount(e.target.value);
                  setIsPartialPayment(parseFloat(e.target.value) < expectedAmount);
                }}
                required
              />
              {variance !== 0 && selectedLease && (
                <div className="flex items-center gap-2 text-sm">
                  {variance > 0 ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-green-600">
                        Overpayment: {selectedLease.currency} {Math.abs(variance).toFixed(2)}
                      </span>
                    </>
                  ) : (
                    <>
                      <AlertTriangle className="h-4 w-4 text-orange-600" />
                      <span className="text-orange-600">
                        Underpayment: {selectedLease.currency} {Math.abs(variance).toFixed(2)}
                      </span>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Payment Date */}
            <div className="space-y-2">
              <Label>Payment Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !paymentDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {paymentDate ? format(paymentDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={paymentDate}
                    onSelect={(date) => date && setPaymentDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            {/* Payment Method */}
            <div className="space-y-2">
              <Label htmlFor="method">Payment Method</Label>
              <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select method..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="check">Check</SelectItem>
                  <SelectItem value="ach">ACH/Bank Transfer</SelectItem>
                  <SelectItem value="wire">Wire Transfer</SelectItem>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="credit_card">Credit Card</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Reference Number */}
            <div className="space-y-2">
              <Label htmlFor="reference">Reference Number</Label>
              <Input
                id="reference"
                placeholder="Check #, transaction ID, etc."
                value={referenceNumber}
                onChange={(e) => setReferenceNumber(e.target.value)}
              />
            </div>
          </div>

          {/* Partial Payment Warning */}
          {isPartialPayment && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                  <div>
                    <p className="font-medium text-orange-800">Partial Payment</p>
                    <p className="text-sm text-orange-700">
                      Remaining balance: {selectedLease?.currency} {(expectedAmount - actualAmount).toFixed(2)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any additional notes about this payment..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={!selectedLeaseId || !paymentAmount}>
              Record Payment
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { CreditCard, Mail, Phone, MoreHorizontal, Search } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface FinancialData {
  lease_id: string;
  tenant_name: string;
  property_address: string;
  monthly_rent: number;
  currency: string;
  due_day: number;
  lease_status: string;
}

interface PaymentsGridProps {
  leases: FinancialData[];
}

export const PaymentsGrid = ({ leases }: PaymentsGridProps) => {
  const navigate = useNavigate();
  const [selectedLeases, setSelectedLeases] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'due' | 'overdue' | 'upcoming'>('all');

  const today = new Date();
  const currentDay = today.getDate();

  // Calculate payment status for each lease
  const getPaymentStatus = (lease: FinancialData) => {
    // Mock logic - in real app this would check actual payment records
    const isDueToday = lease.due_day === currentDay;
    const isOverdue = lease.due_day < currentDay && lease.due_day > currentDay - 28; // Within current month
    const isUpcoming = lease.due_day > currentDay && lease.due_day <= currentDay + 7;
    
    if (isDueToday) return { status: 'due', label: 'Due Today', color: 'bg-yellow-100 text-yellow-800' };
    if (isOverdue) return { status: 'overdue', label: 'Overdue', color: 'bg-red-100 text-red-800' };
    if (isUpcoming) return { status: 'upcoming', label: 'Upcoming', color: 'bg-blue-100 text-blue-800' };
    return { status: 'current', label: 'Current', color: 'bg-green-100 text-green-800' };
  };

  // Filter leases based on search and status
  const filteredLeases = leases.filter(lease => {
    const matchesSearch = lease.tenant_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lease.property_address.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (!matchesSearch) return false;
    
    if (statusFilter === 'all') return true;
    
    const paymentStatus = getPaymentStatus(lease);
    return paymentStatus.status === statusFilter;
  });

  const handleSelectLease = (leaseId: string, checked: boolean) => {
    if (checked) {
      setSelectedLeases([...selectedLeases, leaseId]);
    } else {
      setSelectedLeases(selectedLeases.filter(id => id !== leaseId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedLeases(filteredLeases.map(lease => lease.lease_id));
    } else {
      setSelectedLeases([]);
    }
  };

  // Action handlers
  const handleRecordPayment = (lease: FinancialData) => {
    const amount = prompt(`Enter payment amount for ${lease.tenant_name}:`, lease.monthly_rent.toString());
    if (amount && !isNaN(Number(amount))) {
      const paymentData = {
        lease_id: lease.lease_id,
        tenant_name: lease.tenant_name,
        amount: Number(amount),
        date: new Date().toLocaleDateString(),
        property: lease.property_address
      };
      
      toast.success(`Payment of ${lease.currency} ${Number(amount).toLocaleString()} recorded for ${lease.tenant_name}!`);
      console.log('Payment recorded:', paymentData);
    } else if (amount !== null) {
      toast.error('Please enter a valid payment amount.');
    }
  };

  const handleEmailTenant = (lease: FinancialData) => {
    const paymentStatus = getPaymentStatus(lease);
    let subject = '';
    let body = '';
    
    if (paymentStatus.status === 'overdue') {
      subject = `Overdue Payment Notice - Lease ${lease.lease_id}`;
      body = `Dear ${lease.tenant_name},%0D%0A%0D%0AThis is a notice that your rent payment of ${lease.currency} ${lease.monthly_rent.toLocaleString()} is overdue.%0D%0A%0D%0AProperty: ${lease.property_address}%0D%0ALease ID: ${lease.lease_id}%0D%0ADue Date: ${lease.due_day}${lease.due_day === 1 ? 'st' : lease.due_day === 2 ? 'nd' : lease.due_day === 3 ? 'rd' : 'th'} of each month%0D%0A%0D%0APlease submit your payment immediately to avoid additional late fees.%0D%0A%0D%0AThank you,%0D%0AProperty Management`;
    } else if (paymentStatus.status === 'due') {
      subject = `Payment Due Today - Lease ${lease.lease_id}`;
      body = `Dear ${lease.tenant_name},%0D%0A%0D%0AThis is a friendly reminder that your rent payment of ${lease.currency} ${lease.monthly_rent.toLocaleString()} is due today.%0D%0A%0D%0AProperty: ${lease.property_address}%0D%0ALease ID: ${lease.lease_id}%0D%0A%0D%0APlease submit your payment by the end of the day.%0D%0A%0D%0AThank you,%0D%0AProperty Management`;
    } else {
      subject = `Payment Reminder - Lease ${lease.lease_id}`;
      body = `Dear ${lease.tenant_name},%0D%0A%0D%0AThis is a friendly reminder about your upcoming rent payment of ${lease.currency} ${lease.monthly_rent.toLocaleString()}.%0D%0A%0D%0AProperty: ${lease.property_address}%0D%0ALease ID: ${lease.lease_id}%0D%0ADue Date: ${lease.due_day}${lease.due_day === 1 ? 'st' : lease.due_day === 2 ? 'nd' : lease.due_day === 3 ? 'rd' : 'th'} of each month%0D%0A%0D%0AThank you for your prompt attention.%0D%0A%0D%0ABest regards,%0D%0AProperty Management`;
    }

    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    window.open(mailtoUrl, '_blank');
    toast.success(`Email opened for ${lease.tenant_name}`);
  };

  const handleCallTenant = (lease: FinancialData) => {
    // In a real app, this would use the actual tenant phone number
    const phoneNumber = '+1234567890'; // Mock phone number
    const telUrl = `tel:${phoneNumber}`;
    window.open(telUrl, '_blank');
    toast.success(`Calling ${lease.tenant_name} at ${phoneNumber}`);
  };

  const handleViewPaymentHistory = (lease: FinancialData) => {
    // Create mock payment history
    const mockHistory = [
      { date: '2024-01-01', amount: lease.monthly_rent, status: 'Completed' },
      { date: '2024-02-01', amount: lease.monthly_rent, status: 'Completed' },
      { date: '2024-03-01', amount: lease.monthly_rent, status: 'Completed' },
      { date: '2024-04-01', amount: lease.monthly_rent, status: 'Pending' },
    ];

    const historyText = `
PAYMENT HISTORY - ${lease.tenant_name}
Lease ID: ${lease.lease_id}
Property: ${lease.property_address}

Recent Payments:
${mockHistory.map(payment => 
  `• ${payment.date}: ${lease.currency} ${payment.amount.toLocaleString()} - ${payment.status}`
).join('\n')}

Total Payments: ${mockHistory.length}
Total Amount: ${lease.currency} ${(mockHistory.length * lease.monthly_rent).toLocaleString()}
    `;

    alert(historyText);
    toast.success('Payment history displayed');
  };

  const handleViewLeaseDetails = (lease: FinancialData) => {
    navigate(`/leases/${lease.lease_id}`);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Payment Tracking</CardTitle>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search tenants or properties..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={statusFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFilter('all')}
              >
                All
              </Button>
              <Button
                variant={statusFilter === 'due' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFilter('due')}
              >
                Due Today
              </Button>
              <Button
                variant={statusFilter === 'overdue' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFilter('overdue')}
              >
                Overdue
              </Button>
              <Button
                variant={statusFilter === 'upcoming' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFilter('upcoming')}
              >
                Upcoming
              </Button>
            </div>
          </div>
        </div>
        
        {selectedLeases.length > 0 && (
          <div className="flex items-center gap-3 mt-4 p-3 bg-blue-50 rounded-lg">
            <span className="text-sm font-medium">
              {selectedLeases.length} lease{selectedLeases.length !== 1 ? 's' : ''} selected
            </span>
            <Button size="sm" variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              Send Reminders
            </Button>
            <Button size="sm" variant="outline">
              <CreditCard className="h-4 w-4 mr-2" />
              Record Bulk Payment
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedLeases.length === filteredLeases.length && filteredLeases.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Tenant</TableHead>
              <TableHead>Property</TableHead>
              <TableHead>Amount Due</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Days</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLeases.map((lease) => {
              const paymentStatus = getPaymentStatus(lease);
              const daysFromDue = currentDay - lease.due_day;
              
              return (
                <TableRow key={lease.lease_id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedLeases.includes(lease.lease_id)}
                      onCheckedChange={(checked) => handleSelectLease(lease.lease_id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    {lease.tenant_name}
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {lease.property_address}
                  </TableCell>
                  <TableCell className="font-medium">
                    {lease.currency} {lease.monthly_rent.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    {new Date(today.getFullYear(), today.getMonth(), lease.due_day).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary" className={paymentStatus.color}>
                      {paymentStatus.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {daysFromDue > 0 ? (
                      <span className="text-red-600 font-medium">+{daysFromDue}</span>
                    ) : daysFromDue < 0 ? (
                      <span className="text-blue-600">{Math.abs(daysFromDue)}</span>
                    ) : (
                      <span className="text-yellow-600">Today</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => handleRecordPayment(lease)}
                        title="Record Payment"
                      >
                        <CreditCard className="h-4 w-4" />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => handleEmailTenant(lease)}
                        title="Email Tenant"
                      >
                        <Mail className="h-4 w-4" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="outline">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem onClick={() => handleCallTenant(lease)}>
                            <Phone className="h-4 w-4 mr-2" />
                            Call Tenant
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleViewLeaseDetails(lease)}>
                            View Lease Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleViewPaymentHistory(lease)}>
                            Payment History
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        
        {filteredLeases.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              {searchTerm || statusFilter !== 'all' 
                ? 'No payments found matching your criteria.' 
                : 'No payment data available.'}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

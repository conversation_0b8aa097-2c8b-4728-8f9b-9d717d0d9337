
import { useLocation, Link } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

export const Breadcrumbs = () => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  const breadcrumbNameMap: Record<string, string> = {
    dashboard: 'Dashboard',
    properties: 'Properties',
    leases: 'Leases',
    tenants: 'Tenants',
    financials: 'Financials',
    maintenance: 'Maintenance',
    reports: 'Reports',
  };

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600">
      <Link 
        to="/dashboard" 
        className="flex items-center hover:text-emerald-600 transition-colors"
      >
        <Home className="w-4 h-4" />
      </Link>
      
      {pathnames.map((value, index) => {
        const last = index === pathnames.length - 1;
        const to = `/${pathnames.slice(0, index + 1).join('/')}`;
        const displayName = breadcrumbNameMap[value] || value;

        return (
          <div key={to} className="flex items-center space-x-2">
            <ChevronRight className="w-4 h-4 text-gray-400" />
            {last ? (
              <span className="font-medium text-gray-900">{displayName}</span>
            ) : (
              <Link 
                to={to} 
                className="hover:text-emerald-600 transition-colors"
              >
                {displayName}
              </Link>
            )}
          </div>
        );
      })}
    </nav>
  );
};

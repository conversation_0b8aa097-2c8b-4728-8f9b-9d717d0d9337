import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Building, FileText, ChevronLeft, ChevronRight, Wrench, Users, BarChart3, TrendingUp, UserCheck, FolderOpen, MessageSquare, Home } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
const Sidebar = ({
  isCollapsed,
  onToggle,
  isMobile = false
}: {
  isCollapsed: boolean;
  onToggle: () => void;
  isMobile?: boolean;
}) => {
  const location = useLocation();
  const {
    user
  } = useAuth();
  const mainMenuItems = [{
    icon: Home,
    label: 'Overview',
    path: '/dashboard'
  }, {
    icon: Building,
    label: 'Portfolio',
    path: '/properties'
  }, {
    icon: FileText,
    label: 'Leases',
    path: '/leases'
  }, {
    icon: UserCheck,
    label: 'Tenants',
    path: '/tenants'
  }, {
    icon: TrendingUp,
    label: 'Financials',
    path: '/financials',
    roles: ['administrator', 'property_manager']
  }, {
    icon: Wrench,
    label: 'Maintenance',
    path: '/maintenance'
  }, {
    icon: MessageSquare,
    label: 'Communications',
    path: '/communications'
  }, {
    icon: FolderOpen,
    label: 'Documents',
    path: '/documents'
  }];
  const adminMenuItems = user?.role === 'administrator' ? [{
    icon: Users,
    label: 'User Management',
    path: '/users'
  }] : [];

  // Filter menu items based on user role
  const filteredMainMenuItems = mainMenuItems.filter(item => !item.roles || item.roles.includes(user?.role as any));
  return <div className={cn("h-screen bg-sidebar border-r border-sidebar-border transition-all duration-300 ease-in-out flex flex-col", isCollapsed ? "w-16" : "w-64")}>
      {/* Header */}
      <div className="p-4 border-b border-sidebar-border flex items-center justify-between">
        {!isCollapsed && <div className="flex items-center space-x-2">
            <div className="w-8 h-8 flex items-center justify-center p-1">
              <img src="/lovable-uploads/d1576679-7628-47b0-a3b6-63c60a322061.png" alt="Brasswater Logo" className="w-full h-full object-contain" />
            </div>
            <span className="text-sidebar-foreground text-lg font-normal">BRASSWATER</span>
          </div>}
        <button onClick={onToggle} className="p-1.5 rounded-md hover:bg-sidebar-accent text-sidebar-foreground hover:text-sidebar-accent-foreground transition-colors">
          {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </button>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1 p-2">
        <ul className="space-y-1">
          {filteredMainMenuItems.map(item => {
          const isActive = location.pathname === item.path || location.pathname.startsWith(item.path + '/');
          return <li key={item.path}>
                <Link 
                  to={item.path} 
                  onClick={isMobile ? onToggle : undefined}
                  className={cn("flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200", isActive ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-lg" : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground", isCollapsed && "justify-center")}
                >
                  <item.icon className="w-5 h-5 flex-shrink-0" />
                  {!isCollapsed && <span className="font-medium">{item.label}</span>}
                </Link>
              </li>;
        })}
        </ul>
      </nav>

      {/* Admin Menu at Bottom */}
      {adminMenuItems.length > 0 && <div className="p-2 border-t border-sidebar-border">
          <ul className="space-y-1">
            {adminMenuItems.map(item => {
          const isActive = location.pathname === item.path || location.pathname.startsWith(item.path + '/');
          return <li key={item.path}>
                  <Link 
                    to={item.path} 
                    onClick={isMobile ? onToggle : undefined}
                    className={cn("flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200", isActive ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-lg" : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground", isCollapsed && "justify-center")}
                  >
                    <item.icon className="w-5 h-5 flex-shrink-0" />
                    {!isCollapsed && <span className="font-medium">{item.label}</span>}
                  </Link>
                </li>;
        })}
          </ul>
        </div>}

      {/* Footer */}
      {!isCollapsed && <div className="p-4 border-t border-sidebar-border">
          <div className="text-xs text-sidebar-foreground opacity-60 text-center">
            Version 1.0.0
          </div>
        </div>}
    </div>;
};
export { Sidebar };


import { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { Breadcrumbs } from './Breadcrumbs';
import { useIsMobile } from '@/hooks/use-mobile';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

export const MainLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const isMobile = useIsMobile();

  // Auto-collapse sidebar on mobile
  useEffect(() => {
    if (isMobile) {
      setSidebarCollapsed(true);
    }
  }, [isMobile]);

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, []);

  if (isMobile) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col w-full">
        {/* Mobile Header */}
        <div className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200">
          <Header 
            isMobile={true} 
            onMobileMenuToggle={() => setMobileMenuOpen(!mobileMenuOpen)}
          />
        </div>
        
        {/* Mobile Sidebar Sheet */}
        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
          <SheetContent side="left" className="p-0 w-80">
            <Sidebar 
              isCollapsed={false} 
              onToggle={() => setMobileMenuOpen(false)}
              isMobile={true}
            />
          </SheetContent>
        </Sheet>
        
        {/* Main Content */}
        <div className="flex-1 pt-16 overflow-auto">
          <div className="p-4 pb-safe">
            <div className="block sm:hidden mb-4">
              <Breadcrumbs />
            </div>
            <main>
              <Outlet />
            </main>
          </div>
        </div>
      </div>
    );
  }

  // Desktop Layout
  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <div className="fixed top-0 left-0 z-30 h-full">
        <Sidebar 
          isCollapsed={sidebarCollapsed} 
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
          isMobile={false}
        />
      </div>
      
      <div 
        className={`flex-1 flex flex-col min-w-0 transition-all duration-300 ${
          sidebarCollapsed ? 'ml-16' : 'ml-64'
        }`}
      >
        <div className="fixed top-0 right-0 z-20 bg-white border-b border-gray-200" 
             style={{ 
               left: sidebarCollapsed ? '64px' : '256px',
               transition: 'left 0.3s ease-in-out'
             }}>
          <Header isMobile={false} />
        </div>
        
        <div className="flex-1 overflow-auto pt-16">
          <div className="p-6">
            <Breadcrumbs />
            <main className="mt-4">
              <Outlet />
            </main>
          </div>
        </div>
      </div>
    </div>
  );
};

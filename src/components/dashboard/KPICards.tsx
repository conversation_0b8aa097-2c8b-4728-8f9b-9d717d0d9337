import { Building, FileText, DollarSign, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface KPIData {
  totalProperties: number;
  activeLeases: number;
  monthlyRevenue: number;
  occupancyRate: number;
}

interface KPICardsProps {
  data: KPIData;
}

export const KPICards = ({ data }: KPICardsProps) => {
  const kpiData = [
    {
      title: 'Total Properties',
      value: data.totalProperties || 0,
      icon: Building,
      color: 'text-blue-600',
    },
    {
      title: 'Active Leases',
      value: data.activeLeases || 0,
      icon: FileText,
      color: 'text-emerald-600',
    },
    {
      title: 'Monthly Revenue',
      value: `CAD ${(data.monthlyRevenue || 0).toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-600',
    },
    {
      title: 'Occupancy Rate',
      value: `${(data.occupancyRate || 0).toFixed(1)}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {kpiData.map((kpi) => (
        <Card key={kpi.title} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{kpi.title}</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">{kpi.value}</p>
              </div>
              <div className={`p-3 rounded-lg bg-gray-50 ${kpi.color}`}>
                <kpi.icon className="w-6 h-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

import { BarChart3 } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { StatusBadge } from '@/components/ui/status-badge';

interface ActivityItem {
  type: string;
  message: string;
  time: string;
  status: 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled';
}

interface RecentActivityProps {
  activities: ActivityItem[];
}

export const RecentActivity = ({ activities }: RecentActivityProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart3 className="w-5 h-5 mr-2" />
          Recent Lease Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.length > 0 ? (
            activities.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                </div>
                <StatusBadge status={activity.status} />
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-4">No recent lease activity</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

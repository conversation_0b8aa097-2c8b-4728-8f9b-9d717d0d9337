import { FileText } from 'lucide-react';
import { <PERSON>, CardHeader, Card<PERSON>itle, CardContent } from '@/components/ui/card';
import { StatusBadge } from '@/components/ui/status-badge';

interface ExpirationItem {
  property: string;
  tenant: string;
  expires: string;
  status: 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled';
}

interface UpcomingExpirationsProps {
  expirations: ExpirationItem[];
}

export const UpcomingExpirations = ({ expirations }: UpcomingExpirationsProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="w-5 h-5 mr-2" />
          Upcoming Lease Expirations (30 days)
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {expirations.length > 0 ? (
            expirations.map((lease, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div>
                  <p className="text-sm font-medium text-gray-900">{lease.property}</p>
                  <p className="text-xs text-gray-500">{lease.tenant}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{lease.expires}</p>
                  <StatusBadge status={lease.status} />
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-4">No upcoming expirations</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

import { Plus, <PERSON>S<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>3 } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AddTenantModal } from '@/components/tenants/AddTenantModal';

interface QuickActionsProps {
  onAddLease: () => void;
  onViewFinancials: () => void;
  onViewMaintenance: () => void;
}

export const QuickActions = ({ onAddLease, onViewFinancials, onViewMaintenance }: QuickActionsProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button 
            variant="outline" 
            className="h-20 flex flex-col space-y-2 hover:bg-emerald-50 hover:border-emerald-200"
            onClick={onAddLease}
          >
            <Plus className="w-5 h-5" />
            <span className="text-sm">Add New Lease</span>
          </Button>
          <Button 
            variant="outline" 
            className="h-20 flex flex-col space-y-2 hover:bg-emerald-50 hover:border-emerald-200"
            onClick={onViewFinancials}
          >
            <DollarSign className="w-5 h-5" />
            <span className="text-sm">Record Payment</span>
          </Button>
          <Button 
            variant="outline" 
            className="h-20 flex flex-col space-y-2 hover:bg-emerald-50 hover:border-emerald-200"
            onClick={onViewMaintenance}
          >
            <Wrench className="w-5 h-5" />
            <span className="text-sm">View Maintenance</span>
          </Button>
          <AddTenantModal trigger={
            <Button variant="outline" className="h-20 flex flex-col space-y-2 hover:bg-emerald-50 hover:border-emerald-200">
              <BarChart3 className="w-5 h-5" />
              <span className="text-sm">Add Tenant</span>
            </Button>
          } />
        </div>
      </CardContent>
    </Card>
  );
};

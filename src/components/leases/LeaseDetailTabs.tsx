import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { LeaseOverview } from './LeaseOverview';
import { LeaseParties } from './LeaseParties';
import { LeaseFinancials } from './LeaseFinancials';
import { LeasePropertyRights } from './LeasePropertyRights';
import { LeaseOperations } from './LeaseOperations';
import { LeaseLegalTerms } from './LeaseLegalTerms';
import { LeaseDocuments } from './LeaseDocuments';
import { LeaseChat } from '../chat/LeaseChat';
import { useState } from 'react';

interface LeaseDetailTabsProps {
  lease: any;
}

export const LeaseDetailTabs = ({ lease }: LeaseDetailTabsProps) => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {/* New 8-Tab Structure */}
      <Tabs value={activeTab} onValue<PERSON><PERSON>e={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-8 lg:grid-cols-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="parties">Parties</TabsTrigger>
          <TabsTrigger value="financials">Financial</TabsTrigger>
          <TabsTrigger value="property">Property</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
          <TabsTrigger value="legal">Legal</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="chat">AI Assistant</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <LeaseOverview lease={lease} />
        </TabsContent>

        <TabsContent value="parties">
          <LeaseParties 
            parties={lease.parties || []}
            guarantors={lease.guarantors || []}
            brokers={lease.brokers || []}
            lenders={lease.lenders || []}
          />
        </TabsContent>

        <TabsContent value="financials">
          <LeaseFinancials 
            lease={lease}
            financialTerms={lease.financial_terms?.[0] || null}
            securityDeposits={lease.security_deposits || []}
            rentEscalations={lease.rent_escalations || []}
            operatingCosts={lease.operating_costs_details || []}
            tenantInducements={lease.tenant_inducements || []}
            purchaseOptions={lease.purchase_options || []}
          />
        </TabsContent>

        <TabsContent value="property">
          <LeasePropertyRights 
            lease={lease}
            properties={lease.properties || []}
            premiseConditions={lease.premises_condition_terms || []}
            useRestrictions={lease.use_restrictions || []}
            improvementTerms={lease.improvement_terms || []}
            amenities={lease.leased_amenities || []}
          />
        </TabsContent>

        <TabsContent value="operations">
          <LeaseOperations 
            lease={lease}
            maintenanceObligations={lease.maintenance_obligations || []}
            complianceRequirements={lease.compliance_requirements || []}
            accessRights={lease.access_inspection_rights || []}
            relocationClauses={lease.relocation_clauses || []}
          />
        </TabsContent>

        <TabsContent value="legal">
          <LeaseLegalTerms 
            lease={lease}
            insuranceTerms={lease.insurance_liability_terms || []}
            defaultRemedies={lease.default_remedies || []}
            disputeResolution={lease.dispute_resolution || []}
            forceMajeure={lease.force_majeure_provisions || []}
            healthEmergency={lease.health_emergency_provisions || []}
            environmental={lease.environmental_provisions || []}
            indemnification={lease.indemnification_clauses || []}
            estoppel={lease.estoppel_certificate_provisions || []}
            subordination={lease.subordination_attornment_provisions || []}
          />
        </TabsContent>

        <TabsContent value="documents">
          <LeaseDocuments 
            documentAttachments={lease.document_attachments || []}
            signatures={lease.signatures || []}
            noticeProvisions={lease.notice_provisions || []}
            legalProvisions={lease.legal_general_provisions || []}
            financialInfoProvisions={lease.financial_information_provisions || []}
          />
        </TabsContent>

        <TabsContent value="chat">
          <LeaseChat leaseId={lease.lease_id} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

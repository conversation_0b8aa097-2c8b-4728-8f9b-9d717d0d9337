
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Wrench, User, DollarSign, Clock, Calendar, AlertTriangle, Hammer, Droplets, Zap, Building, Sparkles } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface MaintenanceObligation {
  maintenance_id: string;
  maintenance_type: string;
  maintenance_description: string;
  responsible_party: string;
  cost_responsibility: string;
  notification_requirements: string;
}

interface LeaseMaintenanceProps {
  maintenanceObligations: MaintenanceObligation[];
}

export const LeaseMaintenance = ({ maintenanceObligations }: LeaseMaintenanceProps) => {
  const groupedObligations = maintenanceObligations.reduce((acc, obligation) => {
    const type = obligation.maintenance_type || 'General Maintenance';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(obligation);
    return acc;
  }, {} as Record<string, MaintenanceObligation[]>);

  const getIconForType = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('hvac')) return Zap;
    if (lowerType.includes('plumbing')) return Droplets;
    if (lowerType.includes('electrical')) return Zap;
    if (lowerType.includes('structural')) return Building;
    if (lowerType.includes('cleaning')) return Sparkles;
    if (lowerType.includes('general')) return Wrench;
    return Hammer;
  };

  const getResponsibilityColor = (party: string) => {
    switch (party?.toLowerCase()) {
      case 'landlord': return 'default';
      case 'tenant': return 'secondary';
      case 'shared': return 'outline';
      default: return 'outline';
    }
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  if (Object.entries(groupedObligations).length === 0) {
    return (
      <Card>
        <CardContent className="text-center p-12">
          <Wrench className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Maintenance Obligations</h3>
          <p className="text-gray-500">No maintenance obligations have been defined for this lease.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Maintenance Obligations</h2>
          <p className="text-gray-600 mt-1">Maintenance responsibilities and requirements</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {maintenanceObligations.length} {maintenanceObligations.length === 1 ? 'Obligation' : 'Obligations'}
        </Badge>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {Object.entries(groupedObligations).map(([type, obligations]) => {
          const IconComponent = getIconForType(type);
          return (
            <AccordionItem key={type} value={type} className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-orange-50">
                      <IconComponent className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{capitalizeFirstLetter(type)}</h3>
                      <p className="text-sm text-gray-600">
                        {obligations.length} {obligations.length === 1 ? 'obligation' : 'obligations'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {obligations.map((obligation) => (
                      <div key={obligation.maintenance_id} className="p-6 border rounded-lg bg-gray-50">
                        <div className="space-y-4">
                          <div>
                            <p className="font-medium text-gray-900 text-base leading-relaxed">
                              {obligation.maintenance_description || 'No description provided'}
                            </p>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                              <User className="h-5 w-5 text-blue-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Responsible Party</p>
                                <Badge variant={getResponsibilityColor(obligation.responsible_party) as any} className="mt-1">
                                  {capitalizeFirstLetter(obligation.responsible_party) || 'Not specified'}
                                </Badge>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                              <DollarSign className="h-5 w-5 text-green-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Cost Responsibility</p>
                                <Badge variant={getResponsibilityColor(obligation.cost_responsibility) as any} className="mt-1">
                                  {capitalizeFirstLetter(obligation.cost_responsibility) || 'Not specified'}
                                </Badge>
                              </div>
                            </div>
                          </div>

                          {obligation.notification_requirements && (
                            <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-l-blue-400">
                              <div className="flex items-start gap-3">
                                <Clock className="h-5 w-5 text-blue-500 mt-0.5" />
                                <div>
                                  <p className="font-medium text-blue-900 text-sm">Notification Requirements</p>
                                  <p className="text-blue-800 mt-1">
                                    {obligation.notification_requirements}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Landlord Obligations</p>
              <p className="text-xl font-semibold">
                {maintenanceObligations.filter(m => m.responsible_party?.toLowerCase() === 'landlord').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <User className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Tenant Obligations</p>
              <p className="text-xl font-semibold">
                {maintenanceObligations.filter(m => m.responsible_party?.toLowerCase() === 'tenant').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-50">
              <AlertTriangle className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Shared Responsibilities</p>
              <p className="text-xl font-semibold">
                {maintenanceObligations.filter(m => m.responsible_party?.toLowerCase() === 'shared').length}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

import { useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, CheckCircle, AlertCircle, XCircle, 
  DollarSign, Settings, Shield, Building, Scale
} from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface UseRestriction {
  restriction_id: string;
  restriction_type: string;
  restriction_category: string;
  restriction_description: string;
}

interface EnhancedTerm extends UseRestriction {
  priority: string;
  status: string;
}

interface LeaseTermsInnovativeProps {
  lease: any;
}

export const LeaseTermsInnovative = ({ lease }: LeaseTermsInnovativeProps) => {
  const restrictions = lease.use_restrictions || [];

  // Enhanced data processing with mock additional terms
  const enhancedTerms = useMemo(() => {
    const baseTerms = restrictions.map((r: UseRestriction) => ({
      ...r,
      priority: r.restriction_type === 'prohibited' ? 'high' : 
               r.restriction_type === 'restricted' ? 'medium' : 'low',
      status: 'active'
    }));

    // Add comprehensive lease terms for better demo
    const additionalTerms = [
      {
        restriction_id: 'financial-1',
        restriction_type: 'financial',
        restriction_category: 'Payment Terms',
        restriction_description: 'Monthly rent due on the 1st of each month. Late fees of 5% apply after 5 days.',
        priority: 'high',
        status: 'active'
      },
      {
        restriction_id: 'maintenance-1',
        restriction_type: 'maintenance',
        restriction_category: 'Property Care',
        restriction_description: 'Tenant responsible for minor repairs under $100. Major repairs handled by landlord.',
        priority: 'medium',
        status: 'active'
      },
      {
        restriction_id: 'insurance-1',
        restriction_type: 'insurance',
        restriction_category: 'Coverage Requirements',
        restriction_description: 'Tenant must maintain $1M general liability insurance with landlord as additional insured.',
        priority: 'high',
        status: 'active'
      },
      {
        restriction_id: 'access-1',
        restriction_type: 'access',
        restriction_category: 'Property Access',
        restriction_description: 'Landlord may inspect premises with 24 hours written notice except in emergencies.',
        priority: 'medium',
        status: 'active'
      }
    ];

    return [...baseTerms, ...additionalTerms];
  }, [restrictions]);

  const groupedTerms = useMemo(() => {
    return enhancedTerms.reduce((acc, term) => {
      const category = term.restriction_category || 'General Terms';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(term);
      return acc;
    }, {} as Record<string, EnhancedTerm[]>);
  }, [enhancedTerms]);

  const getIconForType = (type: string) => {
    const iconMap: Record<string, any> = {
      'prohibited': XCircle,
      'restricted': AlertCircle,
      'permitted': CheckCircle,
      'financial': DollarSign,
      'maintenance': Settings,
      'insurance': Shield,
      'access': Building,
      'compliance': Scale,
      'default': FileText
    };
    return iconMap[type] || iconMap.default;
  };

  const getColorForType = (type: string) => {
    const colorMap: Record<string, string> = {
      'prohibited': 'bg-red-50 text-red-600',
      'restricted': 'bg-yellow-50 text-yellow-600',
      'permitted': 'bg-green-50 text-green-600',
      'financial': 'bg-blue-50 text-blue-600',
      'maintenance': 'bg-purple-50 text-purple-600',
      'insurance': 'bg-indigo-50 text-indigo-600',
      'access': 'bg-gray-50 text-gray-600',
      'compliance': 'bg-teal-50 text-teal-600'
    };
    return colorMap[type] || 'bg-gray-50 text-gray-600';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'default';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    
    // Special handling for specific terms that should always be capitalized
    const lowerStr = str.toLowerCase();
    
    if (lowerStr === 'use') return 'Use';
    if (lowerStr === 'vehicle') return 'Vehicle';
    if (lowerStr === 'activity') return 'Activity';
    
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  if (Object.entries(groupedTerms).length === 0) {
    return (
      <Card>
        <CardContent className="text-center p-12">
          <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Terms & Conditions</h3>
          <p className="text-gray-500">No lease terms or restrictions have been defined for this lease.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Terms & Conditions</h2>
          <p className="text-gray-600 mt-1">Lease terms, restrictions, and obligations</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {enhancedTerms.length} {enhancedTerms.length === 1 ? 'Term' : 'Terms'}
        </Badge>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {Object.entries(groupedTerms).map(([category, terms]) => {
          const termsArray = terms as EnhancedTerm[];
          const firstTerm = termsArray[0];
          const IconComponent = getIconForType(firstTerm?.restriction_type);
          const iconColor = getColorForType(firstTerm?.restriction_type);
          
          return (
            <AccordionItem key={category} value={category} className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className={`p-2 rounded-lg ${iconColor}`}>
                      <IconComponent className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{category}</h3>
                      <p className="text-sm text-gray-600">
                        {termsArray.length} {termsArray.length === 1 ? 'term' : 'terms'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {termsArray.map((term: EnhancedTerm) => (
                      <div key={term.restriction_id} className="p-6 border rounded-lg bg-gray-50">
                        <div className="space-y-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 text-base leading-relaxed">
                                {term.restriction_description || 'No description provided'}
                              </p>
                            </div>
                            <Badge variant={getPriorityColor(term.priority) as any} className="ml-2">
                              {capitalizeFirstLetter(term.priority)} Priority
                            </Badge>
                          </div>
                          
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${
                                term.restriction_type === 'prohibited' ? 'bg-red-500' :
                                term.restriction_type === 'restricted' ? 'bg-yellow-500' :
                                term.restriction_type === 'permitted' ? 'bg-green-500' :
                                'bg-blue-500'
                              }`}></div>
                              <span className="text-sm text-gray-600">Type:</span>
                              <Badge variant="outline" className="text-xs">
                                {capitalizeFirstLetter(term.restriction_type)}
                              </Badge>
                            </div>
                          </div>

                          {term.restriction_type === 'prohibited' && (
                            <div className="p-4 bg-red-50 rounded-lg border-l-4 border-l-red-400">
                              <div className="flex items-start gap-3">
                                <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                                <div>
                                  <p className="font-medium text-red-900 text-sm">Strictly Prohibited</p>
                                  <p className="text-red-800 mt-1 text-sm">
                                    This activity is completely forbidden under this lease agreement.
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                          {term.restriction_type === 'financial' && (
                            <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-l-blue-400">
                              <div className="flex items-start gap-3">
                                <DollarSign className="h-5 w-5 text-blue-500 mt-0.5" />
                                <div>
                                  <p className="font-medium text-blue-900 text-sm">Financial Obligation</p>
                                  <p className="text-blue-800 mt-1 text-sm">
                                    Payment terms and financial responsibilities.
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-red-50">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Prohibited</p>
              <p className="text-xl font-semibold">
                {enhancedTerms.filter(t => t.restriction_type === 'prohibited').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-yellow-50">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Restricted</p>
              <p className="text-xl font-semibold">
                {enhancedTerms.filter(t => t.restriction_type === 'restricted').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <DollarSign className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Financial</p>
              <p className="text-xl font-semibold">
                {enhancedTerms.filter(t => t.restriction_type === 'financial').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">High Priority</p>
              <p className="text-xl font-semibold">
                {enhancedTerms.filter(t => t.priority === 'high').length}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail, MapPin, Phone, FileText, Clock, Building } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface NoticeProvision {
  notice_id: string;
  party_type: string;
  notice_address: string;
  delivery_methods: string;
  service_domicile: string;
}

interface LeaseNoticeProvisionsProps {
  noticeProvisions: NoticeProvision[];
}

export const LeaseNoticeProvisions = ({ noticeProvisions }: LeaseNoticeProvisionsProps) => {
  const groupedProvisions = noticeProvisions.reduce((acc, provision) => {
    const type = provision.party_type || 'General';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(provision);
    return acc;
  }, {} as Record<string, NoticeProvision[]>);

  const getIconForParty = (party: string) => {
    const lowerParty = party.toLowerCase();
    if (lowerParty.includes('landlord')) return Building;
    if (lowerParty.includes('tenant')) return MapPin;
    return Mail;
  };

  const getPartyColor = (party: string) => {
    switch (party?.toLowerCase()) {
      case 'landlord': return 'default';
      case 'tenant': return 'secondary';
      default: return 'outline';
    }
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  if (Object.entries(groupedProvisions).length === 0) {
    return (
      <Card>
        <CardContent className="text-center p-12">
          <Mail className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Notice Provisions</h3>
          <p className="text-gray-500">No notice provisions have been defined for this lease.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Notice & Communication</h2>
          <p className="text-gray-600 mt-1">Notice requirements and communication methods</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {noticeProvisions.length} {noticeProvisions.length === 1 ? 'Provision' : 'Provisions'}
        </Badge>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {Object.entries(groupedProvisions).map(([party, provisions]) => {
          const IconComponent = getIconForParty(party);
          return (
            <AccordionItem key={party} value={party} className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-blue-50">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{capitalizeFirstLetter(party)}</h3>
                      <p className="text-sm text-gray-600">
                        {provisions.length} notice {provisions.length === 1 ? 'address' : 'addresses'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {provisions.map((provision) => (
                      <div key={provision.notice_id} className="p-6 border rounded-lg bg-gray-50">
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-start gap-3 p-3 bg-white rounded-lg border">
                              <MapPin className="h-5 w-5 text-green-500 mt-1" />
                              <div className="flex-1">
                                <p className="text-sm font-medium text-gray-900">Notice Address</p>
                                <p className="text-gray-700 mt-1 text-sm leading-relaxed">
                                  {provision.notice_address || 'Not specified'}
                                </p>
                              </div>
                            </div>
                            
                            <div className="flex items-start gap-3 p-3 bg-white rounded-lg border">
                              <Mail className="h-5 w-5 text-blue-500 mt-1" />
                              <div className="flex-1">
                                <p className="text-sm font-medium text-gray-900">Delivery Methods</p>
                                <p className="text-gray-700 mt-1 text-sm leading-relaxed">
                                  {provision.delivery_methods || 'Not specified'}
                                </p>
                              </div>
                            </div>
                          </div>

                          {provision.service_domicile && (
                            <div className="p-4 bg-purple-50 rounded-lg border-l-4 border-l-purple-400">
                              <div className="flex items-start gap-3">
                                <FileText className="h-5 w-5 text-purple-500 mt-0.5" />
                                <div>
                                  <p className="font-medium text-purple-900 text-sm">Service Domicile</p>
                                  <p className="text-purple-800 mt-1 text-sm">
                                    {provision.service_domicile}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Building className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Landlord Notices</p>
              <p className="text-xl font-semibold">
                {noticeProvisions.filter(n => n.party_type?.toLowerCase() === 'landlord').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <MapPin className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Tenant Notices</p>
              <p className="text-xl font-semibold">
                {noticeProvisions.filter(n => n.party_type?.toLowerCase() === 'tenant').length}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

import { useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { DollarSign, Settings, FileText } from 'lucide-react';

interface LeaseMetricsProps {
  lease: any;
}

export const LeaseMetrics = ({ lease }: LeaseMetricsProps) => {
  const keyMetrics = useMemo(() => {
    const totalPayments = lease.payment_transactions?.length || 0;
    const completedPayments = lease.payment_transactions?.filter((p: any) => p.payment_status === 'completed').length || 0;
    const totalMaintenance = lease.maintenance_requests?.length || 0;
    const completedMaintenance = lease.maintenance_requests?.filter((m: any) => m.status === 'completed').length || 0;
    const totalDocuments = (lease.document_attachments?.length || 0) + (lease.document_storage?.length || 0);
    const complianceItems = lease.compliance_requirements?.length || 0;
    
    const totalRevenue = lease.payment_transactions?.reduce((sum: number, p: any) => 
      p.payment_status === 'completed' ? sum + (p.amount || 0) : sum, 0) || 0;
    
    const outstandingBalance = lease.payment_transactions?.reduce((sum: number, p: any) => 
      p.payment_status === 'pending' || p.payment_status === 'overdue' ? sum + (p.amount || 0) : sum, 0) || 0;

    const maintenanceCosts = lease.maintenance_requests?.reduce((sum: number, m: any) => 
      sum + (m.actual_cost || m.estimated_cost || 0), 0) || 0;

    return {
      totalPayments, completedPayments, totalMaintenance, completedMaintenance,
      totalDocuments, complianceItems, totalRevenue, outstandingBalance, maintenanceCosts,
      paymentCompletionRate: totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0,
      maintenanceCompletionRate: totalMaintenance > 0 ? (completedMaintenance / totalMaintenance) * 100 : 0,
      profitMargin: totalRevenue > 0 ? ((totalRevenue - maintenanceCosts) / totalRevenue) * 100 : 0
    };
  }, [lease]);

  const metrics = [
    { 
      icon: DollarSign, 
      label: 'Revenue', 
      value: `$${keyMetrics.totalRevenue.toLocaleString()}`, 
      color: 'green' 
    },
    { 
      icon: Settings, 
      label: 'Maintenance', 
      value: `${keyMetrics.maintenanceCompletionRate.toFixed(0)}%`, 
      color: 'blue' 
    },
    { 
      icon: FileText, 
      label: 'Documents', 
      value: keyMetrics.totalDocuments, 
      color: 'purple' 
    }
  ];

  return (
    <>
      {metrics.map((metric, index) => (
        <Card key={index} className="p-4 bg-white/90 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-${metric.color}-100`}>
              <metric.icon className={`h-5 w-5 text-${metric.color}-600`} />
            </div>
            <div>
              <p className="text-sm text-gray-600">{metric.label}</p>
              <p className="text-lg font-bold">{metric.value}</p>
            </div>
          </div>
        </Card>
      ))}
    </>
  );
};

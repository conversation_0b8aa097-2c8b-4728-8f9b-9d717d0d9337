
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface UseRestriction {
  restriction_id: string;
  restriction_type: string;
  restriction_category: string;
  restriction_description: string;
}

interface LeaseTermsProps {
  restrictions: UseRestriction[];
}

export const LeaseTerms = ({ restrictions }: LeaseTermsProps) => {
  const groupedRestrictions = restrictions.reduce((acc, restriction) => {
    const category = restriction.restriction_category || 'General Terms';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(restriction);
    return acc;
  }, {} as Record<string, UseRestriction[]>);

  const getIconForCategory = (category: string) => {
    if (category.toLowerCase().includes('use')) return FileText;
    if (category.toLowerCase().includes('compliance')) return Shield;
    return CheckCircle;
  };

  const getTypeColor = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'prohibited': return 'destructive';
      case 'restricted': return 'secondary';
      case 'permitted': return 'default';
      default: return 'outline';
    }
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  if (Object.entries(groupedRestrictions).length === 0) {
    return (
      <Card>
        <CardContent className="text-center p-12">
          <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Terms & Restrictions</h3>
          <p className="text-gray-500">No use restrictions or terms have been defined for this lease.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Terms & Restrictions</h2>
          <p className="text-gray-600 mt-1">Use restrictions and terms governing this lease</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {restrictions.length} {restrictions.length === 1 ? 'Term' : 'Terms'}
        </Badge>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {Object.entries(groupedRestrictions).map(([category, categoryRestrictions]) => {
          const IconComponent = getIconForCategory(category);
          return (
            <AccordionItem key={category} value={category} className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-blue-50">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{capitalizeFirstLetter(category)}</h3>
                      <p className="text-sm text-gray-600">
                        {categoryRestrictions.length} {categoryRestrictions.length === 1 ? 'restriction' : 'restrictions'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {categoryRestrictions.map((restriction, index) => (
                      <div key={restriction.restriction_id} className={`p-4 rounded-lg border-l-4 ${
                        restriction.restriction_type?.toLowerCase() === 'prohibited' 
                          ? 'border-l-red-400 bg-red-50' 
                          : restriction.restriction_type?.toLowerCase() === 'restricted'
                          ? 'border-l-yellow-400 bg-yellow-50'
                          : 'border-l-green-400 bg-green-50'
                      }`}>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={getTypeColor(restriction.restriction_type) as any}>
                                {capitalizeFirstLetter(restriction.restriction_type) || 'General'}
                              </Badge>
                              {restriction.restriction_type?.toLowerCase() === 'prohibited' && (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                            </div>
                            <p className="text-gray-800 leading-relaxed">
                              {restriction.restriction_description || 'No description provided'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-red-50">
              <AlertCircle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Prohibited Uses</p>
              <p className="text-xl font-semibold">
                {restrictions.filter(r => r.restriction_type?.toLowerCase() === 'prohibited').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-yellow-50">
              <Shield className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Restricted Uses</p>
              <p className="text-xl font-semibold">
                {restrictions.filter(r => r.restriction_type?.toLowerCase() === 'restricted').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Permitted Uses</p>
              <p className="text-xl font-semibold">
                {restrictions.filter(r => r.restriction_type?.toLowerCase() === 'permitted').length}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};


interface LeaseDetailMetricsProps {
  monthlyRent: number | null;
  currency: string;
  rentalAreaSqft: number | null;
  securityDepositTotal: number | null;
  leaseTermMonths: number | null;
}

export const LeaseDetailMetrics = ({
  monthlyRent,
  currency,
  rentalAreaSqft,
  securityDepositTotal,
  leaseTermMonths
}: LeaseDetailMetricsProps) => {
  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
      <div className="bg-white p-4 rounded-lg border">
        <p className="text-sm text-gray-600">Monthly Rent</p>
        <p className="text-2xl font-bold">
          {monthlyRent ? `${currency} ${monthlyRent.toLocaleString()}` : 'N/A'}
        </p>
      </div>
      <div className="bg-white p-4 rounded-lg border">
        <p className="text-sm text-gray-600">Area</p>
        <p className="text-2xl font-bold">
          {rentalAreaSqft ? `${rentalAreaSqft} sq ft` : 'N/A'}
        </p>
      </div>
      <div className="bg-white p-4 rounded-lg border">
        <p className="text-sm text-gray-600">Security Deposit</p>
        <p className="text-2xl font-bold">
          {securityDepositTotal 
            ? `${currency} ${securityDepositTotal.toLocaleString()}` 
            : 'N/A'}
        </p>
      </div>
      <div className="bg-white p-4 rounded-lg border">
        <p className="text-sm text-gray-600">Term Remaining</p>
        <p className="text-2xl font-bold">
          {leaseTermMonths ? `${leaseTermMonths} months` : 'N/A'}
        </p>
      </div>
    </div>
  );
};

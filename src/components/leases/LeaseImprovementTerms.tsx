import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Hammer, DollarSign, CheckCircle, AlertTriangle, User, Building } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface ImprovementTerm {
  improvement_id: string;
  improvement_type: string;
  quality_requirements: string;
  cost_responsibility: string;
  admin_fee_percentage: number;
  ownership_terms: string;
  approval_required: boolean;
}

interface LeaseImprovementTermsProps {
  improvementTerms: ImprovementTerm[];
}

export const LeaseImprovementTerms = ({ improvementTerms }: LeaseImprovementTermsProps) => {
  const groupedTerms = improvementTerms.reduce((acc, term) => {
    const type = term.improvement_type || 'General Improvements';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(term);
    return acc;
  }, {} as Record<string, ImprovementTerm[]>);

  const getIconForType = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('structural')) return Building;
    if (lowerType.includes('cosmetic')) return Hammer;
    if (lowerType.includes('mechanical')) return Building;
    if (lowerType.includes('electrical')) return Building;
    return Hammer;
  };

  const getResponsibilityColor = (party: string) => {
    switch (party?.toLowerCase()) {
      case 'landlord': return 'default';
      case 'tenant': return 'secondary';
      case 'shared': return 'outline';
      default: return 'outline';
    }
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    
    // Handle specific cases for improvement terms
    if (str.toLowerCase() === 'tenant_work') {
      return 'Tenant Work';
    }
    if (str.toLowerCase() === 'landlord_work') {
      return 'Landlord Work';
    }
    
    // Replace underscores with spaces and capitalize each word
    return str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (Object.entries(groupedTerms).length === 0) {
    return (
      <Card>
        <CardContent className="text-center p-12">
          <Hammer className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Improvement Terms</h3>
          <p className="text-gray-500">No improvement terms have been defined for this lease.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Improvement Terms</h2>
          <p className="text-gray-600 mt-1">Property improvement rights and requirements</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {improvementTerms.length} {improvementTerms.length === 1 ? 'Term' : 'Terms'}
        </Badge>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {Object.entries(groupedTerms).map(([type, terms]) => {
          const IconComponent = getIconForType(type);
          return (
            <AccordionItem key={type} value={type} className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-orange-50">
                      <IconComponent className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{capitalizeFirstLetter(type)}</h3>
                      <p className="text-sm text-gray-600">
                        {terms.length} {terms.length === 1 ? 'term' : 'terms'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {terms.map((term) => (
                      <div key={term.improvement_id} className="p-6 border rounded-lg bg-gray-50">
                        <div className="space-y-4">
                          <div className="flex items-center gap-2 mb-3">
                            {term.approval_required && <Badge variant="secondary">Approval Required</Badge>}
                          </div>
                          
                          <div>
                            <p className="font-medium text-gray-900 text-base leading-relaxed">
                              {term.quality_requirements || 'No quality requirements specified'}
                            </p>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                              <User className="h-5 w-5 text-blue-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Cost Responsibility</p>
                                <Badge variant={getResponsibilityColor(term.cost_responsibility) as any} className="mt-1">
                                  {capitalizeFirstLetter(term.cost_responsibility) || 'Not specified'}
                                </Badge>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                              <DollarSign className="h-5 w-5 text-green-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Admin Fee</p>
                                <p className="text-gray-700 mt-1">{term.admin_fee_percentage}%</p>
                              </div>
                            </div>

                            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                              <Building className="h-5 w-5 text-purple-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Ownership</p>
                                <p className="text-gray-700 mt-1 text-sm">{term.ownership_terms || 'Not specified'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Landlord Responsibility</p>
              <p className="text-xl font-semibold">
                {improvementTerms.filter(t => t.cost_responsibility?.toLowerCase() === 'landlord').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <User className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Tenant Responsibility</p>
              <p className="text-xl font-semibold">
                {improvementTerms.filter(t => t.cost_responsibility?.toLowerCase() === 'tenant').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-orange-50">
              <CheckCircle className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Requiring Approval</p>
              <p className="text-xl font-semibold">
                {improvementTerms.filter(t => t.approval_required).length}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};


import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, AlertTriangle, User, FileText, CheckCircle2, XCircle, ShieldCheck, Leaf, FileCheck, Heart, Building2 } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface ComplianceRequirement {
  compliance_id: string;
  compliance_type: string;
  compliance_description: string;
  responsibility_party: string;
  penalty_terms: string;
}

interface LeaseComplianceProps {
  complianceRequirements: ComplianceRequirement[];
}

export const LeaseCompliance = ({ complianceRequirements }: LeaseComplianceProps) => {
  const groupedRequirements = complianceRequirements.reduce((acc, requirement) => {
    const type = requirement.compliance_type || 'General Compliance';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(requirement);
    return acc;
  }, {} as Record<string, ComplianceRequirement[]>);

  const getIconForType = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('safety')) return ShieldCheck;
    if (lowerType.includes('environmental')) return Leaf;
    if (lowerType.includes('insurance')) return Shield;
    if (lowerType.includes('permit')) return FileCheck;
    if (lowerType.includes('health')) return Heart;
    if (lowerType.includes('building')) return Building2;
    return FileText;
  };

  const getResponsibilityColor = (party: string) => {
    switch (party?.toLowerCase()) {
      case 'landlord': return 'default';
      case 'tenant': return 'secondary';
      case 'shared': return 'outline';
      default: return 'outline';
    }
  };

  const getSeverityLevel = (penaltyTerms: string) => {
    if (!penaltyTerms) return 'low';
    const lowerPenalty = penaltyTerms.toLowerCase();
    if (lowerPenalty.includes('termination') || lowerPenalty.includes('eviction')) return 'high';
    if (lowerPenalty.includes('fine') || lowerPenalty.includes('penalty')) return 'medium';
    return 'low';
  };

  const formatComplianceType = (type: string) => {
    if (!type) return type;
    // Replace underscores with spaces and capitalize each word
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  if (Object.entries(groupedRequirements).length === 0) {
    return (
      <Card>
        <CardContent className="text-center p-12">
          <Shield className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Compliance Requirements</h3>
          <p className="text-gray-500">No compliance requirements have been defined for this lease.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Compliance Requirements</h2>
          <p className="text-gray-600 mt-1">Legal and regulatory compliance obligations</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {complianceRequirements.length} {complianceRequirements.length === 1 ? 'Requirement' : 'Requirements'}
        </Badge>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {Object.entries(groupedRequirements).map(([type, requirements]) => {
          const IconComponent = getIconForType(type);
          const formattedType = formatComplianceType(type);
          return (
            <AccordionItem key={type} value={type} className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-green-50">
                      <IconComponent className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{formattedType}</h3>
                      <p className="text-sm text-gray-600">
                        {requirements.length} {requirements.length === 1 ? 'requirement' : 'requirements'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {requirements.map((requirement) => {
                      const severity = getSeverityLevel(requirement.penalty_terms);
                      return (
                        <div key={requirement.compliance_id} className={`p-6 border rounded-lg ${
                          severity === 'high' ? 'bg-red-50 border-red-200' :
                          severity === 'medium' ? 'bg-yellow-50 border-yellow-200' :
                          'bg-gray-50 border-gray-200'
                        }`}>
                          <div className="space-y-4">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <p className="font-medium text-gray-900 text-base leading-relaxed">
                                  {requirement.compliance_description || 'No description provided'}
                                </p>
                              </div>
                              {severity === 'high' && (
                                <AlertTriangle className="h-5 w-5 text-red-500 ml-2" />
                              )}
                            </div>
                            
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-600">Responsible:</span>
                                <Badge variant={getResponsibilityColor(requirement.responsibility_party) as any}>
                                  {capitalizeFirstLetter(requirement.responsibility_party) || 'Not specified'}
                                </Badge>
                              </div>
                            </div>

                            {requirement.penalty_terms && (
                              <div className={`p-4 rounded-lg border-l-4 ${
                                severity === 'high' ? 'border-l-red-400 bg-red-100' :
                                severity === 'medium' ? 'border-l-yellow-400 bg-yellow-100' :
                                'border-l-gray-400 bg-gray-100'
                              }`}>
                                <div className="flex items-start gap-3">
                                  <AlertTriangle className={`h-5 w-5 mt-0.5 ${
                                    severity === 'high' ? 'text-red-600' :
                                    severity === 'medium' ? 'text-yellow-600' :
                                    'text-gray-600'
                                  }`} />
                                  <div>
                                    <p className={`font-medium text-sm ${
                                      severity === 'high' ? 'text-red-900' :
                                      severity === 'medium' ? 'text-yellow-900' :
                                      'text-gray-900'
                                    }`}>
                                      Penalty Terms
                                    </p>
                                    <p className={`mt-1 ${
                                      severity === 'high' ? 'text-red-800' :
                                      severity === 'medium' ? 'text-yellow-800' :
                                      'text-gray-800'
                                    }`}>
                                      {requirement.penalty_terms}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-red-50">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">High Risk</p>
              <p className="text-xl font-semibold">
                {complianceRequirements.filter(r => getSeverityLevel(r.penalty_terms) === 'high').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-yellow-50">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Medium Risk</p>
              <p className="text-xl font-semibold">
                {complianceRequirements.filter(r => getSeverityLevel(r.penalty_terms) === 'medium').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Landlord Duties</p>
              <p className="text-xl font-semibold">
                {complianceRequirements.filter(r => r.responsibility_party?.toLowerCase() === 'landlord').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Tenant Duties</p>
              <p className="text-xl font-semibold">
                {complianceRequirements.filter(r => r.responsibility_party?.toLowerCase() === 'tenant').length}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

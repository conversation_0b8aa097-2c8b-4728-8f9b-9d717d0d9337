import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail, Phone, MapPin, Shield, Briefcase, CreditCard, User, Building2, FileText } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface Party {
  party_id: string;
  party_name: string;
  party_type: string;
  legal_status: string;
  address_street: string;
  address_city: string;
  address_province: string;
  address_postal_code: string;
  representative_name: string;
  representative_title: string;
  authorization_details: string;
  email: string;
  phone_number: string;
}

interface Guarantor {
  id: string;
  party_id: string;
  guarantee_type: string;
  max_liability_amount: number;
  guarantee_duration_start: string;
  guarantee_duration_end: string;
  conditions: string;
}

interface Broker {
  id: string;
  broker_name: string;
  company_name: string;
  broker_type: string;
  commission_details: string;
}

interface Lender {
  id: string;
  lender_name: string;
  contact_person: string;
  loan_type: string;
  notes: string;
}

interface LeasePartiesProps {
  parties: Party[];
  guarantors?: Guarantor[];
  brokers?: Broker[];
  lenders?: Lender[];
}

export const LeaseParties = ({ parties, guarantors = [], brokers = [], lenders = [] }: LeasePartiesProps) => {
  const landlords = parties.filter(p => p.party_type === 'landlord');
  const tenants = parties.filter(p => p.party_type === 'tenant');
  const others = parties.filter(p => !['landlord', 'tenant'].includes(p.party_type));

  const DetailedPartyCard = ({ party, title }: { party: Party; title: string }) => (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl flex items-center gap-3">
          <div className="p-2 rounded-lg bg-blue-50">
            <Building2 className="h-6 w-6 text-blue-600" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span>{title}</span>
              <Badge variant="secondary" className="text-xs">
                {party.legal_status || party.party_type}
              </Badge>
            </div>
            <h3 className="font-bold text-2xl text-gray-900 mt-1">{party.party_name}</h3>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Contact Information */}
        <div className="space-y-4">
          <h4 className="font-semibold text-lg text-gray-800 border-b border-gray-200 pb-2">Contact Information</h4>
          
          {/* Address */}
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-lg bg-gray-50">
              <MapPin className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <p className="font-medium text-gray-900">Address</p>
              <div className="text-sm text-gray-700 mt-1">
                <p>{party.address_street}</p>
                <p>{party.address_city}, {party.address_province} {party.address_postal_code}</p>
              </div>
            </div>
          </div>

          {/* Email */}
          {party.email && (
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-blue-50">
                <Mail className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Email</p>
                <p className="text-sm text-blue-600 mt-1">
                  <a href={`mailto:${party.email}`} className="hover:underline">
                    {party.email}
                  </a>
                </p>
              </div>
            </div>
          )}

          {/* Phone */}
          {party.phone_number && (
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-green-50">
                <Phone className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Phone</p>
                <p className="text-sm text-green-600 mt-1">
                  <a href={`tel:${party.phone_number}`} className="hover:underline">
                    {party.phone_number}
                  </a>
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Representative Information */}
        {party.representative_name && (
          <div className="space-y-4">
            <h4 className="font-semibold text-lg text-gray-800 border-b border-gray-200 pb-2">Representative</h4>
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-purple-50">
                <User className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{party.representative_name}</p>
                <p className="text-sm text-gray-600 mt-1">{party.representative_title}</p>
              </div>
            </div>
          </div>
        )}

        {/* Authorization Details */}
        {party.authorization_details && (
          <div className="space-y-4">
            <h4 className="font-semibold text-lg text-gray-800 border-b border-gray-200 pb-2">Authorization</h4>
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-lg bg-yellow-50">
                <FileText className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Authorization Details</p>
                <p className="text-sm text-gray-700 mt-1 leading-relaxed">{party.authorization_details}</p>
              </div>
            </div>
          </div>
        )}

        {/* Legal Status Details */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Legal Status</span>
            <Badge variant="outline" className="text-sm">
              {party.legal_status || 'Not specified'}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-8">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">Parties & Relationships</h2>
          <p className="text-gray-600 mt-2 text-lg">Complete details of all entities involved in this lease agreement</p>
        </div>
        <Badge variant="outline" className="text-sm px-3 py-1">
          {parties.length + guarantors.length + brokers.length + lenders.length} Total Entities
        </Badge>
      </div>

      {/* Main Parties - Landlords and Tenants Side by Side */}
      {(landlords.length > 0 || tenants.length > 0) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Landlords Column */}
          <div className="space-y-6">
            {landlords.length > 0 && (
              <>
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-lg bg-blue-50">
                    <Building2 className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">
                      Landlord{landlords.length > 1 ? 's' : ''}
                    </h3>
                    <p className="text-gray-600">{landlords.length} landlord{landlords.length === 1 ? '' : 's'} in this agreement</p>
                  </div>
                </div>
                <div className="space-y-6">
                  {landlords.map((party) => (
                    <DetailedPartyCard key={party.party_id} party={party} title="Landlord" />
                  ))}
                </div>
              </>
            )}
          </div>

          {/* Tenants Column */}
          <div className="space-y-6">
            {tenants.length > 0 && (
              <>
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-lg bg-green-50">
                    <User className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">
                      Tenant{tenants.length > 1 ? 's' : ''}
                    </h3>
                    <p className="text-gray-600">{tenants.length} tenant{tenants.length === 1 ? '' : 's'} in this agreement</p>
                  </div>
                </div>
                <div className="space-y-6">
                  {tenants.map((party) => (
                    <DetailedPartyCard key={party.party_id} party={party} title="Tenant" />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Additional Parties and Relationships */}
      {(guarantors.length > 0 || brokers.length > 0 || lenders.length > 0 || others.length > 0) && (
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-lg bg-purple-50">
              <Shield className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">Related Parties & Services</h3>
              <p className="text-gray-600">Additional entities supporting this lease agreement</p>
            </div>
          </div>

          <Accordion type="multiple" className="space-y-4">
            {/* Guarantors */}
            {guarantors.length > 0 && (
              <AccordionItem value="guarantors" className="border rounded-lg">
                <Card>
                  <AccordionTrigger className="px-6 py-4 hover:no-underline">
                    <div className="flex items-center gap-3 text-left">
                      <div className="p-2 rounded-lg bg-blue-50">
                        <Shield className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg">Guarantors</h4>
                        <p className="text-sm text-gray-600">
                          {guarantors.length} guarantee{guarantors.length === 1 ? '' : 's'} securing this lease
                        </p>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <CardContent className="pt-0 space-y-4">
                      {guarantors.map((guarantor) => {
                        const guarantorParty = parties.find(p => p.party_id === guarantor.party_id);
                        return (
                          <div key={guarantor.id} className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
                            <div className="space-y-4">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h5 className="font-semibold text-xl text-blue-900">
                                    {guarantorParty?.party_name || 'Unknown Guarantor'}
                                  </h5>
                                  <Badge variant="outline" className="mt-2">
                                    {guarantor.guarantee_type}
                                  </Badge>
                                </div>
                                <div className="text-right">
                                  <Badge variant="secondary" className="mb-2">
                                    ${guarantor.max_liability_amount?.toLocaleString() || 'N/A'} max liability
                                  </Badge>
                                  <p className="text-sm text-blue-700">
                                    {new Date(guarantor.guarantee_duration_start).toLocaleDateString()} - {new Date(guarantor.guarantee_duration_end).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              
                              {guarantorParty && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                  <div className="p-4 bg-white rounded border border-blue-200">
                                    <p className="text-sm text-gray-700 font-medium mb-2">Contact Information:</p>
                                    <div className="space-y-1 text-sm text-gray-600">
                                      {guarantorParty.email && (
                                        <p className="flex items-center gap-2">
                                          <Mail className="h-3 w-3" />
                                          {guarantorParty.email}
                                        </p>
                                      )}
                                      {guarantorParty.phone_number && (
                                        <p className="flex items-center gap-2">
                                          <Phone className="h-3 w-3" />
                                          {guarantorParty.phone_number}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                  <div className="p-4 bg-white rounded border border-blue-200">
                                    <p className="text-sm text-gray-700 font-medium mb-2">Address:</p>
                                    <div className="text-sm text-gray-600">
                                      <p>{guarantorParty.address_street}</p>
                                      <p>{guarantorParty.address_city}, {guarantorParty.address_province} {guarantorParty.address_postal_code}</p>
                                    </div>
                                  </div>
                                </div>
                              )}
                              
                              {guarantor.conditions && (
                                <div className="p-4 bg-white rounded border border-blue-200">
                                  <p className="text-sm text-gray-700 font-medium mb-2">Guarantee Conditions:</p>
                                  <p className="text-sm text-gray-600 leading-relaxed">{guarantor.conditions}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </CardContent>
                  </AccordionContent>
                </Card>
              </AccordionItem>
            )}

            {/* Brokers */}
            {brokers.length > 0 && (
              <AccordionItem value="brokers" className="border rounded-lg">
                <Card>
                  <AccordionTrigger className="px-6 py-4 hover:no-underline">
                    <div className="flex items-center gap-3 text-left">
                      <div className="p-2 rounded-lg bg-green-50">
                        <Briefcase className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg">Real Estate Brokers</h4>
                        <p className="text-sm text-gray-600">
                          {brokers.length} broker{brokers.length === 1 ? '' : 's'} involved in this transaction
                        </p>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <CardContent className="pt-0 space-y-4">
                      {brokers.map((broker) => (
                        <div key={broker.id} className="p-6 bg-green-50 border border-green-200 rounded-lg">
                          <div className="space-y-4">
                            <div className="flex items-start justify-between">
                              <div>
                                <h5 className="font-semibold text-xl text-green-900">{broker.broker_name}</h5>
                                <p className="text-green-700 text-lg mt-1">{broker.company_name}</p>
                              </div>
                              <Badge variant="outline" className="text-sm">
                                {broker.broker_type}
                              </Badge>
                            </div>
                            
                            {broker.commission_details && (
                              <div className="p-4 bg-white rounded border border-green-200">
                                <p className="text-sm text-gray-700 font-medium mb-2">Commission Details:</p>
                                <p className="text-sm text-gray-600 leading-relaxed">{broker.commission_details}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </AccordionContent>
                </Card>
              </AccordionItem>
            )}

            {/* Lenders */}
            {lenders.length > 0 && (
              <AccordionItem value="lenders" className="border rounded-lg">
                <Card>
                  <AccordionTrigger className="px-6 py-4 hover:no-underline">
                    <div className="flex items-center gap-3 text-left">
                      <div className="p-2 rounded-lg bg-purple-50">
                        <CreditCard className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg">Financial Institutions</h4>
                        <p className="text-sm text-gray-600">
                          {lenders.length} lender{lenders.length === 1 ? '' : 's'} with interests in this property
                        </p>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <CardContent className="pt-0 space-y-4">
                      {lenders.map((lender) => (
                        <div key={lender.id} className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
                          <div className="space-y-4">
                            <div className="flex items-start justify-between">
                              <div>
                                <h5 className="font-semibold text-xl text-purple-900">{lender.lender_name}</h5>
                                <p className="text-purple-700 text-sm mt-1">Contact: {lender.contact_person}</p>
                              </div>
                              <Badge variant="outline" className="text-sm">
                                {lender.loan_type}
                              </Badge>
                            </div>
                            
                            {lender.notes && (
                              <div className="p-4 bg-white rounded border border-purple-200">
                                <p className="text-sm text-gray-700 font-medium mb-2">Notes:</p>
                                <p className="text-sm text-gray-600 leading-relaxed">{lender.notes}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </AccordionContent>
                </Card>
              </AccordionItem>
            )}

            {/* Other Parties */}
            {others.length > 0 && (
              <AccordionItem value="others" className="border rounded-lg">
                <Card>
                  <AccordionTrigger className="px-6 py-4 hover:no-underline">
                    <div className="flex items-center gap-3 text-left">
                      <div className="p-2 rounded-lg bg-gray-50">
                        <User className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg">Other Parties</h4>
                        <p className="text-sm text-gray-600">
                          {others.length} additional part{others.length === 1 ? 'y' : 'ies'} involved in this lease
                        </p>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                        {others.map((party) => (
                          <DetailedPartyCard key={party.party_id} party={party} title={party.party_type} />
                        ))}
                      </div>
                    </CardContent>
                  </AccordionContent>
                </Card>
              </AccordionItem>
            )}
          </Accordion>
        </div>
      )}

      {/* Enhanced Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 pt-6">
        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-lg bg-blue-50">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-medium">Guarantees</p>
              <p className="text-2xl font-bold">{guarantors.length}</p>
              <p className="text-xs text-gray-500">Financial security</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-lg bg-green-50">
              <Briefcase className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-medium">Brokers</p>
              <p className="text-2xl font-bold">{brokers.length}</p>
              <p className="text-xs text-gray-500">Transaction facilitators</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-lg bg-purple-50">
              <CreditCard className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-medium">Lenders</p>
              <p className="text-2xl font-bold">{lenders.length}</p>
              <p className="text-xs text-gray-500">Financial institutions</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-lg bg-gray-50">
              <User className="h-6 w-6 text-gray-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-medium">Total Entities</p>
              <p className="text-2xl font-bold">{parties.length + guarantors.length + brokers.length + lenders.length}</p>
              <p className="text-xs text-gray-500">All involved parties</p>
            </div>
          </div>
        </Card>
      </div>

      {(parties.length === 0 && guarantors.length === 0 && brokers.length === 0 && lenders.length === 0) && (
        <div className="text-center p-12 text-gray-500">
          <User className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Parties Information</h3>
          <p className="text-gray-500">No parties information is available for this lease.</p>
        </div>
      )}
    </div>
  );
};

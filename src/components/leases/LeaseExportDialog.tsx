import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download, FileText, BarChart3, Archive } from 'lucide-react';
import { toast } from 'sonner';

interface LeaseExportDialogProps {
  lease: any;
}

export const LeaseExportDialog = ({ lease }: LeaseExportDialogProps) => {
  const [isExporting, setIsExporting] = useState(false);

  const calculateLeaseHealth = () => {
    let score = 100;
    const issues = [];
    const warnings = [];
    const recommendations = [];

    if (!lease.security_deposits?.length) {
      score -= 15;
      issues.push('No security deposits recorded');
      recommendations.push('Add security deposit information');
    }

    const overduePayments = lease.payment_transactions?.filter((p: any) => 
      p.payment_status === 'overdue' || p.late_fee_amount > 0
    ).length || 0;
    if (overduePayments > 0) {
      score -= 20;
      issues.push(`${overduePayments} overdue payments detected`);
      recommendations.push('Contact tenant for payment resolution');
    }

    const highPriorityMaintenance = lease.maintenance_requests?.filter((m: any) => 
      m.priority === 'high' && m.status !== 'completed'
    ).length || 0;
    if (highPriorityMaintenance > 0) {
      score -= 10;
      warnings.push(`${highPriorityMaintenance} high-priority maintenance items`);
      recommendations.push('Schedule urgent maintenance');
    }

    if (!lease.compliance_requirements?.length) {
      score -= 10;
      warnings.push('No compliance requirements defined');
      recommendations.push('Define compliance requirements');
    }

    const totalDocs = (lease.document_attachments?.length || 0) + (lease.document_storage?.length || 0);
    if (totalDocs < 5) {
      score -= 5;
      warnings.push('Limited documentation');
      recommendations.push('Upload additional documents');
    }

    return { score: Math.max(0, score), issues, warnings, recommendations };
  };

  const generatePDFReport = async () => {
    setIsExporting(true);
    try {
      const healthScore = calculateLeaseHealth();
      const financialTerms = lease.financial_terms?.[0];
      
      const totalRevenue = lease.payment_transactions?.reduce((sum: number, p: any) => 
        p.payment_status === 'completed' ? sum + (p.amount || 0) : sum, 0) || 0;
      
      const outstandingBalance = lease.payment_transactions?.reduce((sum: number, p: any) => 
        p.payment_status === 'pending' || p.payment_status === 'overdue' ? sum + (p.amount || 0) : sum, 0) || 0;

      const maintenanceCosts = lease.maintenance_requests?.reduce((sum: number, m: any) => 
        sum + (m.actual_cost || m.estimated_cost || 0), 0) || 0;

      const totalDocuments = (lease.document_attachments?.length || 0) + (lease.document_storage?.length || 0);
      const totalPayments = lease.payment_transactions?.length || 0;
      const completedPayments = lease.payment_transactions?.filter((p: any) => p.payment_status === 'completed').length || 0;
      const paymentCompletionRate = totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0;
      const profitMargin = totalRevenue > 0 ? ((totalRevenue - maintenanceCosts) / totalRevenue) * 100 : 0;

      // Create comprehensive HTML structure optimized for PDF printing
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Lease Analysis Report - ${lease.lease_id}</title>
          <meta charset="UTF-8">
          <style>
            @media print {
              body { -webkit-print-color-adjust: exact; color-adjust: exact; }
              .page-break { page-break-before: always; }
              .no-print { display: none; }
            }
            
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              margin: 0; 
              padding: 40px; 
              color: #2d3748; 
              line-height: 1.6; 
              background: #ffffff;
            }
            
            .header { 
              text-align: center; 
              border-bottom: 3px solid #4F46E5; 
              padding-bottom: 30px; 
              margin-bottom: 40px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              margin: -40px -40px 40px -40px;
              padding: 40px;
            }
            
            .header h1 { margin: 0 0 10px 0; font-size: 28px; font-weight: 700; }
            .header h2 { margin: 0 0 20px 0; font-size: 20px; font-weight: 400; opacity: 0.9; }
            .header p { margin: 5px 0; font-size: 14px; opacity: 0.8; }
            
            .section { 
              margin-bottom: 40px; 
              page-break-inside: avoid; 
              background: #ffffff;
              border-radius: 12px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              padding: 25px;
              border: 1px solid #e2e8f0;
            }
            
            .section-title { 
              color: #4F46E5; 
              font-size: 20px; 
              font-weight: 700; 
              margin-bottom: 20px; 
              border-left: 5px solid #4F46E5; 
              padding-left: 15px;
              display: flex;
              align-items: center;
              gap: 10px;
            }
            
            .info-grid { 
              display: grid; 
              grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
              gap: 20px; 
              margin-bottom: 25px; 
            }
            
            .info-item { 
              padding: 20px; 
              background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); 
              border-radius: 10px; 
              border: 1px solid #e2e8f0;
              transition: all 0.3s ease;
            }
            
            .info-label { 
              font-weight: 600; 
              color: #4a5568; 
              font-size: 12px; 
              text-transform: uppercase; 
              letter-spacing: 0.5px;
              margin-bottom: 8px;
            }
            
            .info-value { 
              font-size: 18px; 
              font-weight: 700; 
              color: #2d3748;
              margin-top: 5px; 
            }
            
            .health-score { 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
              color: white; 
              padding: 30px; 
              border-radius: 15px; 
              text-align: center; 
              margin-bottom: 30px;
              box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            }
            
            .health-score h3 { 
              margin: 0 0 10px 0; 
              font-size: 24px; 
              font-weight: 700; 
            }
            
            .health-score p { 
              margin: 0; 
              font-size: 16px; 
              opacity: 0.9; 
            }
            
            .metrics-grid { 
              display: grid; 
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
              gap: 20px; 
              margin: 25px 0; 
            }
            
            .metric-card { 
              background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%); 
              padding: 25px; 
              border-radius: 12px; 
              text-align: center; 
              border: 2px solid #e2e8f0;
              transition: all 0.3s ease;
            }
            
            .metric-value { 
              font-size: 28px; 
              font-weight: 700; 
              color: #4F46E5; 
              margin-bottom: 8px;
            }
            
            .metric-label { 
              font-size: 12px; 
              color: #718096; 
              text-transform: uppercase; 
              letter-spacing: 0.5px;
              font-weight: 600;
            }
            
            .alert-box { 
              background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%); 
              border: 2px solid #fc8181; 
              border-radius: 10px; 
              padding: 20px; 
              margin: 15px 0; 
            }
            
            .alert-title { 
              font-weight: 700; 
              color: #c53030; 
              font-size: 16px;
              display: flex;
              align-items: center;
              gap: 8px;
            }
            
            .recommendation { 
              background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%); 
              border-left: 5px solid #3182ce; 
              padding: 20px; 
              margin: 15px 0; 
              border-radius: 0 10px 10px 0;
            }
            
            .recommendation strong { 
              color: #2b6cb0; 
              font-size: 16px;
            }
            
            .footer { 
              margin-top: 50px; 
              padding-top: 25px; 
              border-top: 2px solid #e2e8f0; 
              text-align: center; 
              color: #718096;
              font-size: 14px;
            }
            
            .print-instructions {
              background: #fff3cd;
              border: 1px solid #ffeaa7;
              padding: 20px;
              border-radius: 10px;
              margin-bottom: 30px;
              text-align: center;
              font-weight: 600;
              color: #856404;
            }
            
            .terms-summary {
              background: #f0f4f8;
              padding: 20px;
              border-radius: 10px;
              margin: 20px 0;
            }
            
            .party-info {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 20px;
              margin: 20px 0;
            }
            
            .party-card {
              background: #f8f9fa;
              padding: 20px;
              border-radius: 10px;
              border-left: 4px solid #4F46E5;
            }
          </style>
        </head>
        <body>
          <div class="print-instructions no-print">
            📄 <strong>Print Instructions:</strong> Press Ctrl+P (or Cmd+P on Mac) and select "Save as PDF" to create a PDF file.
          </div>
          
          <div class="header">
            <h1>🏢 Comprehensive Lease Analysis Report</h1>
            <h2>Lease ID: ${lease.lease_id}</h2>
            <p><strong>Generated:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            <p><strong>Status:</strong> ${lease.lease_status?.toUpperCase() || 'N/A'}</p>
          </div>

          <div class="section">
            <div class="section-title">📊 Executive Summary</div>
            <div class="health-score">
              <h3>Overall Health Score: ${healthScore.score}/100</h3>
              <p>${healthScore.score >= 90 ? '🟢 Excellent Performance' : healthScore.score >= 70 ? '🟡 Good Standing' : '🔴 Requires Attention'}</p>
            </div>
            <div class="metrics-grid">
              <div class="metric-card">
                <div class="metric-value">$${totalRevenue.toLocaleString()}</div>
                <div class="metric-label">💰 Total Revenue</div>
              </div>
              <div class="metric-card">
                <div class="metric-value">${paymentCompletionRate.toFixed(0)}%</div>
                <div class="metric-label">💳 Payment Rate</div>
              </div>
              <div class="metric-card">
                <div class="metric-value">${totalDocuments}</div>
                <div class="metric-label">📋 Documents</div>
              </div>
            </div>
          </div>

          ${healthScore.issues.length > 0 ? `
          <div class="section">
            <div class="section-title">⚠️ Critical Issues</div>
            ${healthScore.issues.map(issue => `
              <div class="alert-box">
                <div class="alert-title">🚨 ${issue}</div>
              </div>
            `).join('')}
          </div>` : ''}

          <div class="section">
            <div class="section-title">💼 Financial Summary</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Monthly Base Rent</div>
                <div class="info-value">$${financialTerms?.monthly_base_rent?.toLocaleString() || 'N/A'}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Outstanding Balance</div>
                <div class="info-value">$${outstandingBalance.toLocaleString()}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Maintenance Costs</div>
                <div class="info-value">$${maintenanceCosts.toLocaleString()}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Profit Margin</div>
                <div class="info-value">${profitMargin.toFixed(1)}%</div>
              </div>
            </div>
          </div>

          ${lease.parties?.length > 0 ? `
          <div class="section">
            <div class="section-title">👥 Parties Information</div>
            <div class="party-info">
              ${lease.parties.map((party: any) => `
                <div class="party-card">
                  <h4>${party.party_type?.toUpperCase() || 'PARTY'}</h4>
                  <p><strong>Name:</strong> ${party.party_name || 'N/A'}</p>
                  <p><strong>Contact:</strong> ${party.contact_email || party.contact_phone || 'N/A'}</p>
                </div>
              `).join('')}
            </div>
          </div>` : ''}

          ${healthScore.recommendations.length > 0 ? `
          <div class="section">
            <div class="section-title">💡 Recommendations</div>
            ${healthScore.recommendations.map(rec => `
              <div class="recommendation">
                <strong>📝 Recommendation:</strong> ${rec}
              </div>
            `).join('')}
          </div>` : ''}

          <div class="footer">
            <p><strong>Report Generated:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            <p><em>This is an automated report generated by the Brasswater Lease Management System.</em></p>
            <p>🏢 Comprehensive lease analysis for informed decision making</p>
          </div>
        </body>
        </html>
      `;

      // Create blob and download with immediate PDF instructions
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Lease-Report-${lease.lease_id}-${new Date().toISOString().split('T')[0]}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // Auto-open print dialog for immediate PDF creation
      setTimeout(() => {
        const printWindow = window.open(url, '_blank');
        if (printWindow) {
          printWindow.onload = () => {
            setTimeout(() => {
              printWindow.print();
            }, 500);
          };
        }
      }, 1000);
      
      toast.success('PDF Report generated! The report will open automatically. Use your browser\'s print function to save as PDF.');
    } catch (error) {
      toast.error('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const generateExcelReport = async () => {
    setIsExporting(true);
    try {
      // Create comprehensive Excel-formatted CSV content
      const csvContent = [
        // Headers
        [
          'Lease ID', 'Status', 'Tenant Name', 'Landlord Name', 'Property Address',
          'Monthly Rent', 'Annual Rent', 'Currency', 'Lease Term (Months)',
          'Commencement Date', 'End Date', 'Security Deposit', 'Rental Area (sqft)',
          'Payment Status', 'Outstanding Balance', 'Total Revenue', 'Maintenance Costs',
          'Health Score', 'Document Count', 'Last Payment Date', 'Contact Email', 'Contact Phone'
        ],
        
        // Data row
        [
          lease.lease_id || '',
          lease.lease_status || '',
          lease.parties?.find((p: any) => p.party_type === 'tenant')?.party_name || '',
          lease.parties?.find((p: any) => p.party_type === 'landlord')?.party_name || '',
          lease.properties?.[0]?.building_address || lease.properties?.[0]?.leased_premises_address || '',
          lease.financial_terms?.[0]?.monthly_base_rent || '',
          lease.financial_terms?.[0]?.annual_base_rent || '',
          lease.financial_terms?.[0]?.rent_currency || 'CAD',
          lease.lease_term_months || '',
          lease.commencement_date || '',
          lease.end_date || '',
          lease.security_deposits?.[0]?.security_deposit_total || '',
          lease.properties?.[0]?.rental_area_sqft || '',
          (() => {
            const total = lease.payment_transactions?.length || 0;
            const completed = lease.payment_transactions?.filter((p: any) => p.payment_status === 'completed').length || 0;
            return total > 0 ? `${completed}/${total} (${Math.round((completed/total)*100)}%)` : 'N/A';
          })(),
          lease.payment_transactions?.reduce((sum: number, p: any) => 
            (p.payment_status === 'pending' || p.payment_status === 'overdue') ? sum + (p.amount || 0) : sum, 0) || 0,
          lease.payment_transactions?.reduce((sum: number, p: any) => 
            p.payment_status === 'completed' ? sum + (p.amount || 0) : sum, 0) || 0,
          lease.maintenance_requests?.reduce((sum: number, m: any) => 
            sum + (m.actual_cost || m.estimated_cost || 0), 0) || 0,
          calculateLeaseHealth().score,
          (lease.document_attachments?.length || 0) + (lease.document_storage?.length || 0),
          lease.payment_transactions?.filter((p: any) => p.payment_status === 'completed')?.[0]?.payment_date || '',
          lease.parties?.find((p: any) => p.party_type === 'tenant')?.contact_email || '',
          lease.parties?.find((p: any) => p.party_type === 'tenant')?.contact_phone || ''
        ]
      ];

      // Add payment transactions section
      if (lease.payment_transactions?.length > 0) {
        csvContent.push([]);
        csvContent.push(['PAYMENT TRANSACTIONS']);
        csvContent.push(['Transaction ID', 'Date', 'Amount', 'Status', 'Type', 'Late Fee', 'Description']);
        
        lease.payment_transactions.forEach((payment: any) => {
          csvContent.push([
            payment.transaction_id || '',
            payment.payment_date || '',
            payment.amount || '',
            payment.payment_status || '',
            payment.payment_type || '',
            payment.late_fee_amount || '',
            payment.description || ''
          ]);
        });
      }

      // Add maintenance requests section
      if (lease.maintenance_requests?.length > 0) {
        csvContent.push([]);
        csvContent.push(['MAINTENANCE REQUESTS']);
        csvContent.push(['Request ID', 'Date', 'Description', 'Priority', 'Status', 'Estimated Cost', 'Actual Cost']);
        
        lease.maintenance_requests.forEach((maintenance: any) => {
          csvContent.push([
            maintenance.request_id || '',
            maintenance.request_date || '',
            maintenance.description || '',
            maintenance.priority || '',
            maintenance.status || '',
            maintenance.estimated_cost || '',
            maintenance.actual_cost || ''
          ]);
        });
      }

      // Add documents section
      const allDocuments = [...(lease.document_attachments || []), ...(lease.document_storage || [])];
      if (allDocuments.length > 0) {
        csvContent.push([]);
        csvContent.push(['DOCUMENTS']);
        csvContent.push(['Document Name', 'Type', 'Upload Date', 'File Size', 'Description']);
        
        allDocuments.forEach((doc: any) => {
          csvContent.push([
            doc.file_name || doc.document_name || '',
            doc.document_type || doc.file_type || '',
            doc.upload_date || doc.created_date || '',
            doc.file_size || '',
            doc.description || ''
          ]);
        });
      }

      // Convert to CSV format
      const csvString = csvContent.map(row => 
        row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
      ).join('\n');

      // Create and download file
      const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Lease-Data-${lease.lease_id}-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Excel/CSV file generated with comprehensive lease data!');
    } catch (error) {
      toast.error('Excel export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleExport = async (format: 'pdf' | 'excel') => {
    if (format === 'pdf') {
      return generatePDFReport();
    } else if (format === 'excel') {
      return generateExcelReport();
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Export Lease Data</DialogTitle>
          <DialogDescription>Choose your preferred export format</DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <Button onClick={() => handleExport('pdf')} disabled={isExporting} className="w-full justify-start">
            <FileText className="h-4 w-4 mr-2" />
            {isExporting ? 'Generating...' : 'Export as PDF Report'}
          </Button>
          <Button onClick={() => handleExport('excel')} disabled={isExporting} variant="outline" className="w-full justify-start">
            <BarChart3 className="h-4 w-4 mr-2" />
            Export as Excel Spreadsheet
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

import { useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { CheckCircle, AlertTriangle } from 'lucide-react';

interface LeaseHealthScoreProps {
  lease: any;
}

export const LeaseHealthScore = ({ lease }: LeaseHealthScoreProps) => {
  const calculateLeaseHealth = () => {
    let score = 100;
    const issues = [];
    const warnings = [];
    const recommendations = [];

    if (!lease.security_deposits?.length) {
      score -= 15;
      issues.push('No security deposits recorded');
      recommendations.push('Add security deposit information');
    }

    const overduePayments = lease.payment_transactions?.filter((p: any) => 
      p.payment_status === 'overdue' || p.late_fee_amount > 0
    ).length || 0;
    if (overduePayments > 0) {
      score -= 20;
      issues.push(`${overduePayments} overdue payments detected`);
      recommendations.push('Contact tenant for payment resolution');
    }

    const highPriorityMaintenance = lease.maintenance_requests?.filter((m: any) => 
      m.priority === 'high' && m.status !== 'completed'
    ).length || 0;
    if (highPriorityMaintenance > 0) {
      score -= 10;
      warnings.push(`${highPriorityMaintenance} high-priority maintenance items`);
      recommendations.push('Schedule urgent maintenance');
    }

    if (!lease.compliance_requirements?.length) {
      score -= 10;
      warnings.push('No compliance requirements defined');
      recommendations.push('Define compliance requirements');
    }

    const totalDocs = (lease.document_attachments?.length || 0) + (lease.document_storage?.length || 0);
    if (totalDocs < 5) {
      score -= 5;
      warnings.push('Limited documentation');
      recommendations.push('Upload additional documents');
    }

    return { score: Math.max(0, score), issues, warnings, recommendations };
  };

  const leaseHealth = calculateLeaseHealth();

  return (
    <Card className="p-4 bg-white/90 backdrop-blur-sm border-2">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">Health Score</p>
          <p className="text-2xl font-bold text-gray-900">{leaseHealth.score}</p>
          <div className="flex items-center gap-1 mt-1">
            {leaseHealth.score >= 90 ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : leaseHealth.score >= 70 ? (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-xs text-gray-600">
              {leaseHealth.score >= 90 ? 'Excellent' : leaseHealth.score >= 70 ? 'Good' : 'At Risk'}
            </span>
          </div>
        </div>
        <div className="h-12 w-12">
          <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#E5E7EB" strokeWidth="3" />
            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none"
              stroke={leaseHealth.score >= 90 ? "#10B981" : leaseHealth.score >= 70 ? "#F59E0B" : "#EF4444"}
              strokeWidth="3" strokeDasharray={`${leaseHealth.score}, 100`} />
          </svg>
        </div>
      </div>
    </Card>
  );
};

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>ch, Shield, Eye, MapPin, AlertTriangle } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface MaintenanceObligation {
  maintenance_id: string;
  responsible_party: string;
  maintenance_type: string;
  maintenance_description: string;
  notification_requirements: string;
  cost_responsibility: string;
  admin_fee_on_landlord_costs_rate: number;
  scope_details: string;
}

interface ComplianceRequirement {
  compliance_id: string;
  compliance_type: string;
  compliance_description: string;
  responsibility_party: string;
  penalty_terms: string;
}

interface AccessRight {
  access_id: string;
  access_type: string;
  notice_requirements: string;
  access_rights_description: string;
  time_restrictions: string;
}

interface RelocationClause {
  id: string;
  notice_period_days: number;
  landlord_cost_responsibility_details: string;
  tenant_compensation_exclusions: string;
  conditions: string;
}

interface LeaseOperationsProps {
  lease: any;
  maintenanceObligations?: MaintenanceObligation[];
  complianceRequirements?: ComplianceRequirement[];
  accessRights?: AccessRight[];
  relocationClauses?: RelocationClause[];
}

export const LeaseOperations = ({ 
  lease, 
  maintenanceObligations = [], 
  complianceRequirements = [], 
  accessRights = [], 
  relocationClauses = [] 
}: LeaseOperationsProps) => {
  
  const totalItems = maintenanceObligations.length + complianceRequirements.length + accessRights.length + relocationClauses.length;

  const getResponsibilityColor = (party: string) => {
    switch (party?.toLowerCase()) {
      case 'landlord': return 'bg-blue-50 border-blue-200 text-blue-900';
      case 'tenant': return 'bg-green-50 border-green-200 text-green-900';
      case 'shared': return 'bg-purple-50 border-purple-200 text-purple-900';
      default: return 'bg-gray-50 border-gray-200 text-gray-900';
    }
  };

  const getSeverityLevel = (penaltyTerms: string) => {
    if (!penaltyTerms) return 'low';
    const lowerPenalty = penaltyTerms.toLowerCase();
    if (lowerPenalty.includes('termination') || lowerPenalty.includes('eviction')) return 'high';
    if (lowerPenalty.includes('fine') || lowerPenalty.includes('penalty')) return 'medium';
    return 'low';
  };

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Operations & Maintenance</h2>
          <p className="text-gray-600 mt-1">Day-to-day operational requirements and procedures</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {totalItems} Operational Components
        </Badge>
      </div>

      {/* Operations Sections */}
      <Accordion type="multiple" className="space-y-4">
        {/* Maintenance Obligations */}
        {maintenanceObligations.length > 0 && (
          <AccordionItem value="maintenance" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-blue-50">
                    <Wrench className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Maintenance Obligations</h3>
                    <p className="text-sm text-gray-600">
                      {maintenanceObligations.length} obligation{maintenanceObligations.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {maintenanceObligations.map((obligation) => (
                    <div key={obligation.maintenance_id} className={`p-6 border rounded-lg ${getResponsibilityColor(obligation.responsible_party)}`}>
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg">{obligation.maintenance_type}</h4>
                            <p className="text-sm opacity-80 mt-1">{obligation.maintenance_description}</p>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline">
                              {obligation.responsible_party || 'Not specified'}
                            </Badge>
                            {obligation.admin_fee_on_landlord_costs_rate && (
                              <p className="text-xs mt-1 opacity-70">
                                Admin fee: {obligation.admin_fee_on_landlord_costs_rate}%
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {obligation.notification_requirements && (
                            <div className="p-4 bg-white rounded border">
                              <p className="text-sm font-medium mb-2">Notification Requirements:</p>
                              <p className="text-sm opacity-80">{obligation.notification_requirements}</p>
                            </div>
                          )}
                          {obligation.cost_responsibility && (
                            <div className="p-4 bg-white rounded border">
                              <p className="text-sm font-medium mb-2">Cost Responsibility:</p>
                              <p className="text-sm opacity-80">{obligation.cost_responsibility}</p>
                            </div>
                          )}
                        </div>

                        {obligation.scope_details && (
                          <div className="p-4 bg-white rounded border">
                            <p className="text-sm font-medium mb-2">Scope Details:</p>
                            <p className="text-sm opacity-80">{obligation.scope_details}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Compliance Requirements */}
        {complianceRequirements.length > 0 && (
          <AccordionItem value="compliance" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-green-50">
                    <Shield className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Compliance Requirements</h3>
                    <p className="text-sm text-gray-600">
                      {complianceRequirements.length} requirement{complianceRequirements.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {complianceRequirements.map((requirement) => {
                    const severity = getSeverityLevel(requirement.penalty_terms);
                    return (
                      <div key={requirement.compliance_id} className={`p-6 border rounded-lg ${
                        severity === 'high' ? 'bg-red-50 border-red-200' :
                        severity === 'medium' ? 'bg-yellow-50 border-yellow-200' :
                        'bg-green-50 border-green-200'
                      }`}>
                        <div className="space-y-4">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className={`font-semibold text-lg ${
                                severity === 'high' ? 'text-red-900' :
                                severity === 'medium' ? 'text-yellow-900' :
                                'text-green-900'
                              }`}>{requirement.compliance_type}</h4>
                              <p className={`text-sm mt-1 ${
                                severity === 'high' ? 'text-red-700' :
                                severity === 'medium' ? 'text-yellow-700' :
                                'text-green-700'
                              }`}>{requirement.compliance_description}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {requirement.responsibility_party || 'Not specified'}
                              </Badge>
                              {severity === 'high' && (
                                <AlertTriangle className="h-4 w-4 text-red-500" />
                              )}
                            </div>
                          </div>

                          {requirement.penalty_terms && (
                            <div className={`p-4 bg-white rounded border ${
                              severity === 'high' ? 'border-red-200' :
                              severity === 'medium' ? 'border-yellow-200' :
                              'border-green-200'
                            }`}>
                              <p className="text-sm font-medium mb-2">Penalty Terms:</p>
                              <p className="text-sm opacity-80">{requirement.penalty_terms}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Access & Inspection Rights */}
        {accessRights.length > 0 && (
          <AccordionItem value="access" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-purple-50">
                    <Eye className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Access & Inspection Rights</h3>
                    <p className="text-sm text-gray-600">
                      {accessRights.length} access right{accessRights.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {accessRights.map((access) => (
                    <div key={access.access_id} className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-purple-900">{access.access_type}</h4>
                            <p className="text-purple-700 text-sm mt-1">{access.access_rights_description}</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {access.notice_requirements && (
                            <div className="p-4 bg-white rounded border border-purple-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Notice Requirements:</p>
                              <p className="text-sm text-gray-600">{access.notice_requirements}</p>
                            </div>
                          )}
                          {access.time_restrictions && (
                            <div className="p-4 bg-white rounded border border-purple-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Time Restrictions:</p>
                              <p className="text-sm text-gray-600">{access.time_restrictions}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Relocation Clauses */}
        {relocationClauses.length > 0 && (
          <AccordionItem value="relocation" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-orange-50">
                    <MapPin className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Relocation Clauses</h3>
                    <p className="text-sm text-gray-600">
                      {relocationClauses.length} relocation clause{relocationClauses.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {relocationClauses.map((clause) => (
                    <div key={clause.id} className="p-6 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-orange-900">Relocation Rights</h4>
                            <p className="text-orange-700 text-sm mt-1">
                              Notice period: {clause.notice_period_days} days
                            </p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {clause.landlord_cost_responsibility_details && (
                            <div className="p-4 bg-white rounded border border-orange-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Landlord Cost Responsibility:</p>
                              <p className="text-sm text-gray-600">{clause.landlord_cost_responsibility_details}</p>
                            </div>
                          )}
                          {clause.tenant_compensation_exclusions && (
                            <div className="p-4 bg-white rounded border border-orange-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Tenant Compensation Exclusions:</p>
                              <p className="text-sm text-gray-600">{clause.tenant_compensation_exclusions}</p>
                            </div>
                          )}
                        </div>

                        {clause.conditions && (
                          <div className="p-4 bg-white rounded border border-orange-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Conditions:</p>
                            <p className="text-sm text-gray-600">{clause.conditions}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Wrench className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Maintenance</p>
              <p className="text-xl font-semibold">{maintenanceObligations.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <Shield className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Compliance</p>
              <p className="text-xl font-semibold">{complianceRequirements.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-50">
              <Eye className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Access Rights</p>
              <p className="text-xl font-semibold">{accessRights.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-orange-50">
              <MapPin className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Relocation</p>
              <p className="text-xl font-semibold">{relocationClauses.length}</p>
            </div>
          </div>
        </Card>
      </div>

      {totalItems === 0 && (
        <div className="text-center p-8 text-gray-500">
          No operational information available
        </div>
      )}
    </div>
  );
};

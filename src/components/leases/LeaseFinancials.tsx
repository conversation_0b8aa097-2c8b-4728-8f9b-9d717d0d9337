import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DollarSign, TrendingUp, Shield, Gift, Home, Receipt } from 'lucide-react';
import { Accordion, AccordionContent, Accordion<PERSON><PERSON>, AccordionTrigger } from '@/components/ui/accordion';

interface FinancialTerms {
  monthly_base_rent: number;
  annual_base_rent: number;
  annual_rent_per_sqft: number;
  rent_currency: string;
  rent_payment_frequency: string;
  rent_due_day: number;
  late_payment_fee: number;
  late_payment_threshold: number;
  gst_rate: number;
  qst_rate: number;
  estimated_operating_expenses_per_sqft: number;
  estimated_taxes_per_sqft: number;
}

interface SecurityDeposit {
  deposit_id: string;
  security_deposit_base: number;
  security_deposit_taxes: number;
  security_deposit_total: number;
  security_deposit_payment_method: string;
  security_deposit_interest_accrual_terms: string;
  security_deposit_conditions: string;
}

interface RentEscalation {
  id: string;
  escalation_type: string;
  start_date: string;
  end_date: string;
  amount_or_formula: string;
  frequency: string;
  review_mechanism: string;
  annual_base_rent_for_period: number;
  monthly_base_rent_for_period: number;
  rate_per_sqft_for_period: number;
}

interface OperatingCost {
  id: string;
  cost_type: string;
  calculation_method: string;
  inclusions: string;
  exclusions: string;
  cap_type: string;
  cap_amount_or_formula: number;
  reconciliation_frequency: string;
  recovery_method: string;
  gross_up_provision_enabled: boolean;
  gross_up_occupancy_percentage: number;
}

interface TenantInducement {
  id: string;
  inducement_type: string;
  amount: number;
  conditions: string;
  payment_schedule: string;
  rent_free_period_start: string;
  rent_free_period_end: string;
}

interface PurchaseOption {
  id: string;
  option_type: string;
  conditions: string;
  purchase_price_formula: string;
  exercise_period: string;
  notice_requirements: string;
}

interface LeaseFinancialsProps {
  lease: any;
  financialTerms: FinancialTerms | null;
  securityDeposits?: SecurityDeposit[];
  rentEscalations?: RentEscalation[];
  operatingCosts?: OperatingCost[];
  tenantInducements?: TenantInducement[];
  purchaseOptions?: PurchaseOption[];
}

export const LeaseFinancials = ({ 
  lease, 
  financialTerms, 
  securityDeposits = [], 
  rentEscalations = [], 
  operatingCosts = [], 
  tenantInducements = [], 
  purchaseOptions = [] 
}: LeaseFinancialsProps) => {
  
  const currency = financialTerms?.rent_currency || 'CAD';
  const totalFinancialItems = securityDeposits.length + rentEscalations.length + operatingCosts.length + tenantInducements.length + purchaseOptions.length;

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Financial Terms</h2>
          <p className="text-gray-600 mt-1">Complete financial structure and obligations</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {totalFinancialItems + (financialTerms ? 1 : 0)} Financial Components
        </Badge>
      </div>

      {/* Base Rent Overview */}
      {financialTerms && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-blue-50">
                  <DollarSign className="h-5 w-5 text-blue-600" />
                </div>
                Base Rent
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <p className="text-3xl font-bold text-blue-600">
                  {currency} {financialTerms.monthly_base_rent?.toLocaleString() || 'N/A'}
                </p>
                <p className="text-sm text-gray-600">per month</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm pt-4 border-t">
                <div>
                  <p className="text-gray-600">Annual</p>
                  <p className="font-medium">{currency} {financialTerms.annual_base_rent?.toLocaleString() || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-gray-600">Per Sq Ft</p>
                  <p className="font-medium">{currency} {financialTerms.annual_rent_per_sqft || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-green-50">
                  <Receipt className="h-5 w-5 text-green-600" />
                </div>
                Payment Terms
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Frequency</span>
                <span className="font-medium">{financialTerms.rent_payment_frequency || 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Due Day</span>
                <span className="font-medium">
                  {financialTerms.rent_due_day ? `${financialTerms.rent_due_day}${getOrdinal(financialTerms.rent_due_day)}` : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Late Fee</span>
                <span className="font-medium">{currency} {financialTerms.late_payment_fee || 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Grace Period</span>
                <span className="font-medium">{financialTerms.late_payment_threshold ? `${financialTerms.late_payment_threshold} days` : 'N/A'}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-purple-50">
                  <Receipt className="h-5 w-5 text-purple-600" />
                </div>
                Tax Rates
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">GST Rate</span>
                <span className="font-medium">{financialTerms.gst_rate ? `${financialTerms.gst_rate}%` : 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">QST Rate</span>
                <span className="font-medium">{financialTerms.qst_rate ? `${financialTerms.qst_rate}%` : 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Operating Exp./sq ft</span>
                <span className="font-medium">{currency} {financialTerms.estimated_operating_expenses_per_sqft || 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Taxes/sq ft</span>
                <span className="font-medium">{currency} {financialTerms.estimated_taxes_per_sqft || 'N/A'}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Financial Details Sections */}
      {(securityDeposits.length > 0 || rentEscalations.length > 0 || operatingCosts.length > 0 || tenantInducements.length > 0 || purchaseOptions.length > 0) && (
        <Accordion type="multiple" className="space-y-4">
          {/* Security Deposits */}
          {securityDeposits.length > 0 && (
            <AccordionItem value="deposits" className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-yellow-50">
                      <Shield className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">Security Deposits</h3>
                      <p className="text-sm text-gray-600">
                        {securityDeposits.length} deposit{securityDeposits.length === 1 ? '' : 's'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {securityDeposits.map((deposit) => (
                      <div key={deposit.deposit_id} className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">Base Amount</p>
                              <p className="font-semibold text-lg">{currency} {deposit.security_deposit_base?.toLocaleString() || 'N/A'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Taxes</p>
                              <p className="font-semibold text-lg">{currency} {deposit.security_deposit_taxes?.toLocaleString() || 'N/A'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Total</p>
                              <p className="font-semibold text-lg text-yellow-700">{currency} {deposit.security_deposit_total?.toLocaleString() || 'N/A'}</p>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="p-4 bg-white rounded border border-yellow-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Payment Method:</p>
                              <p className="text-sm text-gray-600">{deposit.security_deposit_payment_method || 'Not specified'}</p>
                            </div>
                            <div className="p-4 bg-white rounded border border-yellow-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Interest Terms:</p>
                              <p className="text-sm text-gray-600">{deposit.security_deposit_interest_accrual_terms || 'Not specified'}</p>
                            </div>
                          </div>

                          {deposit.security_deposit_conditions && (
                            <div className="p-4 bg-white rounded border border-yellow-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Conditions:</p>
                              <p className="text-sm text-gray-600">{deposit.security_deposit_conditions}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          )}

          {/* Rent Escalations */}
          {rentEscalations.length > 0 && (
            <AccordionItem value="escalations" className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-green-50">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">Rent Escalations</h3>
                      <p className="text-sm text-gray-600">
                        {rentEscalations.length} escalation{rentEscalations.length === 1 ? '' : 's'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {rentEscalations.map((escalation) => (
                      <div key={escalation.id} className="p-6 bg-green-50 border border-green-200 rounded-lg">
                        <div className="space-y-4">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold text-lg text-green-900">{escalation.escalation_type}</h4>
                              <p className="text-green-700">{escalation.frequency} • {escalation.review_mechanism}</p>
                            </div>
                            <Badge variant="outline">
                              {new Date(escalation.start_date).toLocaleDateString()} - {new Date(escalation.end_date).toLocaleDateString()}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Monthly Rent</p>
                              <p className="text-lg font-semibold text-green-700">{currency} {escalation.monthly_base_rent_for_period?.toLocaleString() || 'N/A'}</p>
                            </div>
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Annual Rent</p>
                              <p className="text-lg font-semibold text-green-700">{currency} {escalation.annual_base_rent_for_period?.toLocaleString() || 'N/A'}</p>
                            </div>
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Rate per Sq Ft</p>
                              <p className="text-lg font-semibold text-green-700">{currency} {escalation.rate_per_sqft_for_period || 'N/A'}</p>
                            </div>
                          </div>

                          {escalation.amount_or_formula && (
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Formula/Amount:</p>
                              <p className="text-sm text-gray-600">{escalation.amount_or_formula}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          )}
        </Accordion>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-yellow-50">
              <Shield className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Deposits</p>
              <p className="text-xl font-semibold">{securityDeposits.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <TrendingUp className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Escalations</p>
              <p className="text-xl font-semibold">{rentEscalations.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Receipt className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Op. Costs</p>
              <p className="text-xl font-semibold">{operatingCosts.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-50">
              <Gift className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Inducements</p>
              <p className="text-xl font-semibold">{tenantInducements.length}</p>
            </div>
          </div>
        </Card>
      </div>

      {(!financialTerms && totalFinancialItems === 0) && (
        <div className="text-center p-8 text-gray-500">
          No financial information available
        </div>
      )}
    </div>
  );
};

function getOrdinal(n: number): string {
  const s = ["th", "st", "nd", "rd"];
  const v = n % 100;
  return s[(v - 20) % 10] || s[v] || s[0];
}

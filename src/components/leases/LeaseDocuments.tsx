
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, Download, Eye, PenTool, Bell, Scale, DollarSign } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface DocumentAttachment {
  attachment_id: string;
  attachment_name: string;
  attachment_type: string;
  attachment_description: string;
  annex_reference: string;
}

interface Signature {
  signature_id: string;
  party_type: string;
  signatory_name: string;
  signatory_title: string;
  execution_date: string;
}

interface NoticeProvision {
  notice_id: string;
  party_type: string;
  notice_address: string;
  delivery_methods: string;
  service_domicile: string;
  contact_person_name: string;
  contact_person_title: string;
}

interface LegalProvision {
  legal_id: string;
  provision_type: string;
  provision_description: string;
  jurisdiction: string;
  requirements: string;
  is_confidentiality_clause: boolean;
  scope_of_confidentiality: string;
}

interface FinancialInfoProvision {
  id: string;
  party_required_to_provide: string;
  information_type: string;
  requesting_parties: string;
  notes: string;
}

interface LeaseDocumentsProps {
  documentAttachments: DocumentAttachment[];
  signatures?: Signature[];
  noticeProvisions?: NoticeProvision[];
  legalProvisions?: LegalProvision[];
  financialInfoProvisions?: FinancialInfoProvision[];
}

export const LeaseDocuments = ({ 
  documentAttachments, 
  signatures = [], 
  noticeProvisions = [], 
  legalProvisions = [], 
  financialInfoProvisions = [] 
}: LeaseDocumentsProps) => {
  
  const totalItems = documentAttachments.length + signatures.length + noticeProvisions.length + legalProvisions.length + financialInfoProvisions.length;
  
  const groupedAttachments = documentAttachments.reduce((acc, attachment) => {
    const type = attachment.attachment_type || 'General Documents';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(attachment);
    return acc;
  }, {} as Record<string, DocumentAttachment[]>);

  const getFileIcon = (name: string) => {
    const ext = name?.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf': return '📄';
      case 'doc':
      case 'docx': return '📝';
      case 'xls':
      case 'xlsx': return '📊';
      case 'jpg':
      case 'jpeg':
      case 'png': return '🖼️';
      default: return '📎';
    }
  };

  const getFileSize = () => {
    // Mock file sizes - in real app this would come from database
    const sizes = ['2.1 MB', '850 KB', '1.4 MB', '675 KB', '3.2 MB'];
    return sizes[Math.floor(Math.random() * sizes.length)];
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Documents & Procedures</h2>
          <p className="text-gray-600 mt-1">Documentation and procedural requirements</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {totalItems} Total Items
        </Badge>
      </div>

      {/* Documents and Procedures Sections */}
      <Accordion type="multiple" className="space-y-4">
        {/* Document Attachments */}
        {Object.entries(groupedAttachments).length > 0 && (
          <AccordionItem value="attachments" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-purple-50">
                    <FileText className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Document Attachments</h3>
                    <p className="text-sm text-gray-600">
                      {documentAttachments.length} attachment{documentAttachments.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {Object.entries(groupedAttachments).map(([type, attachments]) => (
                    <div key={type} className="space-y-3">
                      <h4 className="font-medium text-purple-900 border-b border-purple-200 pb-2">
                        {capitalizeFirstLetter(type)}
                      </h4>
                      {attachments.map((attachment) => (
                        <div key={attachment.attachment_id} className="p-4 border rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4 flex-1 min-w-0">
                              <div className="text-2xl">
                                {getFileIcon(attachment.attachment_name)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <h5 className="font-medium text-gray-900 truncate">
                                  {attachment.attachment_name || 'Unnamed Document'}
                                </h5>
                                <div className="flex items-center gap-4 mt-1">
                                  {attachment.annex_reference && (
                                    <Badge variant="outline" className="text-xs">
                                      Ref: {attachment.annex_reference}
                                    </Badge>
                                  )}
                                  <span className="text-xs text-gray-500">{getFileSize()}</span>
                                </div>
                                {attachment.attachment_description && (
                                  <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                                    {attachment.attachment_description}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Signatures */}
        {signatures.length > 0 && (
          <AccordionItem value="signatures" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-blue-50">
                    <PenTool className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Signatures</h3>
                    <p className="text-sm text-gray-600">
                      {signatures.length} signature{signatures.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {signatures.map((signature) => (
                    <div key={signature.signature_id} className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-blue-900">{signature.signatory_name}</h4>
                            <p className="text-blue-700">{signature.signatory_title}</p>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline">{signature.party_type}</Badge>
                            <p className="text-sm text-blue-700 mt-1">
                              {new Date(signature.execution_date).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Notice Provisions */}
        {noticeProvisions.length > 0 && (
          <AccordionItem value="notices" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-green-50">
                    <Bell className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Notice Provisions</h3>
                    <p className="text-sm text-gray-600">
                      {noticeProvisions.length} notice provision{noticeProvisions.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {noticeProvisions.map((notice) => (
                    <div key={notice.notice_id} className="p-6 bg-green-50 border border-green-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-green-900">{notice.party_type}</h4>
                            <p className="text-green-700">{notice.notice_address}</p>
                          </div>
                          <Badge variant="outline">{notice.service_domicile}</Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-white rounded border border-green-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Delivery Methods:</p>
                            <p className="text-sm text-gray-600">{notice.delivery_methods}</p>
                          </div>
                          {notice.contact_person_name && (
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Contact Person:</p>
                              <p className="text-sm text-gray-600">
                                {notice.contact_person_name}
                                {notice.contact_person_title && `, ${notice.contact_person_title}`}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Legal Provisions */}
        {legalProvisions.length > 0 && (
          <AccordionItem value="legal-provisions" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-indigo-50">
                    <Scale className="h-5 w-5 text-indigo-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Legal Provisions</h3>
                    <p className="text-sm text-gray-600">
                      {legalProvisions.length} legal provision{legalProvisions.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {legalProvisions.map((provision) => (
                    <div key={provision.legal_id} className="p-6 bg-indigo-50 border border-indigo-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-indigo-900">{provision.provision_type}</h4>
                            <p className="text-indigo-700 text-sm mt-1">{provision.provision_description}</p>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline">{provision.jurisdiction}</Badge>
                            {provision.is_confidentiality_clause && (
                              <Badge variant="secondary" className="ml-2">Confidential</Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {provision.requirements && (
                            <div className="p-4 bg-white rounded border border-indigo-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Requirements:</p>
                              <p className="text-sm text-gray-600">{provision.requirements}</p>
                            </div>
                          )}
                          {provision.scope_of_confidentiality && (
                            <div className="p-4 bg-white rounded border border-indigo-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Confidentiality Scope:</p>
                              <p className="text-sm text-gray-600">{provision.scope_of_confidentiality}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Financial Information Provisions */}
        {financialInfoProvisions.length > 0 && (
          <AccordionItem value="financial-info" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-yellow-50">
                    <DollarSign className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Financial Information Provisions</h3>
                    <p className="text-sm text-gray-600">
                      {financialInfoProvisions.length} financial provision{financialInfoProvisions.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {financialInfoProvisions.map((provision) => (
                    <div key={provision.id} className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-yellow-900">{provision.information_type}</h4>
                            <p className="text-yellow-700 text-sm mt-1">
                              Required from: {provision.party_required_to_provide}
                            </p>
                          </div>
                          <Badge variant="outline">{provision.requesting_parties}</Badge>
                        </div>
                        
                        {provision.notes && (
                          <div className="p-4 bg-white rounded border border-yellow-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Notes:</p>
                            <p className="text-sm text-gray-600">{provision.notes}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-50">
              <FileText className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Documents</p>
              <p className="text-xl font-semibold">{documentAttachments.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <PenTool className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Signatures</p>
              <p className="text-xl font-semibold">{signatures.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <Bell className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Notices</p>
              <p className="text-xl font-semibold">{noticeProvisions.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-indigo-50">
              <Scale className="h-5 w-5 text-indigo-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Legal</p>
              <p className="text-xl font-semibold">{legalProvisions.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-yellow-50">
              <DollarSign className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Financial Info</p>
              <p className="text-xl font-semibold">{financialInfoProvisions.length}</p>
            </div>
          </div>
        </Card>
      </div>

      {totalItems === 0 && (
        <div className="text-center p-8 text-gray-500">
          No documents or procedures information available
        </div>
      )}
    </div>
  );
};

import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';

interface LeaseQuickActionsProps {
  onRecordPayment: () => void;
  onScheduleMaintenance: () => void;
  onUploadDocument: () => void;
}

export const LeaseQuickActions = ({ 
  onRecordPayment, 
  onScheduleMaintenance, 
  onUploadDocument 
}: LeaseQuickActionsProps) => {
  return (
    <div className="flex items-center gap-3 mt-4">
      <Button size="sm" onClick={onRecordPayment} className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Record Payment
      </Button>
      <Button size="sm" variant="outline" onClick={onScheduleMaintenance} className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Schedule Maintenance
      </Button>
      <Button size="sm" variant="outline" onClick={onUploadDocument} className="flex items-center gap-2">
        <Plus className="h-4 w-4" />
        Upload Document
      </Button>
    </div>
  );
};

// Default handlers
export const defaultQuickActionHandlers = {
  handleRecordPayment: () => {
    toast.success('Payment recording modal would open here');
  },
  handleScheduleMaintenance: () => {
    toast.success('Maintenance scheduling modal would open here');
  },
  handleUploadDocument: () => {
    toast.success('Document upload modal would open here');
  }
};

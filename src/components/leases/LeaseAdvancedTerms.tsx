import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRightLeft, Eye, SignpostBig, Scale, AlertTriangle, Clock, DollarSign, Shield, Zap, Heart } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface AdvancedTermsProps {
  assignmentSublettingTerms: any[];
  accessInspectionRights: any[];
  signageProvisions: any[];
  defaultRemedies: any[];
  forceMajeureProvisions: any[];
  healthEmergencyProvisions: any[];
  insuranceLiabilityTerms: any[];
}

export const LeaseAdvancedTerms = ({ 
  assignmentSublettingTerms,
  accessInspectionRights,
  signageProvisions,
  defaultRemedies,
  forceMajeureProvisions,
  healthEmergencyProvisions,
  insuranceLiabilityTerms
}: AdvancedTermsProps) => {
  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const getTotalTermsCount = () => {
    return assignmentSublettingTerms.length + 
           accessInspectionRights.length + 
           signageProvisions.length + 
           defaultRemedies.length +
           forceMajeureProvisions.length +
           healthEmergencyProvisions.length +
           insuranceLiabilityTerms.length;
  };

  if (getTotalTermsCount() === 0) {
    return (
      <Card>
        <CardContent className="text-center p-12">
          <Scale className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Advanced Terms</h3>
          <p className="text-gray-500">No advanced terms have been defined for this lease.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Advanced Terms & Conditions</h2>
          <p className="text-gray-600 mt-1">Assignment, access, signage, and legal provisions</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {getTotalTermsCount()} {getTotalTermsCount() === 1 ? 'Term' : 'Terms'}
        </Badge>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {/* Assignment & Subletting Terms */}
        {assignmentSublettingTerms.length > 0 && (
          <AccordionItem value="assignment" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-blue-50">
                    <ArrowRightLeft className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Assignment & Subletting</h3>
                    <p className="text-sm text-gray-600">
                      {assignmentSublettingTerms.length} {assignmentSublettingTerms.length === 1 ? 'term' : 'terms'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {assignmentSublettingTerms.map((term, index) => (
                    <div key={index} className="p-6 border rounded-lg bg-gray-50">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline">{term.transfer_type}</Badge>
                          {term.consent_required && <Badge variant="secondary">Consent Required</Badge>}
                          {term.landlord_recapture_right && <Badge variant="destructive">Recapture Right</Badge>}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                            <Clock className="h-5 w-5 text-blue-500" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">Response Time</p>
                              <p className="text-gray-700 mt-1">{term.response_time_days} days</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                            <DollarSign className="h-5 w-5 text-green-500" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">Admin Fee</p>
                              <p className="text-gray-700 mt-1">${term.admin_fee?.toLocaleString()}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                            <AlertTriangle className="h-5 w-5 text-orange-500" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">Refusal Reasons</p>
                              <p className="text-gray-700 mt-1 text-xs">{term.refusal_reasons || 'Not specified'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Access & Inspection Rights */}
        {accessInspectionRights.length > 0 && (
          <AccordionItem value="access" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-green-50">
                    <Eye className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Access & Inspection Rights</h3>
                    <p className="text-sm text-gray-600">
                      {accessInspectionRights.length} {accessInspectionRights.length === 1 ? 'right' : 'rights'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {accessInspectionRights.map((right, index) => (
                    <div key={index} className="p-6 border rounded-lg bg-gray-50">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline">{right.access_type}</Badge>
                        </div>
                        
                        <p className="text-gray-900 font-medium mb-3">
                          {right.access_rights_description}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-green-50 rounded-lg border-l-4 border-l-green-400">
                            <p className="font-medium text-green-900 text-sm">Notice Requirements</p>
                            <p className="text-green-800 mt-1 text-sm">{right.notice_requirements}</p>
                          </div>
                          
                          <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-l-blue-400">
                            <p className="font-medium text-blue-900 text-sm">Time Restrictions</p>
                            <p className="text-blue-800 mt-1 text-sm">{right.time_restrictions}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Signage Provisions */}
        {signageProvisions.length > 0 && (
          <AccordionItem value="signage" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-purple-50">
                    <SignpostBig className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Signage Provisions</h3>
                    <p className="text-sm text-gray-600">
                      {signageProvisions.length} {signageProvisions.length === 1 ? 'provision' : 'provisions'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {signageProvisions.map((signage, index) => (
                    <div key={index} className="p-6 border rounded-lg bg-gray-50">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline">{signage.signage_type}</Badge>
                          {signage.approval_required && <Badge variant="secondary">Approval Required</Badge>}
                        </div>
                        
                        <p className="text-gray-900 font-medium mb-3">
                          {signage.signage_restrictions}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-purple-50 rounded-lg border-l-4 border-l-purple-400">
                            <p className="font-medium text-purple-900 text-sm">Cost Responsibility</p>
                            <p className="text-purple-800 mt-1 text-sm">{signage.cost_responsibility}</p>
                          </div>
                          
                          <div className="p-4 bg-orange-50 rounded-lg border-l-4 border-l-orange-400">
                            <p className="font-medium text-orange-900 text-sm">Removal Requirements</p>
                            <p className="text-orange-800 mt-1 text-sm">{signage.removal_requirements}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Default & Remedies */}
        {defaultRemedies.length > 0 && (
          <AccordionItem value="defaults" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-red-50">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Default & Remedies</h3>
                    <p className="text-sm text-gray-600">
                      {defaultRemedies.length} {defaultRemedies.length === 1 ? 'remedy' : 'remedies'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {defaultRemedies.map((remedy, index) => (
                    <div key={index} className="p-6 border rounded-lg bg-red-50">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="destructive">{remedy.default_type}</Badge>
                        </div>
                        
                        <p className="text-gray-900 font-medium mb-3">
                          {remedy.default_description}
                        </p>
                        
                        <p className="text-red-800 font-medium mb-3">
                          {remedy.remedy_description}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="p-3 bg-white rounded-lg border">
                            <p className="text-sm font-medium text-gray-900">Cure Period</p>
                            <p className="text-gray-700 mt-1">{remedy.cure_period_days} days</p>
                          </div>
                          
                          <div className="p-3 bg-white rounded-lg border">
                            <p className="text-sm font-medium text-gray-900">Accelerated Rent</p>
                            <p className="text-gray-700 mt-1">{remedy.accelerated_rent_months} months</p>
                          </div>

                          <div className="p-3 bg-white rounded-lg border">
                            <p className="text-sm font-medium text-gray-900">Interest</p>
                            <p className="text-gray-700 mt-1">{remedy.interest_calculation}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Force Majeure */}
        {forceMajeureProvisions.length > 0 && (
          <AccordionItem value="force-majeure" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-yellow-50">
                    <Zap className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Force Majeure</h3>
                    <p className="text-sm text-gray-600">
                      {forceMajeureProvisions.length} {forceMajeureProvisions.length === 1 ? 'provision' : 'provisions'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {forceMajeureProvisions.map((provision, index) => (
                    <div key={index} className="p-6 border rounded-lg bg-yellow-50">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline">{provision.event_type}</Badge>
                          {provision.rent_payment_exemption && <Badge variant="secondary">Rent Exemption</Badge>}
                        </div>
                        
                        <p className="text-gray-900 font-medium mb-3">
                          {provision.event_description}
                        </p>
                        
                        <p className="text-yellow-800 mb-3">
                          {provision.effect_on_obligations}
                        </p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Health Emergency */}
        {healthEmergencyProvisions.length > 0 && (
          <AccordionItem value="health-emergency" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-pink-50">
                    <Heart className="h-5 w-5 text-pink-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Health Emergency</h3>
                    <p className="text-sm text-gray-600">
                      {healthEmergencyProvisions.length} {healthEmergencyProvisions.length === 1 ? 'provision' : 'provisions'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {healthEmergencyProvisions.map((provision, index) => (
                    <div key={index} className="p-6 border rounded-lg bg-pink-50">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline">{provision.provision_type}</Badge>
                        </div>
                        
                        <p className="text-gray-900 font-medium mb-3">
                          {provision.provision_description}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-white rounded-lg border">
                            <p className="text-sm font-medium text-gray-900">Landlord Rights</p>
                            <p className="text-gray-700 mt-1 text-sm">{provision.landlord_rights}</p>
                          </div>
                          
                          <div className="p-4 bg-white rounded-lg border">
                            <p className="text-sm font-medium text-gray-900">Liability Exclusions</p>
                            <p className="text-gray-700 mt-1 text-sm">{provision.liability_exclusions}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Insurance & Liability */}
        {insuranceLiabilityTerms.length > 0 && (
          <AccordionItem value="insurance" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-teal-50">
                    <Shield className="h-5 w-5 text-teal-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Insurance & Liability</h3>
                    <p className="text-sm text-gray-600">
                      {insuranceLiabilityTerms.length} {insuranceLiabilityTerms.length === 1 ? 'term' : 'terms'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {insuranceLiabilityTerms.map((term, index) => (
                    <div key={index} className="p-6 border rounded-lg bg-teal-50">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline">{term.provision_type}</Badge>
                        </div>
                        
                        <p className="text-gray-900 font-medium mb-3">
                          {term.provision_description}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-white rounded-lg border">
                            <p className="text-sm font-medium text-gray-900">Responsible Party</p>
                            <p className="text-gray-700 mt-1">{term.responsible_party}</p>
                          </div>
                          
                          <div className="p-4 bg-white rounded-lg border">
                            <p className="text-sm font-medium text-gray-900">Exclusions</p>
                            <p className="text-gray-700 mt-1 text-sm">{term.exclusions}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}
      </Accordion>
    </div>
  );
};

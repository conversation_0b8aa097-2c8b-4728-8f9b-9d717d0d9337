import { useState } from 'react';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { LeaseTableRow } from './table/LeaseTableRow';
import { LeaseBulkActions } from './table/LeaseBulkActions';

interface Lease {
  lease_id: string;
  lease_status: string;
  commencement_date: string;
  end_date: string;
  lease_term_months: number;
  parties: Array<{
    party_name: string;
    party_type: string;
    address_street: string;
    address_city: string;
  }>;
  properties: Array<{
    building_address: string;
    leased_premises_address: string;
    rental_area_sqft: number;
  }>;
  financial_terms: Array<{
    monthly_base_rent: number;
    annual_base_rent: number;
    rent_currency: string;
  }>;
}

interface LeasesDataTableProps {
  leases: Lease[];
}

export const LeasesDataTable = ({ leases }: LeasesDataTableProps) => {
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(leases.map(lease => lease.lease_id));
    } else {
      setSelectedRows([]);
    }
  };

  const handleSelectRow = (leaseId: string, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, leaseId]);
    } else {
      setSelectedRows(selectedRows.filter(id => id !== leaseId));
    }
  };

  const handleClearSelection = () => {
    setSelectedRows([]);
  };

  return (
    <div className="space-y-4">
      <LeaseBulkActions 
        selectedCount={selectedRows.length}
        onClearSelection={handleClearSelection}
      />

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedRows.length === leases.length && leases.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Tenant</TableHead>
              <TableHead>Property</TableHead>
              <TableHead>Monthly Rent</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Days Until Expiry</TableHead>
              <TableHead>Term</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {leases.map((lease) => (
              <LeaseTableRow
                key={lease.lease_id}
                lease={lease}
                isSelected={selectedRows.includes(lease.lease_id)}
                onSelect={handleSelectRow}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      {leases.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No leases found. Try adjusting your filters or create a new lease.</p>
        </div>
      )}
    </div>
  );
};

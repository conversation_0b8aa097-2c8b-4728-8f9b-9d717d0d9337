import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Scale, AlertTriangle, FileX, Zap, Leaf, <PERSON>Check, AlertCircle } from 'lucide-react';
import { Accordion, Accordion<PERSON>ontent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface InsuranceTerm {
  insurance_id: string;
  provision_type: string;
  responsible_party: string;
  provision_description: string;
  exclusions: string;
  minimum_coverage_amount: number;
  named_insureds: string;
  certificate_of_insurance_requirement: boolean;
  deductible_limit: number;
  insurance_types_required_details: string;
  waiver_of_subrogation_clause: boolean;
  insurer_notification_period_days: number;
  consequences_of_non_compliance: string;
  landlord_insurance_exclusions: string;
}

interface DefaultRemedy {
  default_id: string;
  default_type: string;
  cure_period_days: number;
  default_description: string;
  remedy_description: string;
  accelerated_rent_months: number;
  interest_calculation: string;
  is_monetary_default: boolean;
  is_non_monetary_default: boolean;
  specific_non_monetary_default_type: string;
  admin_fee_on_remedy_costs_rate: number;
  specific_consequences_details: string;
}

interface DisputeResolution {
  force_majeure_id: string;
  method_type: string;
  governing_law: string;
  jurisdiction: string;
  arbitration_rules: string;
  mediation_requirements: string;
  notice_period_before_action: number;
}

interface ForceMajeure {
  force_majeure_id: string;
  event_type: string;
  event_description: string;
  effect_on_obligations: string;
  rent_payment_exemption: boolean;
}

interface HealthEmergency {
  health_emergency_id: string;
  provision_type: string;
  provision_description: string;
  landlord_rights: string;
  liability_exclusions: string;
}

interface Environmental {
  id: string;
  provision_type: string;
  description: string;
  responsible_party: string;
  indemnification_terms: string;
}

interface Indemnification {
  id: string;
  indemnifying_party: string;
  indemnified_party: string;
  scope_of_indemnity: string;
  triggering_events: string;
  notes: string;
}

interface Estoppel {
  id: string;
  response_time_days: number;
  required_information_details: string;
  party_requesting: string;
  party_providing: string;
}

interface Subordination {
  id: string;
  subordination_type: string;
  attornment_requirement: boolean;
  document_delivery_time_days: number;
  notes: string;
}

interface LeaseLegalTermsProps {
  lease: any;
  insuranceTerms?: InsuranceTerm[];
  defaultRemedies?: DefaultRemedy[];
  disputeResolution?: DisputeResolution[];
  forceMajeure?: ForceMajeure[];
  healthEmergency?: HealthEmergency[];
  environmental?: Environmental[];
  indemnification?: Indemnification[];
  estoppel?: Estoppel[];
  subordination?: Subordination[];
}

export const LeaseLegalTerms = ({ 
  lease, 
  insuranceTerms = [], 
  defaultRemedies = [], 
  disputeResolution = [], 
  forceMajeure = [], 
  healthEmergency = [], 
  environmental = [], 
  indemnification = [], 
  estoppel = [], 
  subordination = [] 
}: LeaseLegalTermsProps) => {
  
  const totalItems = insuranceTerms.length + defaultRemedies.length + disputeResolution.length + forceMajeure.length + healthEmergency.length + environmental.length + indemnification.length + estoppel.length + subordination.length;

  const getSeverityLevel = (description: string, type: string = '') => {
    if (!description) return 'low';
    const lowerDesc = description.toLowerCase();
    const lowerType = type.toLowerCase();
    
    if (lowerDesc.includes('termination') || lowerDesc.includes('eviction') || lowerType.includes('default')) return 'high';
    if (lowerDesc.includes('fine') || lowerDesc.includes('penalty') || lowerType.includes('insurance')) return 'medium';
    return 'low';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-50 border-red-200 text-red-900';
      case 'medium': return 'bg-yellow-50 border-yellow-200 text-yellow-900';
      default: return 'bg-blue-50 border-blue-200 text-blue-900';
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Legal & Risk Management</h2>
          <p className="text-gray-600 mt-1">Legal protections and risk mitigation provisions</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {totalItems} Legal Components
        </Badge>
      </div>

      {/* Legal Provisions */}
      <Accordion type="multiple" className="space-y-4">
        {/* Insurance & Liability Terms */}
        {insuranceTerms.length > 0 && (
          <AccordionItem value="insurance" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-blue-50">
                    <Shield className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Insurance & Liability</h3>
                    <p className="text-sm text-gray-600">
                      {insuranceTerms.length} insurance provision{insuranceTerms.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {insuranceTerms.map((term) => {
                    const severity = getSeverityLevel(term.provision_description, 'insurance');
                    return (
                      <div key={term.insurance_id} className={`p-6 border rounded-lg ${getSeverityColor(severity)}`}>
                        <div className="space-y-4">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold text-lg">{term.provision_type}</h4>
                              <p className="text-sm opacity-80 mt-1">{term.provision_description}</p>
                            </div>
                            <div className="text-right">
                              <Badge variant="outline">
                                {term.responsible_party || 'Not specified'}
                              </Badge>
                              {term.minimum_coverage_amount && (
                                <p className="text-xs mt-1 opacity-70">
                                  Min: ${term.minimum_coverage_amount.toLocaleString()}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {term.insurance_types_required_details && (
                              <div className="p-4 bg-white rounded border">
                                <p className="text-sm font-medium mb-2">Required Insurance Types:</p>
                                <p className="text-sm opacity-80">{term.insurance_types_required_details}</p>
                              </div>
                            )}
                            {term.named_insureds && (
                              <div className="p-4 bg-white rounded border">
                                <p className="text-sm font-medium mb-2">Named Insureds:</p>
                                <p className="text-sm opacity-80">{term.named_insureds}</p>
                              </div>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {term.certificate_of_insurance_requirement && (
                              <div className="p-3 bg-white rounded border text-center">
                                <FileCheck className="h-4 w-4 mx-auto mb-1 text-green-600" />
                                <p className="text-xs font-medium">Certificate Required</p>
                              </div>
                            )}
                            {term.waiver_of_subrogation_clause && (
                              <div className="p-3 bg-white rounded border text-center">
                                <Shield className="h-4 w-4 mx-auto mb-1 text-blue-600" />
                                <p className="text-xs font-medium">Waiver of Subrogation</p>
                              </div>
                            )}
                            {term.insurer_notification_period_days && (
                              <div className="p-3 bg-white rounded border text-center">
                                <AlertCircle className="h-4 w-4 mx-auto mb-1 text-orange-600" />
                                <p className="text-xs font-medium">{term.insurer_notification_period_days} days notice</p>
                              </div>
                            )}
                          </div>

                          {term.consequences_of_non_compliance && (
                            <div className="p-4 bg-white rounded border border-red-200">
                              <p className="text-sm font-medium mb-2 text-red-700">Consequences of Non-Compliance:</p>
                              <p className="text-sm text-red-600">{term.consequences_of_non_compliance}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Default Remedies */}
        {defaultRemedies.length > 0 && (
          <AccordionItem value="defaults" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-red-50">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Default Remedies</h3>
                    <p className="text-sm text-gray-600">
                      {defaultRemedies.length} default type{defaultRemedies.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {defaultRemedies.map((remedy) => (
                    <div key={remedy.default_id} className="p-6 bg-red-50 border border-red-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-red-900">{remedy.default_type}</h4>
                            <p className="text-red-700 text-sm mt-1">{remedy.default_description}</p>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline" className="mb-1">
                              {remedy.cure_period_days} days cure
                            </Badge>
                            {remedy.accelerated_rent_months && (
                              <p className="text-xs text-red-700">
                                {remedy.accelerated_rent_months} months accelerated
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-white rounded border border-red-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Remedy Description:</p>
                            <p className="text-sm text-gray-600">{remedy.remedy_description}</p>
                          </div>
                          {remedy.interest_calculation && (
                            <div className="p-4 bg-white rounded border border-red-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Interest Calculation:</p>
                              <p className="text-sm text-gray-600">{remedy.interest_calculation}</p>
                            </div>
                          )}
                        </div>

                        {remedy.specific_consequences_details && (
                          <div className="p-4 bg-white rounded border border-red-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Specific Consequences:</p>
                            <p className="text-sm text-gray-600">{remedy.specific_consequences_details}</p>
                          </div>
                        )}

                        <div className="flex items-center gap-4 text-sm">
                          {remedy.is_monetary_default && (
                            <Badge variant="secondary">Monetary Default</Badge>
                          )}
                          {remedy.is_non_monetary_default && (
                            <Badge variant="secondary">Non-Monetary Default</Badge>
                          )}
                          {remedy.admin_fee_on_remedy_costs_rate && (
                            <span className="text-red-700">Admin fee: {remedy.admin_fee_on_remedy_costs_rate}%</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Dispute Resolution */}
        {disputeResolution.length > 0 && (
          <AccordionItem value="disputes" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-purple-50">
                    <Scale className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Dispute Resolution</h3>
                    <p className="text-sm text-gray-600">
                      {disputeResolution.length} resolution method{disputeResolution.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {disputeResolution.map((dispute) => (
                    <div key={dispute.force_majeure_id} className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-purple-900">{dispute.method_type}</h4>
                            <p className="text-purple-700 text-sm mt-1">Jurisdiction: {dispute.jurisdiction}</p>
                          </div>
                          <Badge variant="outline">
                            {dispute.notice_period_before_action} days notice
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-white rounded border border-purple-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Governing Law:</p>
                            <p className="text-sm text-gray-600">{dispute.governing_law}</p>
                          </div>
                          {dispute.arbitration_rules && (
                            <div className="p-4 bg-white rounded border border-purple-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Arbitration Rules:</p>
                              <p className="text-sm text-gray-600">{dispute.arbitration_rules}</p>
                            </div>
                          )}
                        </div>

                        {dispute.mediation_requirements && (
                          <div className="p-4 bg-white rounded border border-purple-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Mediation Requirements:</p>
                            <p className="text-sm text-gray-600">{dispute.mediation_requirements}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Force Majeure */}
        {forceMajeure.length > 0 && (
          <AccordionItem value="force-majeure" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-orange-50">
                    <Zap className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Force Majeure</h3>
                    <p className="text-sm text-gray-600">
                      {forceMajeure.length} force majeure event{forceMajeure.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {forceMajeure.map((event) => (
                    <div key={event.force_majeure_id} className="p-6 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-orange-900">{event.event_type}</h4>
                            <p className="text-orange-700 text-sm mt-1">{event.event_description}</p>
                          </div>
                          {event.rent_payment_exemption && (
                            <Badge variant="outline">Rent Exemption</Badge>
                          )}
                        </div>
                        
                        <div className="p-4 bg-white rounded border border-orange-200">
                          <p className="text-sm text-gray-700 font-medium mb-2">Effect on Obligations:</p>
                          <p className="text-sm text-gray-600">{event.effect_on_obligations}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Environmental Provisions */}
        {environmental.length > 0 && (
          <AccordionItem value="environmental" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-green-50">
                    <Leaf className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Environmental Provisions</h3>
                    <p className="text-sm text-gray-600">
                      {environmental.length} environmental provision{environmental.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {environmental.map((env) => (
                    <div key={env.id} className="p-6 bg-green-50 border border-green-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-green-900">{env.provision_type}</h4>
                            <p className="text-green-700 text-sm mt-1">{env.description}</p>
                          </div>
                          <Badge variant="outline">
                            {env.responsible_party}
                          </Badge>
                        </div>
                        
                        {env.indemnification_terms && (
                          <div className="p-4 bg-white rounded border border-green-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Indemnification Terms:</p>
                            <p className="text-sm text-gray-600">{env.indemnification_terms}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Health Emergency Provisions */}
        {healthEmergency.length > 0 && (
          <AccordionItem value="health-emergency" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-pink-50">
                    <AlertTriangle className="h-5 w-5 text-pink-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Health Emergency Provisions</h3>
                    <p className="text-sm text-gray-600">
                      {healthEmergency.length} health provision{healthEmergency.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {healthEmergency.map((health) => (
                    <div key={health.health_emergency_id} className="p-6 bg-pink-50 border border-pink-200 rounded-lg">
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-lg text-pink-900">{health.provision_type}</h4>
                          <p className="text-pink-700 text-sm mt-1">{health.provision_description}</p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {health.landlord_rights && (
                            <div className="p-4 bg-white rounded border border-pink-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Landlord Rights:</p>
                              <p className="text-sm text-gray-600">{health.landlord_rights}</p>
                            </div>
                          )}
                          {health.liability_exclusions && (
                            <div className="p-4 bg-white rounded border border-pink-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Liability Exclusions:</p>
                              <p className="text-sm text-gray-600">{health.liability_exclusions}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Shield className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Insurance</p>
              <p className="text-xl font-semibold">{insuranceTerms.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-red-50">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Defaults</p>
              <p className="text-xl font-semibold">{defaultRemedies.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-50">
              <Scale className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Disputes</p>
              <p className="text-xl font-semibold">{disputeResolution.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-orange-50">
              <Zap className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Force Majeure</p>
              <p className="text-xl font-semibold">{forceMajeure.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <Leaf className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Environmental</p>
              <p className="text-xl font-semibold">{environmental.length}</p>
            </div>
          </div>
        </Card>
      </div>

      {totalItems === 0 && (
        <div className="text-center p-8 text-gray-500">
          No legal terms information available
        </div>
      )}
    </div>
  );
};

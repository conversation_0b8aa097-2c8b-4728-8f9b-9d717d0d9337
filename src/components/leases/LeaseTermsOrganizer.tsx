import { Scale, Settings, AlertCircle, Building, Gavel, ShieldCheck, Handshake, Eye, Flag, Zap, Shield, AlertT<PERSON>gle, <PERSON>, Wrench } from 'lucide-react';

interface LeaseTermsOrganizerProps {
  lease: any;
}

export const LeaseTermsOrganizer = ({ lease }: LeaseTermsOrganizerProps) => {
  const organizeTermsAndConditions = () => {
    const categories = [
      {
        id: 'legal',
        title: 'Legal & Compliance',
        icon: Scale,
        color: 'red',
        description: 'Default remedies, legal provisions, and compliance requirements',
        totalItems: (lease.default_remedies?.length || 0) + (lease.compliance_requirements?.length || 0),
        items: [
          { type: 'Default Remedies', data: lease.default_remedies || [], icon: Gavel },
          { type: 'Compliance Requirements', data: lease.compliance_requirements || [], icon: ShieldCheck }
        ]
      },
      {
        id: 'operational',
        title: 'Operational Terms',
        icon: Settings,
        color: 'blue',
        description: 'Assignment, access rights, and operational guidelines',
        totalItems: (lease.assignment_subletting_terms?.length || 0) + (lease.access_inspection_rights?.length || 0) + (lease.signage_provisions?.length || 0),
        items: [
          { type: 'Assignment & Subletting', data: lease.assignment_subletting_terms || [], icon: Handshake },
          { type: 'Access & Inspection', data: lease.access_inspection_rights || [], icon: Eye },
          { type: 'Signage Provisions', data: lease.signage_provisions || [], icon: Flag }
        ]
      },
      {
        id: 'emergency',
        title: 'Emergency & Risk',
        icon: AlertCircle,
        color: 'orange',
        description: 'Force majeure, health emergency, and risk management',
        totalItems: (lease.force_majeure_provisions?.length || 0) + (lease.health_emergency_provisions?.length || 0) + (lease.insurance_liability_terms?.length || 0),
        items: [
          { type: 'Force Majeure', data: lease.force_majeure_provisions || [], icon: Zap },
          { type: 'Health Emergency', data: lease.health_emergency_provisions || [], icon: AlertCircle },
          { type: 'Insurance & Liability', data: lease.insurance_liability_terms || [], icon: Shield }
        ]
      },
      {
        id: 'property',
        title: 'Property & Improvements',
        icon: Building,
        color: 'green',
        description: 'Use restrictions, improvements, and property management',
        totalItems: (lease.use_restrictions?.length || 0) + (lease.improvement_terms?.length || 0) + (lease.maintenance_obligations?.length || 0),
        items: [
          { type: 'Use Restrictions', data: lease.use_restrictions || [], icon: AlertTriangle },
          { type: 'Improvement Terms', data: lease.improvement_terms || [], icon: Hammer },
          { type: 'Maintenance Obligations', data: lease.maintenance_obligations || [], icon: Wrench }
        ]
      }
    ];

    return categories;
  };

  return {
    termsCategories: organizeTermsAndConditions()
  };
};

export const useLeaseTermsOrganizer = (lease: any) => {
  const organizer = LeaseTermsOrganizer({ lease });
  return organizer.termsCategories;
};

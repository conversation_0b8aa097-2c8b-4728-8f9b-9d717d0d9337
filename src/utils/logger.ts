/**
 * Production-safe logger utility
 * Removes console logs in production builds
 */

interface Logger {
  log: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
  info: (...args: any[]) => void;
  debug: (...args: any[]) => void;
}

const createLogger = (): Logger => {
  const isDev = import.meta.env.DEV;
  
  if (isDev) {
    return {
      log: console.log.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      info: console.info.bind(console),
      debug: console.debug.bind(console),
    };
  }
  
  // In production, return no-op functions except for errors
  return {
    log: () => {},
    warn: () => {},
    error: console.error.bind(console), // Keep errors in production for debugging
    info: () => {},
    debug: () => {},
  };
};

export const logger = createLogger();

// For backwards compatibility with existing console usage
export const safeConsole = logger;

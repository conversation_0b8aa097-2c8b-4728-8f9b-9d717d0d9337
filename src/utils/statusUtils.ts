
export const normalizeStatus = (status: string): 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled' => {
  if (!status) return 'inactive';
  
  const statusLower = status.toLowerCase();
  
  if (statusLower.includes('active') || statusLower === 'current') return 'active';
  if (statusLower.includes('inactive') || statusLower === 'terminated') return 'inactive';
  if (statusLower.includes('pending') || statusLower === 'draft') return 'pending';
  if (statusLower.includes('overdue') || statusLower === 'expired') return 'overdue';
  if (statusLower.includes('completed') || statusLower === 'signed') return 'completed';
  if (statusLower.includes('cancelled') || statusLower === 'void') return 'cancelled';
  
  return 'inactive';
};

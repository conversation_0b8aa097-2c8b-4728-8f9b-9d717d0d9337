
export interface Tenant {
  party_id: string;
  party_name: string;
  address_street: string;
  address_city: string;
  address_province: string;
  address_postal_code: string;
  representative_name?: string;
  representative_title?: string;
  email?: string;
  phone_number?: string;
  lease_id: string;
  lease_status: string;
  commencement_date: string;
  end_date: string;
  monthly_rent: number;
  rent_currency: string;
  property_address: string;
  rental_area_sqft: number;
  days_until_expiry: number;
  lease_term_months: number;
}

export interface TenantStats {
  total: number;
  active: number;
  expiring: number;
  expired: number;
}

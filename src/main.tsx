import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

createRoot(document.getElementById("root")!).render(<App />);

// Register service worker for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
        
        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                  // New update available
                  showUpdateAvailable();
                } else {
                  // Content is cached for offline use
                  console.log('Content is now available offline!');
                }
              }
            });
          }
        });
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });

    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'CACHE_UPDATED') {
        showUpdateAvailable();
      }
    });
  });
}

// PWA install prompt
let deferredPrompt: any;

window.addEventListener('beforeinstallprompt', (e) => {
  console.log('PWA install prompt triggered');
  // Prevent Chrome 67 and earlier from automatically showing the prompt
  e.preventDefault();
  // Stash the event so it can be triggered later
  deferredPrompt = e;
  // Show install button/banner
  showInstallPromotion();
});

window.addEventListener('appinstalled', (evt) => {
  console.log('PWA was installed');
  // Hide install promotion
  hideInstallPromotion();
  // Track the install
  if (typeof window !== 'undefined' && 'gtag' in window) {
    (window as any).gtag('event', 'pwa_installed', {
      event_category: 'engagement',
      event_label: 'pwa'
    });
  }
});

function showUpdateAvailable() {
  // Create update notification
  const updateBanner = document.createElement('div');
  updateBanner.id = 'update-banner';
  updateBanner.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #059669;
    color: white;
    padding: 12px;
    text-align: center;
    z-index: 9999;
    font-family: system-ui, sans-serif;
  `;
  updateBanner.innerHTML = `
    <span>New version available!</span>
    <button id="update-btn" style="margin-left: 16px; background: white; color: #059669; border: none; padding: 4px 12px; border-radius: 4px; cursor: pointer;">
      Refresh
    </button>
    <button id="dismiss-btn" style="margin-left: 8px; background: transparent; color: white; border: 1px solid white; padding: 4px 12px; border-radius: 4px; cursor: pointer;">
      Later
    </button>
  `;
  
  document.body.prepend(updateBanner);
  
  document.getElementById('update-btn')?.addEventListener('click', () => {
    navigator.serviceWorker.getRegistration().then((registration) => {
      if (registration?.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    });
  });
  
  document.getElementById('dismiss-btn')?.addEventListener('click', () => {
    updateBanner.remove();
  });
}

function showInstallPromotion() {
  // Only show install prompt if not already installed
  if (window.matchMedia('(display-mode: standalone)').matches) {
    return;
  }
  
  const installBanner = document.createElement('div');
  installBanner.id = 'install-banner';
  installBanner.style.cssText = `
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #1f2937;
    color: white;
    padding: 16px;
    text-align: center;
    z-index: 9999;
    font-family: system-ui, sans-serif;
  `;
  installBanner.innerHTML = `
    <div style="max-width: 600px; margin: 0 auto;">
      <p style="margin: 0 0 12px 0;">Install Brasswater for quick access and offline features!</p>
      <button id="install-btn" style="background: #059669; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px;">
        Install App
      </button>
      <button id="install-dismiss-btn" style="background: transparent; color: white; border: 1px solid white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
        Not Now
      </button>
    </div>
  `;
  
  document.body.appendChild(installBanner);
  
  document.getElementById('install-btn')?.addEventListener('click', async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      console.log(`User response to install prompt: ${outcome}`);
      deferredPrompt = null;
      installBanner.remove();
    }
  });
  
  document.getElementById('install-dismiss-btn')?.addEventListener('click', () => {
    installBanner.remove();
    // Don't show again for this session
    sessionStorage.setItem('install-dismissed', 'true');
  });
  
  // Auto-dismiss after 10 seconds
  setTimeout(() => {
    if (document.getElementById('install-banner')) {
      installBanner.remove();
    }
  }, 10000);
  
  // Don't show if user dismissed in this session
  if (sessionStorage.getItem('install-dismissed')) {
    installBanner.remove();
  }
}

function hideInstallPromotion() {
  const installBanner = document.getElementById('install-banner');
  if (installBanner) {
    installBanner.remove();
  }
}

// Add viewport meta tag for mobile optimization
const viewport = document.querySelector('meta[name="viewport"]');
if (!viewport) {
  const meta = document.createElement('meta');
  meta.name = 'viewport';
  meta.content = 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover';
  document.head.appendChild(meta);
}

// Add theme color meta tags
const themeColor = document.querySelector('meta[name="theme-color"]');
if (!themeColor) {
  const meta = document.createElement('meta');
  meta.name = 'theme-color';
  meta.content = '#059669';
  document.head.appendChild(meta);
}

// Add apple specific meta tags for iOS
const appleMobileWebAppCapable = document.querySelector('meta[name="apple-mobile-web-app-capable"]');
if (!appleMobileWebAppCapable) {
  const meta = document.createElement('meta');
  meta.name = 'apple-mobile-web-app-capable';
  meta.content = 'yes';
  document.head.appendChild(meta);
}

const appleMobileWebAppStatusBarStyle = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');
if (!appleMobileWebAppStatusBarStyle) {
  const meta = document.createElement('meta');
  meta.name = 'apple-mobile-web-app-status-bar-style';
  meta.content = 'black-translucent';
  document.head.appendChild(meta);
}

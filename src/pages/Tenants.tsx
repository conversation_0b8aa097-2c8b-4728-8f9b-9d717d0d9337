
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { EmptyState } from '@/components/ui/empty-state';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Users, Plus } from 'lucide-react';
import { TenantCard } from '@/components/tenants/TenantCard';
import { TenantStatsCards } from '@/components/tenants/TenantStatsCards';
import { TenantsFilters } from '@/components/tenants/TenantsFilters';
import { AddTenantModal } from '@/components/tenants/AddTenantModal';
import { useTenants } from '@/components/tenants/useTenants';
import type { TenantStats } from '@/types/tenant';

const Tenants = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  const { data: tenants, isLoading, error } = useTenants(searchTerm, statusFilter, sortBy);

  const getTenantStats = (): TenantStats => {
    if (!tenants) return { total: 0, active: 0, expiring: 0, expired: 0 };
    
    return {
      total: tenants.length,
      active: tenants.filter(t => t.lease_status.toLowerCase() === 'active').length,
      expiring: tenants.filter(t => t.days_until_expiry > 0 && t.days_until_expiry < 90).length,
      expired: tenants.filter(t => t.days_until_expiry < 0).length
    };
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading tenants: {(error as Error).message}</p>
      </div>
    );
  }

  const stats = getTenantStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tenants</h1>
          <p className="text-gray-600 mt-1">Manage your tenant relationships and lease agreements</p>
        </div>
        <AddTenantModal />
      </div>

      {/* Stats Cards */}
      <TenantStatsCards stats={stats} />

      {/* Filters and Search */}
      <TenantsFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        sortBy={sortBy}
        setSortBy={setSortBy}
        tenants={tenants}
      />

      {/* Tenants Grid */}
      {tenants && tenants.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tenants.map((tenant) => (
            <TenantCard key={tenant.party_id} tenant={tenant} />
          ))}
        </div>
      ) : (
        <EmptyState
          icon={<Users className="w-12 h-12" />}
          title="No tenants found"
          description={searchTerm || statusFilter !== 'all' 
            ? "No tenants match your current filters. Try adjusting your search criteria."
            : "Get started by adding your first tenant to the system."
          }
          action={<AddTenantModal />}
        />
      )}
    </div>
  );
};

export default Tenants;

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { FolderOpen, Upload, Download, Search, Filter, FileText, File, Calendar, Eye } from 'lucide-react';
import { useState } from 'react';

const Documents = () => {
  const [activeTab, setActiveTab] = useState('all');

  // Mock document data based on your database structure
  const mockDocuments = [
    {
      id: '1',
      name: 'Lease Agreement - GESTION MARCAN INC.pdf',
      type: 'lease_agreement',
      category: 'Legal',
      property: 'Suite 350 & 375, 2550 Daniel-Johnson',
      tenant: 'GESTION MARCAN INC.',
      uploadDate: '2025-05-01',
      size: '2.4 MB',
      status: 'active',
      confidential: true
    },
    {
      id: '2',
      name: 'Property Insurance Certificate.pdf',
      type: 'insurance',
      category: 'Insurance',
      property: 'Suite 350 & 375, 2550 Daniel-Johnson',
      tenant: null,
      uploadDate: '2025-04-15',
      size: '1.1 MB',
      status: 'active',
      confidential: false
    },
    {
      id: '3',
      name: 'Maintenance Report - HVAC.pdf',
      type: 'maintenance',
      category: 'Maintenance',
      property: '2865, rue Botham à Montréal',
      tenant: '9142-1412 QUÉBEC INC.',
      uploadDate: '2025-05-20',
      size: '856 KB',
      status: 'active',
      confidential: false
    },
    {
      id: '4',
      name: 'Financial Statement Q1 2025.xlsx',
      type: 'financial',
      category: 'Financial',
      property: 'All Properties',
      tenant: null,
      uploadDate: '2025-04-01',
      size: '3.2 MB',
      status: 'active',
      confidential: true
    }
  ];

  const documentCategories = [
    { value: 'all', label: 'All Documents' },
    { value: 'legal', label: 'Legal Documents' },
    { value: 'financial', label: 'Financial Records' },
    { value: 'maintenance', label: 'Maintenance Reports' },
    { value: 'insurance', label: 'Insurance Documents' },
    { value: 'inspection', label: 'Inspection Reports' }
  ];

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'lease_agreement':
      case 'legal':
        return <FileText className="w-4 h-4 text-blue-500" />;
      case 'financial':
        return <File className="w-4 h-4 text-green-500" />;
      case 'maintenance':
        return <File className="w-4 h-4 text-orange-500" />;
      case 'insurance':
        return <File className="w-4 h-4 text-purple-500" />;
      default:
        return <File className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Document Management</h1>
        <Button>
          <Upload className="w-4 h-4 mr-2" />
          Upload Document
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="w-8 h-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">24</p>
                <p className="text-sm text-gray-600">Total Documents</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <File className="w-8 h-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">8</p>
                <p className="text-sm text-gray-600">Active Leases</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="w-8 h-8 text-orange-500" />
              <div>
                <p className="text-2xl font-bold">3</p>
                <p className="text-sm text-gray-600">Expiring Soon</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FolderOpen className="w-8 h-8 text-purple-500" />
              <div>
                <p className="text-2xl font-bold">156</p>
                <p className="text-sm text-gray-600">Total Size (MB)</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Document Library</CardTitle>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input placeholder="Search documents..." className="pl-9 w-64" />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {documentCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Property/Tenant</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockDocuments.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getDocumentIcon(doc.type)}
                      <div>
                        <p className="font-medium">{doc.name}</p>
                        {doc.confidential && (
                          <Badge variant="secondary" className="text-xs">Confidential</Badge>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{doc.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="text-sm font-medium">{doc.property}</p>
                      {doc.tenant && (
                        <p className="text-xs text-gray-500">{doc.tenant}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{doc.uploadDate}</TableCell>
                  <TableCell>{doc.size}</TableCell>
                  <TableCell>
                    <Badge variant={doc.status === 'active' ? 'default' : 'secondary'}>
                      {doc.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Uploads</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockDocuments.slice(0, 3).map((doc) => (
                <div key={doc.id} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex items-center space-x-2">
                    {getDocumentIcon(doc.type)}
                    <div>
                      <p className="text-sm font-medium">{doc.name}</p>
                      <p className="text-xs text-gray-500">{doc.uploadDate}</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs">{doc.size}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Document Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Lease Agreements</span>
                <Badge variant="secondary">8</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Financial Records</span>
                <Badge variant="secondary">12</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Maintenance Reports</span>
                <Badge variant="secondary">6</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Insurance Documents</span>
                <Badge variant="secondary">4</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Inspection Reports</span>
                <Badge variant="secondary">3</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Documents;

import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { FileText, Users, Building, DollarSign, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { LeaseFormHeader } from '@/components/forms/LeaseFormHeader';
import { LeaseBasicInfoForm } from '@/components/forms/LeaseBasicInfoForm';
import { LeasePropertyInfoForm } from '@/components/forms/LeasePropertyInfoForm';
import { LeaseTenantInfoForm } from '@/components/forms/LeaseTenantInfoForm';
import { LeaseFinancialForm } from '@/components/forms/LeaseFinancialForm';
import { LeaseTermsForm } from '@/components/forms/LeaseTermsForm';
import { LeaseFormData, initialLeaseData } from '@/components/forms/types';

const EditLease = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('basic');
  const [leaseData, setLeaseData] = useState<LeaseFormData>({
    ...initialLeaseData,
    lease_status: 'active'
  });

  useEffect(() => {
    // Simulate loading lease data
    const loadLeaseData = async () => {
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data - in real app this would come from the database
        const mockLeaseData: LeaseFormData = {
          lease_title: `Office Lease - Suite 350 (ID: ${id})`,
          lease_type: 'commercial',
          commencement_date: '2024-01-01',
          end_date: '2026-12-31',
          lease_term_months: '36',
          lease_status: 'active',
          building_address: '2550 Daniel-Johnson Blvd, Laval, QC',
          leased_premises_address: 'Suite 350 & 375',
          rental_area_sqft: '2500',
          property_description: 'Premium office space with modern amenities',
          tenant_name: 'GESTION MARCAN INC.',
          tenant_type: 'corporation',
          tenant_address: '123 Business St, Montreal, QC H1A 1A1',
          tenant_phone: '(*************',
          tenant_email: '<EMAIL>',
          monthly_base_rent: '8500.00',
          annual_base_rent: '102000.00',
          rent_currency: 'CAD',
          rent_due_day: '1',
          security_deposit: '17000.00',
          renewal_terms: 'Tenant has the option to renew for additional 3-year terms',
          termination_notice: '6 months written notice required',
          use_restrictions: 'Office use only, no retail or manufacturing',
          maintenance_responsibilities: 'Landlord responsible for structural, tenant for interior',
          additional_terms: 'Annual rent increases of 2% starting year 2',
          notes: 'Good tenant, always pays on time'
        };
        
        setLeaseData(mockLeaseData);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load lease data",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadLeaseData();
  }, [id, toast]);

  const handleInputChange = (field: string, value: string) => {
    setLeaseData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Auto-calculate annual rent when monthly is entered
    if (field === 'monthly_base_rent') {
      const monthlyRent = parseFloat(value) || 0;
      setLeaseData(prev => ({
        ...prev,
        annual_base_rent: (monthlyRent * 12).toString()
      }));
    }
  };

  const validateTab = (tabName: string) => {
    switch (tabName) {
      case 'basic':
        return leaseData.lease_title && leaseData.commencement_date && leaseData.end_date;
      case 'property':
        return leaseData.building_address && leaseData.rental_area_sqft;
      case 'tenant':
        return leaseData.tenant_name;
      case 'financial':
        return leaseData.monthly_base_rent;
      default:
        return true;
    }
  };

  const handleNext = () => {
    const tabs = ['basic', 'property', 'tenant', 'financial', 'terms'];
    const currentIndex = tabs.indexOf(activeTab);
    
    if (!validateTab(activeTab)) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields before continuing",
        variant: "destructive"
      });
      return;
    }
    
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1]);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log('Updated lease data:', leaseData);
      
      toast({
        title: "Success",
        description: "Lease updated successfully! Database integration coming soon."
      });
      
      navigate(`/leases/${id}`);
      
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update lease",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <LeaseFormHeader
        title="Edit Lease"
        subtitle="Modify lease details and terms"
        onBack={() => navigate(`/leases/${id}`)}
        onSubmit={handleSubmit}
        isSubmitting={isSubmitting}
        showSaveDraft={false}
        submitLabel="Update Lease"
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Basic Info
          </TabsTrigger>
          <TabsTrigger value="property" className="flex items-center gap-2">
            <Building className="w-4 h-4" />
            Property
          </TabsTrigger>
          <TabsTrigger value="tenant" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Tenant
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            Financial
          </TabsTrigger>
          <TabsTrigger value="terms" className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Terms
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
          <LeaseBasicInfoForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            showStatus={true}
          />
        </TabsContent>

        <TabsContent value="property">
          <LeasePropertyInfoForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            onPrevious={() => setActiveTab('basic')}
          />
        </TabsContent>

        <TabsContent value="tenant">
          <LeaseTenantInfoForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            onPrevious={() => setActiveTab('property')}
          />
        </TabsContent>

        <TabsContent value="financial">
          <LeaseFinancialForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            onPrevious={() => setActiveTab('tenant')}
          />
        </TabsContent>

        <TabsContent value="terms">
          <LeaseTermsForm
            data={leaseData}
            onChange={handleInputChange}
            onSubmit={handleSubmit}
            onPrevious={() => setActiveTab('financial')}
            isSubmitting={isSubmitting}
            showSaveDraft={false}
            submitLabel="Update Lease"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EditLease;

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Users, UserCheck, Shield, AlertTriangle, ArrowLeft } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { UserPropertyAccess } from '@/components/users/UserPropertyAccess';

interface User {
  id: string;
  email: string;
  name: string;
  created_at: string;
  role: 'administrator' | 'property_manager' | 'maintenance_coordinator' | null;
  assigned_at: string | null;
  assigned_by: string | null;
}

const UserManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const { data: users, isLoading } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('user_management_view')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as User[];
    },
  });

  const { data: leases } = useQuery({
    queryKey: ['leases-for-access'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('lease_documents')
        .select(`
          lease_id,
          parties!inner(party_name, party_type),
          properties(building_address, leased_premises_address)
        `);

      if (error) throw error;
      return data;
    },
  });

  const assignRoleMutation = useMutation({
    mutationFn: async ({ userId, role }: { userId: string; role: string }) => {
      const { error } = await supabase
        .from('user_roles')
        .upsert({
          user_id: userId,
          role: role as any,
          assigned_by: (await supabase.auth.getUser()).data.user?.id
        });

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Role assigned successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to assign role.',
        variant: 'destructive',
      });
    },
  });

  const removeRoleMutation = useMutation({
    mutationFn: async (userId: string) => {
      const { error } = await supabase
        .from('user_roles')
        .delete()
        .eq('user_id', userId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Role removed successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to remove role.',
        variant: 'destructive',
      });
    },
  });

  const handleRoleChange = (userId: string, role: string) => {
    if (role === 'none') {
      removeRoleMutation.mutate(userId);
    } else {
      assignRoleMutation.mutate({ userId, role });
    }
  };

  const handleUserClick = (user: User) => {
    if (user.role && user.role !== 'administrator') {
      setSelectedUser(user);
    }
  };

  const getRoleBadgeVariant = (role: string | null) => {
    switch (role) {
      case 'administrator': return 'destructive';
      case 'property_manager': return 'default';
      case 'maintenance_coordinator': return 'secondary';
      default: return 'outline';
    }
  };

  const getRoleLabel = (role: string | null) => {
    switch (role) {
      case 'administrator': return 'Administrator';
      case 'property_manager': return 'Property Manager';
      case 'maintenance_coordinator': return 'Maintenance Coordinator';
      default: return 'No Role';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Show detailed user view when a user is selected
  if (selectedUser && leases) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSelectedUser(null)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-3xl font-bold">User Permissions</h1>
            <p className="text-gray-600 mt-1">
              Managing permissions for {selectedUser.name}
            </p>
          </div>
        </div>
        
        <UserPropertyAccess
          userId={selectedUser.id}
          leases={leases}
          onClose={() => setSelectedUser(null)}
          showAsFullPage={true}
        />
      </div>
    );
  }

  const usersWithoutRoles = users?.filter(user => !user.role) || [];
  const usersWithRoles = users?.filter(user => user.role) || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-gray-600 mt-1">
            Manage user roles and permissions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5 text-gray-500" />
          <span className="text-sm text-gray-600">
            {users?.length || 0} total users
          </span>
        </div>
      </div>

      {/* Users without roles */}
      {usersWithoutRoles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Users Pending Role Assignment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {usersWithoutRoles.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg bg-orange-50">
                  <div>
                    <h3 className="font-medium">{user.name}</h3>
                    <p className="text-sm text-gray-600">{user.email}</p>
                    <p className="text-xs text-gray-500">
                      Joined {new Date(user.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge variant="outline">No Role</Badge>
                    <Select onValueChange={(role) => handleRoleChange(user.id, role)}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Assign role..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="administrator">Administrator</SelectItem>
                        <SelectItem value="property_manager">Property Manager</SelectItem>
                        <SelectItem value="maintenance_coordinator">Maintenance Coordinator</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Users with roles */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5 text-green-500" />
            Active Users
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {usersWithRoles.map((user) => (
              <div 
                key={user.id} 
                className={`flex items-center justify-between p-4 border rounded-lg transition-colors ${
                  user.role && user.role !== 'administrator' 
                    ? 'hover:bg-gray-50 cursor-pointer' 
                    : ''
                }`}
                onClick={() => handleUserClick(user)}
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <h3 className="font-medium">{user.name}</h3>
                    <Badge variant={getRoleBadgeVariant(user.role) as any}>
                      {getRoleLabel(user.role)}
                    </Badge>
                    {user.role && user.role !== 'administrator' && (
                      <span className="text-xs text-gray-500">Click to manage permissions</span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{user.email}</p>
                  <p className="text-xs text-gray-500">
                    Role assigned {user.assigned_at ? new Date(user.assigned_at).toLocaleDateString() : 'Unknown'}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <Select
                    value={user.role || 'none'}
                    onValueChange={(role) => handleRoleChange(user.id, role)}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="administrator">Administrator</SelectItem>
                      <SelectItem value="property_manager">Property Manager</SelectItem>
                      <SelectItem value="maintenance_coordinator">Maintenance Coordinator</SelectItem>
                      <SelectItem value="none">Remove Role</SelectItem>
                    </SelectContent>
                  </Select>
                  {user.role && user.role !== 'administrator' && (
                    <Shield className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserManagement;

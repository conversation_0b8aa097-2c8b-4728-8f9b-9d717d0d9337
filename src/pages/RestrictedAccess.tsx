
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Shield, Clock, Mail, AlertTriangle } from 'lucide-react';

const RestrictedAccess = () => {
  const { logout } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-2xl w-full">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
            <Shield className="h-8 w-8 text-orange-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Account Access Pending
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-2 text-orange-600">
              <Clock className="h-5 w-5" />
              <span className="font-medium">Awaiting Role Assignment</span>
            </div>
            
            <p className="text-gray-600 leading-relaxed">
              Your account has been created successfully, but you don't have access to any features yet. 
              An administrator needs to assign you a role before you can access the system.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
              <h3 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                What happens next?
              </h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• An administrator will review your account</li>
                <li>• You'll be assigned an appropriate role based on your responsibilities</li>
                <li>• Once assigned, you'll have access to relevant features and data</li>
                <li>• You'll receive access to specific properties based on your role</li>
              </ul>
            </div>

            <div className="bg-gray-50 border rounded-lg p-4 text-left">
              <h3 className="font-semibold text-gray-900 mb-2">Available Roles:</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <span className="font-medium">Administrator:</span> Full access to all properties and user management
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <span className="font-medium">Property Manager:</span> Complete access to assigned properties
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <span className="font-medium">Maintenance Coordinator:</span> Access to maintenance, compliance, and non-financial data
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-center gap-2 text-gray-600 text-sm">
              <Mail className="h-4 w-4" />
              <span>Need help? Contact your system administrator</span>
            </div>
          </div>

          <div className="flex justify-center pt-4">
            <Button variant="outline" onClick={logout}>
              Sign Out
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RestrictedAccess;


import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { PropertyDetailHeader } from '@/components/properties/PropertyDetailHeader';
import { PropertyDetailMetrics } from '@/components/properties/PropertyDetailMetrics';
import { PropertyDetailTabs } from '@/components/properties/PropertyDetailTabs';

const PropertyDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const { data: property, isLoading, error } = useQuery({
    queryKey: ['property', id],
    queryFn: async () => {
      if (!id) throw new Error('Property ID is required');

      // Query the property_portfolio table directly
      const { data: portfolioProperty, error } = await (supabase as any)
        .from('property_portfolio')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      if (!portfolioProperty) {
        throw new Error('Property not found');
      }

      // Transform the data to match the expected structure
      return {
        property_id: portfolioProperty.id,
        building_address: `${portfolioProperty.address_street}, ${portfolioProperty.address_city}, ${portfolioProperty.address_province}`,
        leased_premises_address: portfolioProperty.address_street,
        rental_area_sqft: portfolioProperty.total_area_sqft,
        property_condition: 'Good', // Default value
        plan_reference: portfolioProperty.property_external_id,
        
        // Additional portfolio-specific data
        property_name: portfolioProperty.property_name,
        property_type: portfolioProperty.property_type,
        address_street: portfolioProperty.address_street,
        address_city: portfolioProperty.address_city,
        address_province: portfolioProperty.address_province,
        address_country: portfolioProperty.address_country,
        total_area_sqft: portfolioProperty.total_area_sqft,
        property_status: portfolioProperty.property_status,
        property_description: portfolioProperty.property_description,
        amenities: portfolioProperty.amenities,
        contact_name: portfolioProperty.contact_name,
        contact_phone: portfolioProperty.contact_phone,
        contact_email: portfolioProperty.contact_email,
        property_url: portfolioProperty.property_url,
        available_spaces: portfolioProperty.available_spaces || [],
        media_urls: portfolioProperty.media_urls || {},
        nearby_properties: portfolioProperty.nearby_properties || [],
        parking_spaces: portfolioProperty.parking_spaces,
        
        // Mock lease/tenant data for office properties
        tenants: [],
        totalRent: 0,
        activeTenants: 0,
        totalUnits: portfolioProperty.available_spaces?.length || 0,
        occupancyRate: 0,
        leases: []
      };
    },
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading property details</p>
        <Button onClick={() => navigate('/properties')} className="mt-4">
          Back to Properties
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PropertyDetailHeader property={property} />
      <PropertyDetailMetrics property={property} />
      <PropertyDetailTabs property={property} />
    </div>
  );
};

export default PropertyDetail;

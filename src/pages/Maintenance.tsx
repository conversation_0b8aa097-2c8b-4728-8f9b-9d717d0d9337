
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Wrench, User, DollarSign, Clock, AlertTriangle, Hammer, Droplets, Zap, Building, Sparkles, X } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface MaintenanceObligation {
  maintenance_id: string;
  maintenance_type: string;
  maintenance_description: string;
  responsible_party: string;
  cost_responsibility: string;
  notification_requirements: string;
  lease_id: string;
  tenant_name?: string;
  property_address?: string;
}

type FilterType = 'all' | 'landlord' | 'tenant' | 'shared';

const Maintenance = () => {
  const [filter, setFilter] = useState<FilterType>('all');

  const { data: maintenanceData, isLoading, error } = useQuery({
    queryKey: ['all-maintenance'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('maintenance_obligations')
        .select(`
          *,
          lease_documents!inner(
            lease_id,
            parties!inner(
              party_name,
              party_type
            ),
            properties(
              building_address,
              leased_premises_address
            )
          )
        `);

      if (error) throw error;

      // Transform the data to include tenant and property info
      return data.map(obligation => {
        const lease = obligation.lease_documents;
        const tenant = lease.parties?.find(p => p.party_type === 'tenant');
        const property = lease.properties?.[0];
        
        return {
          ...obligation,
          tenant_name: tenant?.party_name || 'Unknown Tenant',
          property_address: property?.building_address || property?.leased_premises_address || 'Unknown Property'
        };
      });
    },
  });

  const getIconForType = (type: string) => {
    const lowerType = type?.toLowerCase() || '';
    if (lowerType.includes('hvac')) return Zap;
    if (lowerType.includes('plumbing')) return Droplets;
    if (lowerType.includes('electrical')) return Zap;
    if (lowerType.includes('structural')) return Building;
    if (lowerType.includes('cleaning')) return Sparkles;
    if (lowerType.includes('general')) return Wrench;
    return Hammer;
  };

  const getResponsibilityColor = (party: string) => {
    switch (party?.toLowerCase()) {
      case 'landlord': return 'default';
      case 'tenant': return 'secondary';
      case 'shared': return 'outline';
      default: return 'outline';
    }
  };

  const capitalizeFirstLetter = (str: string) => {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const formatMaintenanceType = (type: string) => {
    if (!type) return type;
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading maintenance data</p>
      </div>
    );
  }

  const maintenanceObligations = maintenanceData || [];

  // Filter obligations based on selected filter
  const filteredObligations = filter === 'all' 
    ? maintenanceObligations 
    : maintenanceObligations.filter(obligation => 
        obligation.responsible_party?.toLowerCase() === filter
      );

  // Group filtered obligations by type
  const groupedObligations = filteredObligations.reduce((acc, obligation) => {
    const type = obligation.maintenance_type || 'General Maintenance';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(obligation);
    return acc;
  }, {} as Record<string, MaintenanceObligation[]>);

  const getFilterTitle = () => {
    switch (filter) {
      case 'landlord': return 'Landlord Obligations';
      case 'tenant': return 'Tenant Obligations';
      case 'shared': return 'Shared Responsibilities';
      default: return 'All Maintenance Obligations';
    }
  };

  if (Object.entries(groupedObligations).length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Maintenance Management</h1>
            <p className="text-gray-600 mt-2">Manage maintenance obligations across all properties</p>
          </div>
          {filter !== 'all' && (
            <Button 
              variant="outline" 
              onClick={() => setFilter('all')}
              className="flex items-center gap-2"
            >
              <X className="w-4 h-4" />
              Clear Filter
            </Button>
          )}
        </div>
        <Card>
          <CardContent className="text-center p-12">
            <Wrench className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Maintenance Obligations</h3>
            <p className="text-gray-500">
              {filter === 'all' 
                ? 'No maintenance obligations have been found across all leases.'
                : `No ${getFilterTitle().toLowerCase()} found.`
              }
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Maintenance Management</h1>
          <p className="text-gray-600 mt-2">Manage maintenance obligations across all properties</p>
        </div>
        <div className="flex items-center gap-4">
          {filter !== 'all' && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-sm">
                Showing: {getFilterTitle()}
              </Badge>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setFilter('all')}
                className="flex items-center gap-1"
              >
                <X className="w-3 h-3" />
                Clear
              </Button>
            </div>
          )}
          <Badge variant="outline" className="text-sm">
            {filteredObligations.length} {filteredObligations.length === 1 ? 'Obligation' : 'Obligations'}
          </Badge>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card 
          className={`p-4 cursor-pointer transition-all hover:shadow-md ${filter === 'landlord' ? 'ring-2 ring-blue-500 bg-blue-50' : ''}`}
          onClick={() => setFilter(filter === 'landlord' ? 'all' : 'landlord')}
        >
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Landlord Obligations</p>
              <p className="text-xl font-semibold">
                {maintenanceObligations.filter(m => m.responsible_party?.toLowerCase() === 'landlord').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card 
          className={`p-4 cursor-pointer transition-all hover:shadow-md ${filter === 'tenant' ? 'ring-2 ring-green-500 bg-green-50' : ''}`}
          onClick={() => setFilter(filter === 'tenant' ? 'all' : 'tenant')}
        >
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <User className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Tenant Obligations</p>
              <p className="text-xl font-semibold">
                {maintenanceObligations.filter(m => m.responsible_party?.toLowerCase() === 'tenant').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card 
          className={`p-4 cursor-pointer transition-all hover:shadow-md ${filter === 'shared' ? 'ring-2 ring-purple-500 bg-purple-50' : ''}`}
          onClick={() => setFilter(filter === 'shared' ? 'all' : 'shared')}
        >
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-50">
              <AlertTriangle className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Shared Responsibilities</p>
              <p className="text-xl font-semibold">
                {maintenanceObligations.filter(m => m.responsible_party?.toLowerCase() === 'shared').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-orange-50">
              <Wrench className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Properties</p>
              <p className="text-xl font-semibold">
                {new Set(maintenanceObligations.map(m => m.lease_id)).size}
              </p>
            </div>
          </div>
        </Card>
      </div>

      <Accordion type="multiple" className="space-y-4">
        {Object.entries(groupedObligations).map(([type, obligations]) => {
          const IconComponent = getIconForType(type);
          const formattedType = formatMaintenanceType(type);
          return (
            <AccordionItem key={type} value={type} className="border rounded-lg">
              <Card>
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  <div className="flex items-center gap-3 text-left">
                    <div className="p-2 rounded-lg bg-orange-50">
                      <IconComponent className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{formattedType}</h3>
                      <p className="text-sm text-gray-600">
                        {obligations.length} {obligations.length === 1 ? 'obligation' : 'obligations'} across {new Set(obligations.map(o => o.lease_id)).size} {new Set(obligations.map(o => o.lease_id)).size === 1 ? 'property' : 'properties'}
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0 space-y-4">
                    {obligations.map((obligation) => (
                      <div key={obligation.maintenance_id} className="p-6 border rounded-lg bg-gray-50">
                        <div className="space-y-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 text-base leading-relaxed mb-2">
                                {obligation.maintenance_description || 'No description provided'}
                              </p>
                              <div className="text-sm text-gray-600">
                                <p><span className="font-medium">Property:</span> {obligation.property_address}</p>
                                <p><span className="font-medium">Tenant:</span> {obligation.tenant_name}</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                              <User className="h-5 w-5 text-blue-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Responsible Party</p>
                                <Badge variant={getResponsibilityColor(obligation.responsible_party) as any} className="mt-1">
                                  {capitalizeFirstLetter(obligation.responsible_party) || 'Not specified'}
                                </Badge>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                              <DollarSign className="h-5 w-5 text-green-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Cost Responsibility</p>
                                <Badge variant={getResponsibilityColor(obligation.cost_responsibility) as any} className="mt-1">
                                  {capitalizeFirstLetter(obligation.cost_responsibility) || 'Not specified'}
                                </Badge>
                              </div>
                            </div>
                          </div>

                          {obligation.notification_requirements && (
                            <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-l-blue-400">
                              <div className="flex items-start gap-3">
                                <Clock className="h-5 w-5 text-blue-500 mt-0.5" />
                                <div>
                                  <p className="font-medium text-blue-900 text-sm">Notification Requirements</p>
                                  <p className="text-blue-800 mt-1">
                                    {obligation.notification_requirements}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
};

export default Maintenance;

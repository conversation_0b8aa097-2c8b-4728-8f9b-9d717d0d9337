
import { Building, Plus, Search, Filter, MapPin, Phone, Mail, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { StatusBadge } from '@/components/ui/status-badge';
import { EmptyState } from '@/components/ui/empty-state';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useNavigate } from 'react-router-dom';
import { AddPropertyModal } from '@/components/properties/AddPropertyModal';
import { Badge } from '@/components/ui/badge';

const Properties = () => {
  const navigate = useNavigate();

  const { data: properties, isLoading, error } = useQuery({
    queryKey: ['properties'],
    queryFn: async () => {
      // Direct query to property_portfolio table using Supabase client
      const { data, error } = await (supabase as any)
        .from('property_portfolio')
        .select('*')
        .order('property_name');
      
      if (error) throw error;
      
      return data?.map((property: any) => ({
        id: property.id,
        externalId: property.property_external_id,
        name: property.property_name,
        type: property.property_type,
        address: `${property.address_street}, ${property.address_city}, ${property.address_province}`,
        city: property.address_city,
        province: property.address_province,
        country: property.address_country,
        totalArea: property.total_area_sqft,
        status: property.property_status,
        description: property.property_description,
        amenities: property.amenities,
        contactName: property.contact_name,
        contactPhone: property.contact_phone,
        contactEmail: property.contact_email,
        propertyUrl: property.property_url,
        availableSpaces: property.available_spaces || [],
        mediaUrls: property.media_urls || {},
        nearbyProperties: property.nearby_properties || [],
        parkingSpaces: property.parking_spaces,
        createdAt: property.created_at
      })) || [];
    },
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading properties: {error.message}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
          <p className="text-gray-600 mt-1">Manage your property portfolio</p>
        </div>
        <AddPropertyModal />
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Search properties..." className="pl-10" />
          </div>
        </div>
        <Button variant="outline">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Properties Grid */}
      {properties && properties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {properties.map((property: any) => (
            <Card 
              key={property.id} 
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => navigate(`/properties/${property.id}`)}
            >
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{property.name}</CardTitle>
                  <StatusBadge status={property.status} />
                </div>
                <p className="text-sm text-gray-600">{property.address}</p>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium">{property.type}</span>
                  </div>
                  {property.totalArea && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Total Area:</span>
                      <span className="font-medium">{property.totalArea.toLocaleString()} sq ft</span>
                    </div>
                  )}
                  {property.availableSpaces && property.availableSpaces.length > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Available Spaces:</span>
                      <span className="font-medium text-emerald-600">{property.availableSpaces.length} suite(s)</span>
                    </div>
                  )}
                  {property.contactName && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Contact:</span>
                      <span className="font-medium">{property.contactName}</span>
                    </div>
                  )}
                  {property.parkingSpaces && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Parking:</span>
                      <span className="font-medium">{property.parkingSpaces} spaces</span>
                    </div>
                  )}
                  {property.propertyUrl && (
                    <div className="flex items-center gap-2 mt-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(property.propertyUrl, '_blank');
                        }}
                        className="text-xs"
                      >
                        <ExternalLink className="w-3 h-3 mr-1" />
                        View Details
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <EmptyState
          icon={<Building className="w-12 h-12" />}
          title="No properties found"
          description="Get started by adding your first property to the portfolio."
          action={<AddPropertyModal />}
        />
      )}
    </div>
  );
};

export default Properties;

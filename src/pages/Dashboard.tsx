import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { AddPropertyModal } from '@/components/properties/AddPropertyModal';
import { useNavigate } from 'react-router-dom';
import { KPICards } from '@/components/dashboard/KPICards';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { UpcomingExpirations } from '@/components/dashboard/UpcomingExpirations';
import { QuickActions } from '@/components/dashboard/QuickActions';

const Dashboard = () => {
  const navigate = useNavigate();

  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ['dashboard-summary'],
    queryFn: async () => {
      // Get comprehensive data from multiple tables
      const [leasesResult, chatResult, documentsResult] = await Promise.all([
        supabase
          .from('lease_documents')
          .select(`
            lease_id,
            lease_status,
            created_date,
            end_date,
            commencement_date,
            parties!inner(party_name, party_type),
            properties(building_address, leased_premises_address, rental_area_sqft),
            financial_terms(monthly_base_rent, annual_base_rent),
            security_deposits(security_deposit_total)
          `)
          .order('created_date', { ascending: false }),
        
        supabase
          .from('chat_sessions')
          .select('id, created_at, title')
          .order('created_at', { ascending: false })
          .limit(5),
          
        supabase
          .from('documents')
          .select('id, name, created_at')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      if (leasesResult.error) throw leasesResult.error;
      
      const leases = leasesResult.data || [];
      
      // Calculate comprehensive metrics
      const totalProperties = new Set(leases.map(lease => 
        lease.properties?.[0]?.building_address || lease.properties?.[0]?.leased_premises_address
      ).filter(Boolean)).size;
      
      const activeLeases = leases.filter(lease => 
        lease.lease_status?.toLowerCase() === 'active'
      ).length || 0;
      
      const monthlyRevenue = leases.reduce((sum, lease) => {
        const rent = lease.financial_terms?.[0]?.monthly_base_rent || 0;
        return sum + Number(rent);
      }, 0) || 0;

      const annualRevenue = leases.reduce((sum, lease) => {
        const rent = lease.financial_terms?.[0]?.annual_base_rent || 0;
        return sum + Number(rent);
      }, 0) || 0;

      const totalDeposits = leases.reduce((sum, lease) => {
        const deposit = lease.security_deposits?.[0]?.security_deposit_total || 0;
        return sum + Number(deposit);
      }, 0) || 0;

      const totalArea = leases.reduce((sum, lease) => {
        const area = lease.properties?.[0]?.rental_area_sqft || 0;
        return sum + Number(area);
      }, 0) || 0;
      
      const occupancyRate = totalProperties > 0 ? (activeLeases / totalProperties) * 100 : 0;

      return {
        totalProperties,
        activeLeases,
        monthlyRevenue,
        annualRevenue,
        totalDeposits,
        totalArea,
        occupancyRate,
        leases,
        chatSessions: chatResult.data || [],
        documents: documentsResult.data || []
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Get recent lease activities (last 5 leases by creation date)
  const recentActivity = dashboardData?.leases
    .filter(lease => lease.created_date) // Filter out leases without dates
    .sort((a, b) => new Date(b.created_date!).getTime() - new Date(a.created_date!).getTime())
    .slice(0, 5)
    .map(lease => {
      const tenant = lease.parties?.find(p => p.party_type === 'tenant');
      const property = lease.properties?.[0];
      const propertyAddress = property?.building_address || property?.leased_premises_address || 'Unknown Property';
      
      return {
        type: 'lease',
        message: `Lease for ${propertyAddress} with ${tenant?.party_name || 'Unknown Tenant'}`,
        time: new Date(lease.created_date!).toLocaleDateString(),
        status: lease.lease_status?.toLowerCase() as 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled'
      };
    }) || [];

  // Get upcoming lease expirations (next 30 days)
  const upcomingExpirations = dashboardData?.leases
    .filter(lease => {
      if (!lease.end_date) return false;
      const endDate = new Date(lease.end_date);
      const today = new Date();
      const thirtyDaysFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
      return endDate >= today && endDate <= thirtyDaysFromNow;
    })
    .map(lease => {
      const tenant = lease.parties?.find(p => p.party_type === 'tenant');
      const property = lease.properties?.[0];
      const propertyAddress = property?.building_address || property?.leased_premises_address || 'Unknown Property';
      
      return {
        property: propertyAddress,
        tenant: tenant?.party_name || 'Unknown Tenant',
        expires: new Date(lease.end_date!).toLocaleDateString(),
        status: lease.lease_status?.toLowerCase() as 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled'
      };
    }) || [];

  const kpiData = {
    totalProperties: dashboardData?.totalProperties || 0,
    activeLeases: dashboardData?.activeLeases || 0,
    monthlyRevenue: dashboardData?.monthlyRevenue || 0,
    occupancyRate: dashboardData?.occupancyRate || 0,
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back. Here's what's happening with your properties.</p>
        </div>
        <AddPropertyModal trigger={
          <Button className="bg-emerald-600 hover:bg-emerald-700">
            <Plus className="w-4 h-4 mr-2" />
            Quick Action
          </Button>
        } />
      </div>

      {/* KPI Cards */}
      <KPICards data={kpiData} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <RecentActivity activities={recentActivity} />

        {/* Upcoming Expirations */}
        <UpcomingExpirations expirations={upcomingExpirations} />
      </div>

      {/* Quick Actions */}
      <QuickActions
        onAddLease={() => navigate('/leases/new')}
        onViewFinancials={() => navigate('/financials')}
        onViewMaintenance={() => navigate('/maintenance')}
      />
    </div>
  );
};

export default Dashboard;


import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { TenantDetailHeader } from '@/components/tenants/TenantDetailHeader';
import { TenantDetailMetrics } from '@/components/tenants/TenantDetailMetrics';
import { TenantDetailTabs } from '@/components/tenants/TenantDetailTabs';
import type { Tenant } from '@/types/tenant';

const TenantDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const { data: tenant, isLoading, error } = useQuery({
    queryKey: ['tenant', id],
    queryFn: async () => {
      if (!id) throw new Error('Tenant ID is required');

      const { data: lease, error } = await supabase
        .from('lease_documents')
        .select(`
          *,
          parties!inner(
            party_id,
            party_name,
            address_street,
            address_city,
            address_province,
            address_postal_code,
            representative_name,
            representative_title,
            email,
            phone_number
          ),
          properties(*),
          financial_terms(*),
          security_deposits(*),
          maintenance_obligations(*),
          compliance_requirements(*),
          use_restrictions(*),
          document_attachments(*)
        `)
        .eq('parties.party_id', id)
        .eq('parties.party_type', 'tenant')
        .single();

      if (error) throw error;

      const tenant = lease.parties[0];
      const property = lease.properties?.[0];
      const financialTerms = lease.financial_terms?.[0];
      
      const endDate = lease.end_date ? new Date(lease.end_date) : null;
      const today = new Date();
      const daysUntilExpiry = endDate ? Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)) : 0;

      return {
        ...tenant,
        lease_id: lease.lease_id,
        lease_status: lease.lease_status || 'unknown',
        commencement_date: lease.commencement_date || '',
        end_date: lease.end_date || '',
        monthly_rent: Number(financialTerms?.monthly_base_rent) || 0,
        rent_currency: financialTerms?.rent_currency || 'CAD',
        property_address: property?.building_address || property?.leased_premises_address || 'Unknown Property',
        rental_area_sqft: Number(property?.rental_area_sqft) || 0,
        days_until_expiry: daysUntilExpiry,
        lease_term_months: lease.lease_term_months || 0,
        lease: lease
      } as Tenant & { lease: any };
    },
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !tenant) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading tenant details</p>
        <Button onClick={() => navigate('/tenants')} className="mt-4">
          Back to Tenants
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <TenantDetailHeader tenant={tenant} />
      <TenantDetailMetrics tenant={tenant} />
      <TenantDetailTabs tenant={tenant} />
    </div>
  );
};

export default TenantDetail;

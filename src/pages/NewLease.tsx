import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { FileText, Users, Building, DollarSign, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { LeaseFormHeader } from '@/components/forms/LeaseFormHeader';
import { LeaseBasicInfoForm } from '@/components/forms/LeaseBasicInfoForm';
import { LeasePropertyInfoForm } from '@/components/forms/LeasePropertyInfoForm';
import { LeaseTenantInfoForm } from '@/components/forms/LeaseTenantInfoForm';
import { LeaseFinancialForm } from '@/components/forms/LeaseFinancialForm';
import { LeaseTermsForm } from '@/components/forms/LeaseTermsForm';
import { LeaseFormData, initialLeaseData } from '@/components/forms/types';

const NewLease = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [leaseData, setLeaseData] = useState<LeaseFormData>(initialLeaseData);

  const handleInputChange = (field: string, value: string) => {
    setLeaseData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Auto-calculate annual rent when monthly is entered
    if (field === 'monthly_base_rent') {
      const monthlyRent = parseFloat(value) || 0;
      setLeaseData(prev => ({
        ...prev,
        annual_base_rent: (monthlyRent * 12).toString()
      }));
    }
  };

  const validateTab = (tabName: string) => {
    switch (tabName) {
      case 'basic':
        return leaseData.lease_title && leaseData.commencement_date && leaseData.end_date;
      case 'property':
        return leaseData.building_address && leaseData.rental_area_sqft;
      case 'tenant':
        return leaseData.tenant_name;
      case 'financial':
        return leaseData.monthly_base_rent;
      default:
        return true;
    }
  };

  const handleNext = () => {
    const tabs = ['basic', 'property', 'tenant', 'financial', 'terms'];
    const currentIndex = tabs.indexOf(activeTab);
    
    if (!validateTab(activeTab)) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields before continuing",
        variant: "destructive"
      });
      return;
    }
    
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1]);
    }
  };

  const handleSubmit = async (asDraft = false) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const leaseDataToSubmit = {
        ...leaseData,
        lease_status: asDraft ? 'draft' : 'pending_signature'
      };
      
      console.log('Lease data to submit:', leaseDataToSubmit);
      
      toast({
        title: "Success",
        description: `Lease ${asDraft ? 'saved as draft' : 'created'} successfully! Database integration coming soon.`
      });
      
      navigate('/leases');
      
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create lease",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <LeaseFormHeader
        title="Create New Lease"
        subtitle="Enter lease details and terms"
        onBack={() => navigate('/leases')}
        onSave={() => handleSubmit(true)}
        onSubmit={() => handleSubmit(false)}
        isSubmitting={isSubmitting}
        showSaveDraft={true}
        submitLabel="Create Lease"
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Basic Info
          </TabsTrigger>
          <TabsTrigger value="property" className="flex items-center gap-2">
            <Building className="w-4 h-4" />
            Property
          </TabsTrigger>
          <TabsTrigger value="tenant" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Tenant
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            Financial
          </TabsTrigger>
          <TabsTrigger value="terms" className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Terms
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
          <LeaseBasicInfoForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            showStatus={false}
          />
        </TabsContent>

        <TabsContent value="property">
          <LeasePropertyInfoForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            onPrevious={() => setActiveTab('basic')}
          />
        </TabsContent>

        <TabsContent value="tenant">
          <LeaseTenantInfoForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            onPrevious={() => setActiveTab('property')}
          />
        </TabsContent>

        <TabsContent value="financial">
          <LeaseFinancialForm
            data={leaseData}
            onChange={handleInputChange}
            onNext={handleNext}
            onPrevious={() => setActiveTab('tenant')}
          />
        </TabsContent>

        <TabsContent value="terms">
          <LeaseTermsForm
            data={leaseData}
            onChange={handleInputChange}
            onSubmit={() => handleSubmit(false)}
            onSaveDraft={() => handleSubmit(true)}
            onPrevious={() => setActiveTab('financial')}
            isSubmitting={isSubmitting}
            showSaveDraft={true}
            submitLabel="Create Lease"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NewLease;

# Extracted Information Mapping from N8N Workflow to Database Structure

This document details the information extracted by each n8n workflow extractor and its corresponding mapping to the `optimized_database_structure_for_n8n.md`. Notes are included where discrepancies or additional context is relevant.

## 1. Document & Lease ID
- **`document_filename`**: The filename exactly as shown.
  - **Maps to**: `documents.file_name`
- **`file_url`**: The URL to the stored document file (S3, etc.).
  - **Maps to**: `documents.file_url` (Note: `optimized_database_structure_for_n8n.md` states this is extracted by "15. Signatures & Attachments" for attachments, not "1. Document & Lease ID".)
- **`lease_number`**: The lease number (Numéro du bail/Lease Number).
  - **Maps to**: `leases.lease_number`
- **`pwgsc_region_file`**: PWGSC Region File number (Dossier de la région de TPSGC).
  - **Maps to**: `leases.pwgsc_region_file`
- **`client_info`**: Client information exactly as written (e.g., Client: EDSC).
  - **Maps to**: No direct mapping; potentially `leases.notes` or `leases.details_jsonb` (if such a column existed).
- **`lease_status`**: Lease status from filename (e.g., 'Fully Executed') or document.
  - **Maps to**: `leases.current_status`
- **`lease_name`**: A human-readable name for the lease (e.g., 'XYZ Corp Office Lease - Suite 100').
  - **Maps to**: `leases.lease_name`
- **`lease_type`**: The type of lease (e.g., 'Main Lease', 'EV Charging Agreement', 'Sublease').
  - **Maps to**: `leases.lease_type`
- **`version_number`**: Version number if specified, otherwise 'Not specified in document'.
  - **Maps to**: `leases.version_number`
- **`original_lease_id`**: If this lease is a renewal or replacement of a previous one.
  - **Maps to**: `leases.original_lease_id` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`lease_language`**: Primary language of the document (English or Français).
  - **Maps to**: `leases.lease_language` (Corresponds to `documents.document_language` in `documents` table)
- **`document_date`**: The overall date the physical document was issued/signed in YYYY-MM-DD format.
  - **Maps to**: `documents.document_date`

## 2. Dates & Terms
- **`execution_date`**: Date the lease was signed by all parties in YYYY-MM-DD format.
  - **Maps to**: `leases.execution_date`
- **`commencement_date`**: Lease term commencement date (may precede rent start due to fixturing) in YYYY-MM-DD format.
  - **Maps to**: `leases.commencement_date`
- **`term_start_date`**: Actual start of rent period (after fixturing, if any) in YYYY-MM-DD format.
  - **Maps to**: `leases.term_start_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`term_end_date`**: Lease term end date in YYYY-MM-DD format.
  - **Maps to**: `leases.term_end_date`
- **`possession_date`**: Date physical possession of premises was taken in YYYY-MM-DD format.
  - **Maps to**: `leases.possession_date`
- **`fixturing_period_start_date`**: Start date of fixturing period in YYYY-MM-DD format.
  - **Maps to**: `leases.fixturing_period_start_date`
- **`fixturing_period_end_date`**: End date of fixturing period in YYYY-MM-DD format.
  - **Maps to**: `leases.fixturing_period_end_date`
- **`fixturing_period_months`**: Duration of fixturing period in months.
  - **Maps to**: `leases.fixturing_period_months`
- **`initial_term_months`**: Initial term duration in months.
  - **Maps to**: `leases.initial_term_months`
- **`initial_term_years`**: Initial term duration in years.
  - **Maps to**: `leases.initial_term_years`

## 3. Complete Party Info
- **`landlord.party_name`**: Legal name of the landlord.
  - **Maps to**: `parties.legal_name`
- **`landlord.former_name`**: Former name of the landlord.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`landlord.legal_status`**: Legal status of the landlord.
  - **Maps to**: `parties.entity_type`
- **`landlord.address_street`**: Street address of the landlord.
  - **Maps to**: `parties.primary_address_street`
- **`landlord.address_city`**: City of the landlord's address.
  - **Maps to**: `parties.primary_address_city`
- **`landlord.address_province`**: Province of the landlord's address.
  - **Maps to**: `parties.primary_address_province`
- **`landlord.address_postal_code`**: Postal code of the landlord's address.
  - **Maps to**: `parties.primary_address_postal_code`
- **`landlord.primary_email`**: Primary email for the landlord.
  - **Maps to**: `parties.primary_email`
- **`landlord.primary_phone_number`**: Primary phone number for the landlord.
  - **Maps to**: `parties.primary_phone_number`
- **`landlord.representative_name`**: Name of the landlord's representative.
  - **Maps to**: `parties.primary_contact_name`
- **`landlord.representative_title`**: Title of the landlord's representative.
  - **Maps to**: `parties.primary_contact_title`
- **`landlord.authorization_date`**: Authorization date for the landlord.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`tenant.party_name`**: Legal name of the tenant.
  - **Maps to**: `parties.legal_name`
- **`tenant.legal_status`**: Legal status of the tenant.
  - **Maps to**: `parties.entity_type`
- **`tenant.legal_address.street`**: Street address of the tenant's legal address.
  - **Maps to**: `parties.primary_address_street`
- **`tenant.legal_address.city`**: City of the tenant's legal address.
  - **Maps to**: `parties.primary_address_city`
- **`tenant.legal_address.province`**: Province of the tenant's legal address.
  - **Maps to**: `parties.primary_address_province`
- **`tenant.legal_address.postal_code`**: Postal code of the tenant's legal address.
  - **Maps to**: `parties.primary_address_postal_code`
- **`tenant.notice_address.street`**: Street address for tenant notices.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`tenant.notice_address.city`**: City for tenant notices.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`tenant.notice_address.province`**: Province for tenant notices.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`tenant.notice_address.postal_code`**: Postal code for tenant notices.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`tenant.primary_email`**: Primary email for the tenant.
  - **Maps to**: `parties.primary_email`
- **`tenant.primary_phone_number`**: Primary phone number for the tenant.
  - **Maps to**: `parties.primary_phone_number`
- **`tenant.representative_name`**: Name of the tenant's representative.
  - **Maps to**: `parties.primary_contact_name`
- **`tenant.representative_title`**: Title of the tenant's representative.
  - **Maps to**: `parties.primary_contact_title`
- **`tenant.delegation_date`**: Delegation date for the tenant.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`board_resolution.resolution_date`**: Date of the board resolution.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`board_resolution.authorization_details`**: Details of the board authorization.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`board_resolution.lease_reference`**: Lease reference from the board resolution.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`board_resolution.leased_premises_area_sqft`**: Leased premises area in sqft from board resolution.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`board_resolution.board_signatures`**: Names of board members who signed.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`party_role`**: Additional role specification for the party (e.g., 'Landlord', 'Tenant', 'Guarantor').
  - **Maps to**: `lease_party_roles.role_name`
- **`is_primary_role`**: Flag indicating if it's the main party (e.g., the main tenant, the main landlord).
  - **Maps to**: `lease_party_roles.is_primary_role` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`role_specific_details_jsonb`**: For role-specific details not in parties (e.g., {"representative_title": "CEO", "authorization_details": "Board Resolution XYZ"}).
  - **Maps to**: `lease_party_roles.role_specific_details_jsonb`
- **`additional_contact_info_jsonb`**: For fax, secondary contacts, other addresses (e.g., notice_address, billing_address, head_office if different from primary), and their types.
  - **Maps to**: `parties.additional_contact_info_jsonb`
- **`notes`**: General, unstructured notes about the party.
  - **Maps to**: `parties.notes` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`brokers`**: Array of broker objects.
  - **`broker_name`**: Name of the broker.
    - **Maps to**: `parties.legal_name` (if broker is a party) or `parties.additional_contact_info_jsonb`
  - **`firm_name`**: Name of the broker's firm.
    - **Maps to**: `parties.legal_name` (if firm is a party) or `parties.additional_contact_info_jsonb`
  - **`broker_type`**: Type of broker (e.g., 'Tenant Broker', 'Landlord Broker').
    - **Maps to**: `lease_party_roles.role_name` (if broker is a party) or `parties.additional_contact_info_jsonb`
  - **`commission_details`**: Details about commission structure or payment.
    - **Maps to**: `parties.additional_contact_info_jsonb`
  - **`contact_info_jsonb`**: Email, phone, address etc. for the broker.
    - **Maps to**: `parties.additional_contact_info_jsonb`

## 4. Property & Building
- **`property_name`**: The name of the building or property.
  - **Maps to**: `properties.name` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`property_address_street`**: Street address of the property.
  - **Maps to**: `properties.address_street`
- **`property_address_city`**: City of the property's address.
  - **Maps to**: `properties.address_city` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`property_address_province`**: Province of the property's address.
  - **Maps to**: `properties.address_province` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`property_address_postal_code`**: Postal code of the property's address.
  - **Maps to**: `properties.address_postal_code` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`property_address_country`**: Country of the property's address.
  - **Maps to**: `properties.address_country` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`property_type`**: Type of property (e.g., 'Commercial Building', 'Shopping Center').
  - **Maps to**: `properties.property_type` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`property_current_status`**: Current status of the property (e.g., 'Active', 'Under Acquisition').
  - **Maps to**: `properties.current_status` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`total_land_area_sqft`**: Total land area in square feet.
  - **Maps to**: `properties.total_land_area_sqft` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`total_built_area_sqft`**: Total built area in square feet.
  - **Maps to**: `properties.total_built_area_sqft` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`total_built_area_sqm`**: Total built area in square meters.
  - **Maps to**: `properties.total_built_area_sqft` (requires conversion) or `properties.description`
- **`year_built`**: Year the property was built.
  - **Maps to**: `properties.year_built` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`number_of_floors`**: Number of floors in the property.
  - **Maps to**: `properties.number_of_floors` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`total_parking_spaces`**: Sum of all parking spaces on the property.
  - **Maps to**: `properties.total_parking_spaces`
- **`interior_parking_spaces`**: Number of interior parking spaces.
  - **Maps to**: `properties.amenities_jsonb` or `properties.description`
- **`exterior_parking_spaces`**: Number of exterior parking spaces.
  - **Maps to**: `properties.amenities_jsonb` or `properties.description`
- **`property_manager_name`**: Name of the party managing this property.
  - **Maps to**: `properties.property_manager_party_id` (via `parties` table)
- **`description`**: General description of the property.
  - **Maps to**: `properties.description` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`zoning_classification`**: Zoning classification of the property.
  - **Maps to**: `properties.zoning_classification` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`legal_description`**: Full lot numbers, cadastre details from any relevant document.
  - **Maps to**: `properties.legal_description`
- **`amenities_jsonb`**: Structured JSON for amenities.
  - **Maps to**: `properties.amenities_jsonb`
- **`media_urls_jsonb`**: URLs to property photos/videos.
  - **Maps to**: `properties.media_urls_jsonb` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`external_reference_id`**: External reference ID for the property.
  - **Maps to**: `properties.external_reference_id` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`leased_units`**: Array of leased unit objects.
  - **`unit_number`**: Unit number.
    - **Maps to**: `property_units.unit_number` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`unit_type`**: Type of unit (e.g., 'Office', 'Retail', 'Warehouse').
    - **Maps to**: `property_units.unit_type` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`floor_number`**: Floor number of the unit.
    - **Maps to**: `property_units.floor_number` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`rentable_area_sqft`**: Rentable area in square feet.
    - **Maps to**: `property_units.rentable_area_sqft` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`rentable_area_sqm`**: Rentable area in square meters.
    - **Maps to**: `property_units.rentable_area_sqm` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`unit_current_status`**: Current status of the unit (e.g., 'Vacant', 'Occupied').
    - **Maps to**: `property_units.current_status` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`is_leasable`**: Whether the unit is leasable.
    - **Maps to**: `property_units.is_leasable` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`move_in_ready`**: Whether the unit is move-in ready.
    - **Maps to**: `property_units.move_in_ready` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`last_renovation_date`**: Last renovation date in YYYY-MM-DD format.
    - **Maps to**: `property_units.last_renovation_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`description`**: Specific details about the unit.
    - **Maps to**: `property_units.description` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`features_jsonb`**: Structured JSON for unique unit features.
    - **Maps to**: `property_units.features_jsonb` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)

## 5. Detailed Financial
- **`rent_schedules`**: Array of rent schedule objects.
  - **`schedule_type`**: Type of rent schedule (e.g., 'Base Rent', 'Semi-Gross Rent').
    - **Maps to**: `rent_schedules.schedule_type`
  - **`effective_start_date`**: Effective start date in YYYY-MM-DD format.
    - **Maps to**: `rent_schedules.effective_start_date`
  - **`effective_end_date`**: Effective end date in YYYY-MM-DD format.
    - **Maps to**: `rent_schedules.effective_end_date`
  - **`annual_rent_per_sqft`**: Annual rent per square foot.
    - **Maps to**: `rent_schedules.annual_rent_per_sqft`
  - **`annual_total_amount`**: Annual total amount.
    - **Maps to**: `rent_schedules.annual_total_amount`
  - **`monthly_installment`**: Monthly installment amount.
    - **Maps to**: `rent_schedules.monthly_installment`
  - **`currency`**: Currency (e.g., 'CAD', 'USD').
    - **Maps to**: `rent_schedules.currency` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`escalation_details_jsonb`**: Structured JSON for escalation rules.
    - **Maps to**: `rent_schedules.escalation_details_jsonb`
  - **`notes`**: Notes about the rent schedule.
    - **Maps to**: `rent_schedules.notes` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
- **`lease_financial_terms`**: Array of lease financial term objects.
  - **`term_type`**: Type of financial term (e.g., 'Operating Expenses', 'Taxes', 'Utilities', 'Late Payment').
    - **Maps to**: `lease_financial_terms.term_type` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`effective_start_date`**: Effective start date in YYYY-MM-DD format.
    - **Maps to**: `lease_financial_terms.effective_start_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`effective_end_date`**: Effective end date in YYYY-MM-DD format.
    - **Maps to**: `lease_financial_terms.effective_end_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`responsible_party_role`**: Responsible party role (e.g., 'Tenant', 'Landlord').
    - **Maps to**: `lease_financial_terms.responsible_party_role` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`annual_rate_pct`**: Annual rate percentage.
    - **Maps to**: `lease_financial_terms.annual_rate_pct` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`fixed_amount`**: Fixed amount.
    - **Maps to**: `lease_financial_terms.fixed_amount`
  - **`is_tenant_responsible_for_increase`**: Whether the tenant is responsible for an increase.
    - **Maps to**: `lease_financial_terms.is_tenant_responsible_for_increase` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`calculation_method`**: Calculation method.
    - **Maps to**: `lease_financial_terms.calculation_method` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`currency`**: Currency (e.g., 'CAD', 'USD').
    - **Maps to**: `lease_financial_terms.currency` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`details_jsonb`**: Remaining granular details and rule configurations.
    - **Maps to**: `lease_financial_terms.details_jsonb`
- **`general_gst_rate`**: General GST rate if found outside specific terms.
  - **Maps to**: No direct mapping; potentially `lease_financial_terms.details_jsonb`
- **`general_qst_rate`**: General QST rate if found outside specific terms.
  - **Maps to**: No direct mapping; potentially `lease_financial_terms.details_jsonb`
- **`general_admin_fee_on_operating_expenses`**: Admin fee on operating expenses if global.
  - **Maps to**: No direct mapping; potentially `lease_financial_terms.details_jsonb`
- **`general_admin_fee_on_taxes`**: Admin fee on taxes if global.
  - **Maps to**: No direct mapping; potentially `lease_financial_terms.details_jsonb`
- **`general_late_payment_fee`**: Late payment fee if global.
  - **Maps to**: No direct mapping; potentially `lease_financial_terms.details_jsonb`
- **`general_interest_rate_formula`**: General interest rate formula if global.
  - **Maps to**: No direct mapping; potentially `lease_financial_terms.details_jsonb`
- **`admin_fee_structure`**: Complete tiered admin fee structure.
  - **Maps to**: No direct mapping; potentially `lease_financial_terms.details_jsonb`

## 6. Security & Deposits
- **`deposits`**: Array of deposit objects.
  - **`deposit_type`**: Type of deposit (e.g., 'Security Deposit', 'Prepaid Rent', 'Pet Deposit').
    - **Maps to**: `security_deposits.deposit_type` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`amount`**: Amount of the deposit.
    - **Maps to**: `security_deposits.amount`
  - **`currency`**: Currency (e.g., 'CAD', 'USD').
    - **Maps to**: `security_deposits.currency` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`is_refundable`**: Whether the deposit is refundable.
    - **Maps to**: `security_deposits.is_refundable` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`refund_conditions`**: Conditions for refund.
    - **Maps to**: `security_deposits.refund_conditions`
  - **`interest_accrual_terms`**: Terms for interest accrual.
    - **Maps to**: `security_deposits.interest_accrual_terms`
  - **`payment_method_details_jsonb`**: JSON for payment method details.
    - **Maps to**: `security_deposits.payment_method_details_jsonb`
  - **`notes`**: Notes about the deposit.
    - **Maps to**: `security_deposits.notes`
- **`deposit_absent_note`**: Note if no security deposit provisions found in the lease.
  - **Maps to**: `security_deposits.notes`

## 7. Lease Unit Allocations
- **`lease_unit_allocations`**: Array of lease unit allocation objects.
  - **`lease_id`**: Lease ID.
    - **Maps to**: `lease_unit_allocations.lease_id`
  - **`unit_id`**: Unit ID.
    - **Maps to**: `lease_unit_allocations.unit_id`
  - **`effective_start_date`**: Effective start date in YYYY-MM-DD format.
    - **Maps to**: `lease_unit_allocations.effective_start_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`effective_end_date`**: Effective end date in YYYY-MM-DD format.
    - **Maps to**: `lease_unit_allocations.effective_end_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`allocated_area_sqft`**: Allocated area in square feet.
    - **Maps to**: `lease_unit_allocations.allocated_area_sqft` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`proportionate_share_pct`**: Proportionate share percentage.
    - **Maps to**: `lease_unit_allocations.proportionate_share_pct` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`specific_use_details`**: Specific use details for each unit under the lease.
    - **Maps to**: `lease_unit_allocations.specific_use_details` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)

## 8. Rights & Options
- **`lease_options`**: Array of lease option objects.
  - **`option_type`**: Type of option (e.g., 'Renewal', 'Purchase', 'Early Termination', 'Right of First Refusal').
    - **Maps to**: `lease_options.option_type`
  - **`option_number`**: For 1st Renewal, 2nd Renewal, etc.
    - **Maps to**: `lease_options.option_number`
  - **`effective_start_date`**: Effective start date in YYYY-MM-DD format.
    - **Maps to**: `lease_options.effective_start_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`effective_end_date`**: Effective end date in YYYY-MM-DD format.
    - **Maps to**: `lease_options.effective_end_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`notice_period_days`**: Notice period in days.
    - **Maps to**: `lease_options.notice_period_days`
  - **`exercise_conditions_text`**: Exercise conditions.
    - **Maps to**: `lease_options.exercise_conditions_text`
  - **`option_term_months`**: Option term duration in months (e.g., 60 months for a 5-year renewal).
    - **Maps to**: `lease_options.option_term_months` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`rent_calculation_method`**: Rent calculation method (e.g., 'Fair Market Rent', 'Fixed Increase', 'CPI Adjusted').
    - **Maps to**: `lease_options.rent_calculation_method`
  - **`penalty_amount`**: Penalty amount for early termination.
    - **Maps to**: `lease_options.penalty_amount`
  - **`purchase_price_formula`**: Purchase price formula for purchase options.
    - **Maps to**: `lease_options.purchase_price_formula`
  - **`details_jsonb`**: Remaining specific parameters, e.g., escalation specifics for renewal, specific exclusions for inducements on renewal, exact formulas.
    - **Maps to**: `lease_options.details_jsonb`
- **`holdover_provision`**: Object containing holdover provision details.
  - **`rent_multiplier`**: Rent multiplier.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`additional_rent_multiplier_pct`**: Additional rent multiplier percentage.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`no_tacit_renewal`**: Flag for no tacit renewal.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`details_jsonb`**: Additional details for holdover provision.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`guarantors`**: Array of guarantor objects.
  - **`guarantee_type`**: Type of guarantee.
    - **Maps to**: `parties.additional_contact_info_jsonb` or `lease_party_roles.role_specific_details_jsonb`
  - **`max_liability_amount`**: Maximum liability amount.
    - **Maps to**: `parties.additional_contact_info_jsonb` or `lease_party_roles.role_specific_details_jsonb`
  - **`guarantee_duration_start`**: Guarantee duration start date.
    - **Maps to**: `parties.additional_contact_info_jsonb` or `lease_party_roles.role_specific_details_jsonb`
  - **`guarantee_duration_end`**: Guarantee duration end date.
    - **Maps to**: `parties.additional_contact_info_jsonb` or `lease_party_roles.role_specific_details_jsonb`
  - **`conditions`**: Conditions of the guarantee.
    - **Maps to**: `parties.additional_contact_info_jsonb` or `lease_party_roles.role_specific_details_jsonb`
  - **`details_jsonb`**: Any other guarantor-specific details.
    - **Maps to**: `parties.additional_contact_info_jsonb` or `lease_party_roles.role_specific_details_jsonb`
- **`renewal_discrepancy_note`**: Note about discrepancies found between summary and detailed renewal terms.
  - **Maps to**: `lease_options.notes` or `leases.notes`

## 9. Environmental & Inducements
- **`environmental_provisions`**: Array of environmental provision objects.
  - **`provision_description`**: Description of the provision.
    - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
  - **`regulatory_references`**: Specific regulatory references (e.g., 'ASHRAE Standard 62.1-2019', 'CSA Z94.4-18').
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`responsible_party_role`**: Responsible party.
    - **Maps to**: `lease_terms_and_conditions.responsible_party_role`
  - **`notes`**: Notes about the provision.
    - **Maps to**: `lease_terms_and_conditions.notes`
- **`tenant_inducements`**: Array of tenant inducement objects.
  - **`inducement_type`**: Type of inducement (e.g., 'Rent-Free Period', 'Tenant Improvement Allowance').
    - **Maps to**: `lease_financial_terms.term_type` or `lease_financial_terms.details_jsonb`
  - **`amount`**: Amount of the inducement.
    - **Maps to**: `lease_financial_terms.fixed_amount` or `lease_financial_terms.details_jsonb`
  - **`currency`**: Currency.
    - **Maps to**: `lease_financial_terms.currency` or `lease_financial_terms.details_jsonb`
  - **`start_date`**: Start date in YYYY-MM-DD format.
    - **Maps to**: `lease_financial_terms.effective_start_date` or `lease_financial_terms.details_jsonb`
  - **`end_date`**: End date in YYYY-MM-DD format.
    - **Maps to**: `lease_financial_terms.effective_end_date` or `lease_financial_terms.details_jsonb`
  - **`conditions`**: Conditions for the inducement.
    - **Maps to**: `lease_financial_terms.details_jsonb`
  - **`details_jsonb`**: Any complex or additional inducement details.
    - **Maps to**: `lease_financial_terms.details_jsonb`

## 10. Use & Signage
- **`use_restrictions`**: Array of use restriction objects.
  - **`description_full`**: Full description of the use restriction.
    - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
  - **`exclusivity_clause_details`**: Details of exclusivity clause.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`no_continuous_operation_allowed`**: Flag for no continuous operation allowed.
    - **Maps to**: `lease_terms_and_conditions.is_prohibited` or `lease_terms_and_conditions.details_jsonb`
  - **`parking_details`**: Object with type, spots, cost_per_spot.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`signage_provisions`**: Array of signage provision objects.
  - **`signage_type_specific`**: Specific type of signage.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`approval_required`**: Whether approval is required for signage.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`cost_responsibility`**: Who is responsible for the cost of signage.
    - **Maps to**: `lease_terms_and_conditions.responsible_party_role`
  - **`abatement_for_delay_details`**: Details about abatement for delay.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`

## 11. Maintenance & Improvements
- **`maintenance_obligations`**: Array of maintenance obligation objects.
  - **`maintenance_type_specific`**: Specific type of maintenance.
    - **Maps to**: `lease_terms_and_conditions.clause_type` or `lease_terms_and_conditions.details_jsonb`
  - **`scope_details`**: Scope details of the maintenance.
    - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
  - **`notification_requirements_text`**: Notification requirements.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`admin_fee_on_landlord_costs_rate_pct`**: Admin fee on landlord costs rate percentage.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`improvement_terms`**: Array of improvement term objects.
  - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`access_inspection_rights`**: Array of access and inspection rights objects.
  - **Maps to**: `lease_terms_and_conditions.details_jsonb`

## 12. Assignment & Defaults
- **`assignment_subletting`**: Array of assignment and subletting objects.
  - **`consent_required`**: Whether consent is required.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`admin_fee_amount`**: Admin fee amount.
    - **Maps to**: `lease_terms_and_conditions.fixed_amount_value` or `lease_terms_and_conditions.details_jsonb`
  - **`landlord_can_terminate_in_lieu_of_consent`**: Whether landlord can terminate in lieu of consent.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`tenant_remains_liable`**: Whether tenant remains liable.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`liability_duration`**: Duration of liability.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`permitted_transferee_definition`**: Definition of permitted transferee.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`default_remedies`**: Array of default remedy objects.
  - **`default_trigger_events`**: Events that trigger default.
    - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
  - **`accelerated_rent_months`**: Number of months for accelerated rent.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`remedy_description_full`**: Full description of the remedy.
    - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
  - **`pre_determined_service_charge_amount`**: Pre-determined service charge amount.
    - **Maps to**: `lease_terms_and_conditions.fixed_amount_value` or `lease_terms_and_conditions.details_jsonb`
  - **`interest_rate_formula`**: Interest rate formula.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`cure_period_description`**: Description of the cure period.
    - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
  - **`cure_period_days`**: Cure period in days.
    - **Maps to**: `lease_terms_and_conditions.cure_period_days`

## 13. Insurance & Compliance
- **`insurance_liability`**: Array of insurance liability objects.
  - **`min_coverage_amount`**: Minimum coverage amount.
    - **Maps to**: `lease_terms_and_conditions.fixed_amount_value` or `lease_terms_and_conditions.details_jsonb`
  - **`named_insureds_roles`**: Roles of named insureds.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`waiver_of_subrogation_required`**: Whether waiver of subrogation is required.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`insurer_notification_days`**: Insurer notification days.
    - **Maps to**: `lease_terms_and_conditions.notice_period_days` or `lease_terms_and_conditions.details_jsonb`
  - **`self_insured_items`**: Items that are self-insured.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`tenant_pays_increase_premium`**: Whether tenant pays increase premium.
    - **Maps to**: `lease_terms_and_conditions.is_tenant_responsible_for_increase` or `lease_terms_and_conditions.details_jsonb`
- **`compliance_requirements`**: Array of compliance requirement objects.
  - **`compliance_type`**: Type of compliance.
    - **Maps to**: `lease_terms_and_conditions.clause_type` or `lease_terms_and_conditions.details_jsonb`
  - **`description`**: Description of the compliance requirement.
    - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
  - **`specific_standards_references`**: Specific standards references.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`deficiency_correction_timeline`**: Timeline for deficiency correction.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`

## 14. Legal Provisions
- **`force_majeure.description`**: Description of force majeure.
  - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
- **`destruction_expropriation`**: Object containing destruction/expropriation details.
  - **`repair_timeline_days`**: Repair timeline in days.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`rent_abatement_formula`**: Rent abatement formula.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`landlord_termination_notice_days`**: Landlord termination notice in days.
    - **Maps to**: `lease_terms_and_conditions.notice_period_days` or `lease_terms_and_conditions.details_jsonb`
  - **`expropriation_compensation_allocation`**: Expropriation compensation allocation.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`notice_provisions.notice_period_days`**: Notice period in days.
  - **Maps to**: `lease_terms_and_conditions.notice_period_days`
- **`dispute_resolution.description`**: Description of dispute resolution.
  - **Maps to**: `lease_terms_and_conditions.summary_description` or `lease_terms_and_conditions.details_jsonb`
- **`estoppel_certificate`**: Object containing estoppel certificate details.
  - **`response_time_days`**: Response time in days.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`required_information_details`**: Required information details.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`subordination_attornment`**: Object containing subordination and attornment details.
  - **`subordination_type`**: Type of subordination.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`attornment_required`**: Whether attornment is required.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`document_delivery_days`**: Document delivery days.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`power_of_attorney`**: Object containing power of attorney details.
  - **`appointed_party_role`**: Role of the appointed party.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`granting_party_role`**: Role of the granting party.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`scope_of_authority`**: Scope of authority.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`trigger_condition`**: Trigger condition.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`landlord_release`**: Object containing landlord release details.
  - **`release_event_type`**: Type of release event.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`release_details`**: Details of the release.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
- **`financial_info_provision`**: Object containing financial info provision details.
  - **`party_required_to_provide_role`**: Role of the party required to provide information.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`information_type`**: Type of information.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`
  - **`requesting_parties`**: Requesting parties.
    - **Maps to**: `lease_terms_and_conditions.details_jsonb`

## 15. Signatures & Attachments
- **`signatures`**: Array of signature objects.
  - **`party_type`**: Type of party.
    - **Maps to**: `lease_party_roles.role_name` or `parties.additional_contact_info_jsonb`
  - **`signatory_name`**: Name of the signatory.
    - **Maps to**: `parties.primary_contact_name` or `parties.additional_contact_info_jsonb`
  - **`signatory_title`**: Title of the signatory.
    - **Maps to**: `parties.primary_contact_title` or `parties.additional_contact_info_jsonb`
  - **`execution_date`**: Execution date in YYYY-MM-DD format.
    - **Maps to**: `leases.execution_date` (if main lease execution date) or `parties.additional_contact_info_jsonb`
- **`attachments`**: Array of attachment objects.
  - **`attachment_type`**: Type of attachment.
    - **Maps to**: `documents.document_type` or `documents.extracted_content_jsonb`
  - **`attachment_name`**: Name of the attachment.
    - **Maps to**: `documents.file_name` or `documents.extracted_content_jsonb`
  - **`annex_reference`**: Reference to the annex.
    - **Maps to**: `documents.extracted_content_jsonb`
  - **`file_url`**: URL to the stored attachment.
    - **Maps to**: `documents.file_url`
  - **`attachment_metadata`**: Any other attachment-specific details.
    - **Maps to**: `documents.extracted_content_jsonb`

## 16. Lease Amendments
- **`lease_amendments`**: Array of lease amendment objects.
  - **`lease_id`**: Lease ID.
    - **Maps to**: `lease_amendments.lease_id`
  - **`amendment_document_id`**: Amendment document ID.
    - **Maps to**: `lease_amendments.amendment_document_id` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`amendment_date`**: Amendment date in YYYY-MM-DD format.
    - **Maps to**: `lease_amendments.amendment_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`description`**: Description of the amendment.
    - **Maps to**: `lease_amendments.description` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`applies_to_lease_version`**: Applicable lease version.
    - **Maps to**: `lease_amendments.applies_to_lease_version` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`is_major_amendment`**: Whether it's a major amendment.
    - **Maps to**: `lease_amendments.is_major_amendment` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`details_jsonb`**: Additional details in JSONB.
    - **Maps to**: `lease_amendments.details_jsonb` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)

## 17. Tenant Payments
- **`tenant_payments`**: Array of tenant payment objects.
  - **`lease_id`**: Lease ID.
    - **Maps to**: `tenant_payments.lease_id` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`paying_party_id`**: Paying party ID.
    - **Maps to**: `tenant_payments.paying_party_id` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`payment_date`**: Payment date in YYYY-MM-DD format.
    - **Maps to**: `tenant_payments.payment_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`amount`**: Amount of the payment.
    - **Maps to**: `tenant_payments.amount` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`currency`**: Currency.
    - **Maps to**: `tenant_payments.currency` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`payment_method`**: Payment method.
    - **Maps to**: `tenant_payments.payment_method` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`reference_number`**: Reference number.
    - **Maps to**: `tenant_payments.reference_number` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`applies_to_period_start_date`**: Period start date the payment covers in YYYY-MM-DD format.
    - **Maps to**: `tenant_payments.applies_to_period_start_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`applies_to_period_end_date`**: Period end date the payment covers in YYYY-MM-DD format.
    - **Maps to**: `tenant_payments.applies_to_period_end_date` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`payment_type`**: Type of payment.
    - **Maps to**: `tenant_payments.payment_type` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`is_late`**: Whether the payment was late.
    - **Maps to**: `tenant_payments.is_late` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)
  - **`notes`**: Notes about the payment.
    - **Maps to**: `tenant_payments.notes` (Note: `optimized_database_structure_for_n8n.md` states this is "Not extracted by any information extractor".)

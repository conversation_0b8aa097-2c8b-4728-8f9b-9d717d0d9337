// Test script to debug chat session creation
// Run this in browser console when logged in

console.log('=== DEBUGGING CHAT SESSION CREATION ===');

// Check authentication
console.log('Auth state:', {
  authUser: window.localStorage.getItem('sb-yvoonviweliyhcbefvhc-auth-token'),
  hasSession: !!window.supabase?.auth?.getSession
});

// Test direct database access
async function testDatabaseAccess() {
  try {
    // Test connection
    const { data: testData, error: testError } = await window.supabase
      .from('chat_sessions')
      .select('id')
      .limit(1);
    
    console.log('Database connection test:', { testData, testError });
    
    // Test insert with minimal data
    const { data: session, error: sessionError } = await window.supabase
      .from('chat_sessions')
      .insert({
        lease_id: 'test-lease-id',
        user_id: window.supabase.auth.getUser().then(u => u.data.user?.id),
        title: 'Test Session',
        metadata: {}
      })
      .select()
      .single();
      
    console.log('Session creation test:', { session, sessionError });
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testDatabaseAccess();

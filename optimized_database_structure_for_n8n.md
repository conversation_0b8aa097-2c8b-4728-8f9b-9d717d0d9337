# Optimized Database Structure for Brasswater Lease, Property, and Chat Management (for N8N Workflow)

## Overview
This optimized database structure is designed to efficiently store data extracted by the `full_n8n_workflow_perfect.json` workflow, and to comprehensively manage all property-related information and chat functionalities. It balances consolidation with the need for distinct entities, reducing complexity while ensuring all critical data points are well-structured and accessible.

The n8n workflow extracts lease-specific data into distinct JSON objects for each of the 15 categories. This database structure provides dedicated columns for core, structured data and `jsonb` columns to store the more flexible, detailed, or multi-valued data directly from the n8n extractors.

## Core Document Tables (2 tables)

### 1. documents (Document Storage)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **file_name** (text, NOT NULL) - *Corresponds to `document_filename` extracted by "1. Document & Lease ID".*
-   **file_url** (text, NOT NULL) - *Not extracted by "1. Document & Lease ID", but extracted by "15. Signatures & Attachments" for attachments. This field would need to be populated by the document upload process.*
-   **document_type** (varchar, NOT NULL) - *Extracted by "1. Document & Lease ID".*
-   **document_language** (varchar, NOT NULL) - *Extracted by "1. Document & Lease ID".*
-   **document_date** (date) - *Extracted by "1. Document & Lease ID".*
-   **complete_content_text** (text) - *Corresponds to `complete_content` in the workflow, which is the input for extractors.*
-   **extracted_content_jsonb** (jsonb) - *Corresponds to `metadata` extracted by "1. Document & Lease ID" and can store other extracted JSON content.*
-   **uploaded_by_user_id** (uuid) - *Not extracted by any information extractor. Foreign key to `users` table.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 2. document_sections (Document Sections)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **document_id** (uuid, FOREIGN KEY → documents.id, ON DELETE CASCADE, NOT NULL) - *Foreign key, not directly extracted by an information extractor.*
-   **section_title** (text) - *Not extracted by any information extractor.*
-   **content** (text) - *This is a section of the document, not an extracted field.*
-   **doc_order** (integer) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

## Lease Core Tables (7 tables)

### 3. leases (Main Lease Information) - *Renamed from `lease_documents` to match actual DB*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database. Corresponds to `lease_id` in the old markdown.*
-   **document_id** (uuid, FOREIGN KEY → documents.id) - *From `Create row in lease_documents table` (from Get Document Content node).*
-   **lease_name** (varchar, NOT NULL) - *Extracted by "1. Document & Lease ID".*
-   **lease_type** (varchar, NOT NULL) - *Extracted by "1. Document & Lease ID".*
-   **current_status** (varchar, NOT NULL) - *Corresponds to `lease_status` extracted by "1. Document & Lease ID".*
-   **execution_date** (date) - *Consolidated from `landlord_execution_date` and `tenant_execution_date` in "2. Dates & Terms".*
-   **commencement_date** (date) - *Extracted by "2. Dates & Terms".*
-   **term_start_date** (date) - *Not extracted by any information extractor. Could be same as `commencement_date`.*
-   **term_end_date** (date) - *Extracted by "2. Dates & Terms" (named `end_date`).*
-   **initial_term_months** (integer) - *Extracted by "2. Dates & Terms" (named `lease_term_months`).*
-   **initial_term_years** (integer) - *Extracted by "2. Dates & Terms" (named `lease_term_years`).*
-   **possession_date** (date) - *Extracted by "2. Dates & Terms".*
-   **fixturing_period_start_date** (date) - *Extracted by "2. Dates & Terms" (named `installation_period_start`).*
-   **fixturing_period_end_date** (date) - *Extracted by "2. Dates & Terms" (named `installation_period_end`).*
-   **fixturing_period_months** (integer) - *Extracted by "2. Dates & Terms" (named `installation_period_months`).*
-   **version_number** (integer, DEFAULT 1) - *Extracted by "1. Document & Lease ID".*
-   **original_lease_id** (uuid) - *Not extracted by any information extractor. For amendments/renewals.*
-   **lease_language** (varchar, NOT NULL) - *Corresponds to `document_language` extracted by "1. Document & Lease ID".*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **lease_number** (text) - *Extracted by "1. Document & Lease ID".*
-   **pwgsc_region_file** (text) - *Extracted by "1. Document & Lease ID".*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 4. parties (All Parties: Landlord, Tenant, Guarantor, Broker)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database. Corresponds to `party_id` in the old markdown.*
-   **legal_name** (varchar, NOT NULL) - *Corresponds to `party_name` extracted by "3. Complete Party Info".*
-   **entity_type** (varchar, NOT NULL) - *Corresponds to `legal_status` extracted by "3. Complete Party Info".*
-   **primary_contact_name** (varchar) - *Corresponds to `representative_name` extracted by "3. Complete Party Info".*
-   **primary_contact_title** (varchar) - *Corresponds to `representative_title` extracted by "3. Complete Party Info".*
-   **primary_email** (varchar) - *Extracted by "3. Complete Party Info".*
-   **primary_phone_number** (varchar) - *Extracted by "3. Complete Party Info".*
-   **primary_address_street** (varchar) - *Corresponds to `address_street` extracted by "3. Complete Party Info".*
-   **primary_address_city** (varchar) - *Corresponds to `address_city` extracted by "3. Complete Party Info".*
-   **primary_address_province** (varchar) - *Corresponds to `address_province` extracted by "3. Complete Party Info".*
-   **primary_address_postal_code** (varchar) - *Corresponds to `address_postal_code` extracted by "3. Complete Party Info".*
-   **additional_contact_info_jsonb** (jsonb) - *Corresponds to `additional_addresses` and `party_details` in the old markdown. Can store `notice_address` and other party-specific details from "3. Complete Party Info".*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 5. properties (Property Details)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database. Corresponds to `property_id` in the old markdown.*
-   **name** (varchar, NOT NULL) - *Not extracted by any information extractor. This would likely be derived or manually entered.*
-   **address_street** (varchar) - *Corresponds to `leased_premises_address` extracted by "4. Property & Building".*
-   **address_city** (varchar) - *Not extracted by any information extractor.*
-   **address_province** (varchar) - *Not extracted by any information extractor.*
-   **address_postal_code** (varchar) - *Not extracted by any information extractor.*
-   **address_country** (varchar, DEFAULT 'Canada') - *Not extracted by any information extractor.*
-   **property_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **current_status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **total_land_area_sqft** (numeric) - *Not extracted by any information extractor.*
-   **total_built_area_sqft** (numeric) - *Not extracted by any information extractor.*
-   **year_built** (integer) - *Not extracted by any information extractor.*
-   **number_of_floors** (integer) - *Not extracted by any information extractor.*
-   **total_parking_spaces** (integer) - *Corresponds to `total_parking_spaces` extracted by "4. Property & Building".*
-   **property_manager_party_id** (uuid) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **description** (text) - *Not extracted by any information extractor.*
-   **zoning_classification** (varchar) - *Not extracted by any information extractor.*
-   **legal_description** (text) - *Corresponds to `cadastre_details` and `land_lot_number` extracted by "4. Property & Building".*
-   **amenities_jsonb** (jsonb) - *Corresponds to `property_features` extracted by "4. Property & Building". Can also store details from `Leased Amenities (Multiples)`.*
-   **media_urls_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **external_reference_id** (varchar) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 6. rent_schedules (Rent Schedules) - *Corresponds to `lease_escalations` in the old markdown*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `leases` table.*
-   **schedule_type** (varchar, NOT NULL) - *Corresponds to `escalation_type` extracted by "7. Escalations & Operating".*
-   **effective_start_date** (date, NOT NULL) - *Corresponds to `start_date` extracted by "7. Escalations & Operating".*
-   **effective_end_date** (date) - *Corresponds to `end_date` extracted by "7. Escalations & Operating".*
-   **annual_rent_per_sqft** (numeric) - *Corresponds to `rate_per_sqft_for_period` extracted by "7. Escalations & Operating".*
-   **annual_total_amount** (numeric, NOT NULL) - *Corresponds to `annual_base_rent_for_period` extracted by "7. Escalations & Operating".*
-   **monthly_installment** (numeric, NOT NULL) - *Corresponds to `monthly_base_rent_for_period` extracted by "7. Escalations & Operating".*
-   **currency** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **escalation_details_jsonb** (jsonb) - *Corresponds to `escalation_details` extracted by "7. Escalations & Operating".*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 7. security_deposits (Security Deposit Details)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database. Corresponds to `deposit_id` in the old markdown.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `leases` table.*
-   **deposit_type** (varchar, NOT NULL) - *Not extracted by any information extractor. Could be derived from `security_deposit_base`.*
-   **amount** (numeric, NOT NULL) - *Corresponds to `security_deposit_total` extracted by "6. Security & Deposits".*
-   **currency** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **is_refundable** (boolean) - *Not extracted by any information extractor.*
-   **refund_conditions** (text) - *Corresponds to `security_deposit_conditions` extracted by "6. Security & Deposits".*
-   **interest_accrual_terms** (text) - *Corresponds to `deposit_interest_terms` extracted by "6. Security & Deposits".*
-   **payment_method_details_jsonb** (jsonb) - *Corresponds to `deposit_details` extracted by "6. Security & Deposits". Can store `deposit_payment_method`.*
-   **notes** (text) - *Corresponds to `deposit_absent_note` extracted by "6. Security & Deposits".*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 8. lease_financial_terms (Lease Financial Terms) - *Corresponds to `financial_terms` in the old markdown*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database. Corresponds to `financial_id` in the old markdown.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `leases` table.*
-   **term_type** (varchar, NOT NULL) - *Not extracted by any information extractor. Could be derived from the financial terms.*
-   **effective_start_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **effective_end_date** (date) - *Not extracted by any information extractor.*
-   **responsible_party_role** (varchar) - *Not extracted by any information extractor.*
-   **annual_rate_pct** (numeric) - *Not extracted by any information extractor.*
-   **fixed_amount** (numeric) - *Corresponds to `annual_base_rent` or `monthly_base_rent` extracted by "5. Detailed Financial".*
-   **is_tenant_responsible_for_increase** (boolean) - *Not extracted by any information extractor.*
-   **calculation_method** (varchar) - *Not extracted by any information extractor.*
-   **currency** (varchar) - *Not extracted by any information extractor.*
-   **details_jsonb** (jsonb) - *Corresponds to `rent_breakdown`, `additional_fees`, `payment_terms` extracted by "5. Detailed Financial".*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 9. lease_terms_and_conditions (Lease Terms & Conditions) - *Corresponds to `lease_terms` in the old markdown*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `leases` table.*
-   **clause_type** (varchar, NOT NULL) - *Corresponds to `term_type` in the old markdown. e.g., 'maintenance', 'improvement', 'use_restriction', 'access_inspection', 'compliance', 'signage', 'assignment_subletting', 'default_remedies', 'expiration_holdover', 'notice'.*
-   **effective_start_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **effective_end_date** (date) - *Not extracted by any information extractor.*
-   **summary_description** (text) - *Corresponds to `description` extracted by "11. Maintenance & Improvements", "13. Insurance & Compliance", "12. Assignment & Defaults".*
-   **responsible_party_role** (varchar) - *Corresponds to `responsible_party` extracted by "11. Maintenance & Improvements", "10. Use & Signage".*
-   **notice_period_days** (integer) - *Corresponds to `notice_period_days` extracted by "8. Rights & Options" (for early termination/renewal/purchase options) and "14. Legal Provisions" (for notice provisions).*
-   **is_transferrable** (boolean) - *Not extracted by any information extractor.*
-   **cure_period_days** (integer) - *Corresponds to `cure_period_days` extracted by "12. Assignment & Defaults".*
-   **is_recurring** (boolean) - *Not extracted by any information extractor.*
-   **is_prohibited** (boolean) - *Not extracted by any information extractor.*
-   **fixed_amount_value** (numeric) - *Not extracted by any information extractor.*
-   **details_jsonb** (jsonb) - *Corresponds to `term_details` in the old markdown. Stores all specific fields for each term type from "11. Maintenance & Improvements", "10. Use & Signage", "13. Insurance & Compliance", "12. Assignment & Defaults", "8. Rights & Options" (for holdover termination), and "14. Legal Provisions" (for notice provisions).*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 10. lease_options (Consolidated Options)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `leases` table.*
-   **option_type** (varchar, NOT NULL) - *e.g., 'renewal', 'purchase', 'early_termination'. Extracted by "8. Rights & Options".*
-   **option_number** (integer) - *Extracted by "8. Rights & Options" (from `renewal_options` array).*
-   **effective_start_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **effective_end_date** (date) - *Not extracted by any information extractor.*
-   **notice_period_days** (integer) - *Extracted by "8. Rights & Options".*
-   **exercise_conditions_text** (text) - *Corresponds to `exercise_conditions` extracted by "8. Rights & Options".*
-   **option_term_months** (integer) - *Not extracted by any information extractor. Could be derived from `renewal_term_duration`.*
-   **rent_calculation_method** (varchar) - *Extracted by "8. Rights & Options" (from `renewal_options` array).*
-   **penalty_amount** (numeric) - *Corresponds to `penalty_amount_or_formula` extracted by "8. Rights & Options" (for early termination). This would require parsing if `penalty_amount_or_formula` is text.*
-   **purchase_price_formula** (text) - *Extracted by "8. Rights & Options" (from `purchase_options` array).*
-   **details_jsonb** (jsonb) - *Corresponds to `option_details` in the old markdown. Stores all specific fields for each option type from "8. Rights & Options".*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 11. lease_amendments (Lease Amendments) - *Corresponds to `lease_events` in the old markdown for amendments*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `leases` table.*
-   **amendment_document_id** (uuid) - *Not extracted by any information extractor. Foreign key to `documents` table.*
-   **amendment_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **description** (text) - *Not extracted by any information extractor.*
-   **applies_to_lease_version** (integer) - *Not extracted by any information extractor.*
-   **is_major_amendment** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **details_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 12. lease_party_roles (Lease Party Roles) - *Linking table for parties and leases*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, NOT NULL) - *Foreign key to `leases` table.*
-   **party_id** (uuid, FOREIGN KEY → parties.id, NOT NULL) - *Foreign key to `parties` table.*
-   **role_name** (varchar, NOT NULL) - *Corresponds to `party_type` in the old markdown (e.g., 'landlord', 'tenant', 'guarantor', 'broker').*
-   **is_primary_role** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **role_specific_details_jsonb** (jsonb) - *Can store `party_role` and other party-specific details from "3. Complete Party Info".*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 13. lease_unit_allocations (Lease Unit Allocations) - *Linking table for leases and property units*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, NOT NULL) - *Foreign key to `leases` table.*
-   **unit_id** (uuid, FOREIGN KEY → property_units.id, NOT NULL) - *Foreign key to `property_units` table.*
-   **effective_start_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **effective_end_date** (date) - *Not extracted by any information extractor.*
-   **allocated_area_sqft** (numeric) - *Not extracted by any information extractor.*
-   **proportionate_share_pct** (numeric) - *Not extracted by any information extractor.*
-   **specific_use_details** (text) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

## Property Management Tables (6 tables)

### 14. property_units (Unit Details)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **property_id** (uuid, FOREIGN KEY → properties.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `properties` table.*
-   **unit_number** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **unit_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **floor_number** (integer) - *Not extracted by any information extractor.*
-   **rentable_area_sqft** (numeric, NOT NULL) - *Not extracted by any information extractor.*
-   **rentable_area_sqm** (numeric) - *Not extracted by any information extractor.*
-   **current_status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **is_leasable** (boolean, DEFAULT true) - *Not extracted by any information extractor.*
-   **move_in_ready** (boolean, DEFAULT true) - *Not extracted by any information extractor.*
-   **last_renovation_date** (date) - *Not extracted by any information extractor.*
-   **description** (text) - *Not extracted by any information extractor.*
-   **features_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 15. property_expenses (Property Expenses)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **property_id** (uuid, FOREIGN KEY → properties.id, NOT NULL) - *Foreign key to `properties` table.*
-   **expense_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **description** (text) - *Not extracted by any information extractor.*
-   **amount** (numeric, NOT NULL) - *Not extracted by any information extractor.*
-   **currency** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **expense_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **vendor_party_id** (uuid) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **invoice_document_id** (uuid) - *Not extracted by any information extractor. Foreign key to `documents` table.*
-   **payment_status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **payment_date** (date) - *Not extracted by any information extractor.*
-   **is_recurring** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **recurring_frequency** (varchar) - *Not extracted by any information extractor.*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **created_by_user_id** (uuid, NOT NULL) - *Not extracted by any information extractor. Foreign key to `users` table.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 16. work_orders (Work Orders)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **property_id** (uuid, FOREIGN KEY → properties.id, NOT NULL) - *Foreign key to `properties` table.*
-   **unit_id** (uuid, FOREIGN KEY → property_units.id) - *Foreign key to `property_units` table.*
-   **work_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **description** (text, NOT NULL) - *Not extracted by any information extractor.*
-   **requested_by_party_id** (uuid) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **assigned_to_party_id** (uuid) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **priority** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **requested_date** (date, DEFAULT CURRENT_DATE) - *Not extracted by any information extractor.*
-   **scheduled_date** (date) - *Not extracted by any information extractor.*
-   **due_date** (date) - *Not extracted by any information extractor.*
-   **completion_date** (date) - *Not extracted by any information extractor.*
-   **estimated_cost** (numeric) - *Not extracted by any information extractor.*
-   **actual_cost** (numeric) - *Not extracted by any information extractor.*
-   **invoice_document_id** (uuid) - *Not extracted by any information extractor. Foreign key to `documents` table.*
-   **is_recurring** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **recurring_schedule_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **linked_lease_clause_id** (uuid) - *Not extracted by any information extractor. Foreign key to `lease_terms_and_conditions` table.*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 17. work_order_updates (Work Order Updates)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **work_order_id** (uuid, FOREIGN KEY → work_orders.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `work_orders` table.*
-   **update_date** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **status_update** (varchar) - *Not extracted by any information extractor.*
-   **comment** (text, NOT NULL) - *Not extracted by any information extractor.*
-   **created_by_user_id** (uuid, NOT NULL) - *Not extracted by any information extractor. Foreign key to `users` table.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 18. property_inspections (Property Inspections)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **property_id** (uuid, FOREIGN KEY → properties.id, ON DELETE CASCADE, NOT NULL) - *Foreign key to `properties` table.*
-   **unit_id** (uuid, FOREIGN KEY → property_units.id) - *Foreign key to `property_units` table.*
-   **inspection_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **inspection_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **inspector_party_id** (uuid) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **overall_condition_notes** (text) - *Not extracted by any information extractor.*
-   **issues_found_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **recommendations** (text) - *Not extracted by any information extractor.*
-   **follow_up_required** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **follow_up_date** (date) - *Not extracted by any information extractor.*
-   **report_document_id** (uuid) - *Not extracted by any information extractor. Foreign key to `documents` table.*
-   **photos_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **tenant_present** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **signatures_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **linked_acquisition_pipeline_id** (uuid) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 19. property_acquisition_pipeline (Property Acquisition Pipeline)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **potential_property_name** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **address_street** (varchar) - *Not extracted by any information extractor.*
-   **address_city** (varchar) - *Not extracted by any information extractor.*
-   **address_province** (varchar) - *Not extracted by any information extractor.*
-   **address_postal_code** (varchar) - *Not extracted by any information extractor.*
-   **status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **initial_contact_date** (date) - *Not extracted by any information extractor.*
-   **target_acquisition_date** (date) - *Not extracted by any information extractor.*
-   **estimated_value** (numeric) - *Not extracted by any information extractor.*
-   **acquisition_notes** (text) - *Not extracted by any information extractor.*
-   **due_diligence_details_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **acquired_property_id** (uuid) - *Not extracted by any information extractor. Foreign key to `properties` table.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

## Tenant Management Tables (3 tables)

### 20. tenants (Tenant Management)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **party_id** (uuid, FOREIGN KEY → parties.id, NOT NULL, UNIQUE) - *Foreign key to `parties` table.*
-   **tenant_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **current_lease_id** (uuid, UNIQUE) - *Not extracted by any information extractor. Foreign key to `leases` table.*
-   **credit_score** (integer) - *Not extracted by any information extractor.*
-   **annual_income** (numeric) - *Not extracted by any information extractor.*
-   **employment_status** (varchar) - *Not extracted by any information extractor.*
-   **employer_name** (varchar) - *Not extracted by any information extractor.*
-   **move_in_date** (date) - *Not extracted by any information extractor.*
-   **move_out_date** (date) - *Not extracted by any information extractor.*
-   **emergency_contact_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 21. rental_applications (Rental Applications)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **applicant_party_id** (uuid, FOREIGN KEY → parties.id, NOT NULL) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **property_id** (uuid, FOREIGN KEY → properties.id, ON DELETE CASCADE, NOT NULL) - *Not extracted by any information extractor. Foreign key to `properties` table.*
-   **unit_id** (uuid, FOREIGN KEY → property_units.id) - *Not extracted by any information extractor. Foreign key to `property_units` table.*
-   **application_date** (date, DEFAULT CURRENT_DATE) - *Generated by database.*
-   **desired_move_in_date** (date) - *Not extracted by any information extractor.*
-   **application_status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **screening_status_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **application_fee_paid** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **application_fee_amount** (numeric) - *Not extracted by any information extractor.*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **decision_date** (date) - *Not extracted by any information extractor.*
-   **decision_reason** (text) - *Not extracted by any information extractor.*
-   **analysis_data_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 22. tenant_communications (Tenant Communications)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **communication_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **subject** (varchar) - *Not extracted by any information extractor.*
-   **content** (text, NOT NULL) - *Not extracted by any information extractor.*
-   **sent_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **priority** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **follow_up_required** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **follow_up_date** (date) - *Not extracted by any information extractor.*
-   **attachments_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_by_user_id** (uuid, NOT NULL) - *Not extracted by any information extractor. Foreign key to `users` table.*
-   **linked_entities_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 23. tenant_payments (Tenant Payments)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **lease_id** (uuid, FOREIGN KEY → leases.id, NOT NULL) - *Not extracted by any information extractor. Foreign key to `leases` table.*
-   **paying_party_id** (uuid, FOREIGN KEY → parties.id, NOT NULL) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **payment_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **amount** (numeric, NOT NULL) - *Not extracted by any information extractor.*
-   **currency** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **payment_method** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **reference_number** (varchar) - *Not extracted by any information extractor.*
-   **applies_to_period_start_date** (date) - *Not extracted by any information extractor.*
-   **applies_to_period_end_date** (date) - *Not extracted by any information extractor.*
-   **payment_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **is_late** (boolean, DEFAULT false) - *Not extracted by any information extractor.*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

## Linking Tables (1 table)

### 24. lease_property_tenant_link (Links Leases, Properties, and Tenants) - *This table does not exist in the actual DB, but its functionality is covered by direct foreign keys in `leases` and `tenants`.*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid()) - *Not applicable, as this table does not exist in the actual DB.*
-   **lease_id** (uuid, REFERENCES lease_documents(lease_id)) - *Not applicable.*
-   **property_id** (uuid, REFERENCES property_portfolio(id)) - *Not applicable.*
-   **unit_id** (uuid, REFERENCES property_units(id)) - *Not applicable.*
-   **tenant_id** (uuid, REFERENCES tenants(id)) - *Not applicable.*
-   **status** (varchar(50), DEFAULT 'active') - *Not applicable.*
-   **start_date** (date) - *Not applicable.*
-   **end_date** (date) - *Not applicable.*
-   **created_at** (timestamptz, DEFAULT now()) - *Not applicable.*

## Financial Entities (1 table)

### 25. property_valuations (Property Valuations) - *Renamed from `lenders` in old markdown, but matches actual DB table name*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **property_id** (uuid, FOREIGN KEY → properties.id, ON DELETE CASCADE, NOT NULL) - *Not extracted by any information extractor. Foreign key to `properties` table.*
-   **valuation_date** (date, NOT NULL) - *Not extracted by any information extractor.*
-   **valuation_amount** (numeric, NOT NULL) - *Not extracted by any information extractor.*
-   **valuation_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **appraiser_name** (varchar) - *Not extracted by any information extractor.*
-   **appraiser_company** (varchar) - *Not extracted by any information extractor.*
-   **appraiser_license** (varchar) - *Not extracted by any information extractor.*
-   **valuation_method** (varchar) - *Not extracted by any information extractor.*
-   **square_foot_value** (numeric) - *Not extracted by any information extractor.*
-   **valuation_notes** (text) - *Not extracted by any information extractor.*
-   **report_url** (varchar) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 26. property_managers (Property Managers)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **company_name** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **contact_person** (varchar) - *Not extracted by any information extractor.*
-   **email** (varchar) - *Not extracted by any information extractor.*
-   **phone_primary** (varchar) - *Not extracted by any information extractor.*
-   **address_street** (varchar) - *Not extracted by any information extractor.*
-   **address_city** (varchar) - *Not extracted by any information extractor.*
-   **address_province** (varchar) - *Not extracted by any information extractor.*
-   **address_postal_code** (varchar) - *Not extracted by any information extractor.*
-   **notes** (text) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 27. property_acquisition_pipeline (Property Acquisition Pipeline) - *This table exists in the actual DB, but was not in the old markdown's "Financial Entities" section.*
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **potential_property_name** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **address_street** (varchar) - *Not extracted by any information extractor.*
-   **address_city** (varchar) - *Not extracted by any information extractor.*
-   **address_province** (varchar) - *Not extracted by any information extractor.*
-   **address_postal_code** (varchar) - *Not extracted by any information extractor.*
-   **status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **initial_contact_date** (date) - *Not extracted by any information extractor.*
-   **target_acquisition_date** (date) - *Not extracted by any information extractor.*
-   **estimated_value** (numeric) - *Not extracted by any information extractor.*
-   **acquisition_notes** (text) - *Not extracted by any information extractor.*
-   **due_diligence_details_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **acquired_property_id** (uuid) - *Not extracted by any information extractor. Foreign key to `properties` table.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

## Chat Functionality Tables (2 tables)

### 28. chat_sessions (Chat Sessions)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **user_id** (uuid, NOT NULL) - *Not extracted by any information extractor. Foreign key to `users` table.*
-   **related_party_id** (uuid) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **session_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **status** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **last_message_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **context_data_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 29. messages (Messages)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **session_id** (uuid, FOREIGN KEY → chat_sessions.id, ON DELETE CASCADE, NOT NULL) - *Not extracted by any information extractor. Foreign key to `chat_sessions` table.*
-   **sender_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **sender_user_id** (uuid) - *Not extracted by any information extractor. Foreign key to `users` table.*
-   **sender_party_id** (uuid) - *Not extracted by any information extractor. Foreign key to `parties` table.*
-   **content** (text, NOT NULL) - *Not extracted by any information extractor.*
-   **sent_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **message_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **metadata_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*

## Other Tables from Actual DB (2 tables)

### 30. users (Users)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **username** (varchar, NOT NULL, UNIQUE) - *Not extracted by any information extractor.*
-   **email** (varchar, NOT NULL, UNIQUE) - *Not extracted by any information extractor.*
-   **first_name** (varchar) - *Not extracted by any information extractor.*
-   **last_name** (varchar) - *Not extracted by any information extractor.*
-   **role** (varchar) - *Not extracted by any information extractor.*
-   **is_active** (boolean, DEFAULT true) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **updated_at** (timestamptz, DEFAULT now()) - *Generated by database.*

### 31. audit_logs (Audit Logs)
-   **id** (uuid, PRIMARY KEY, DEFAULT gen_random_uuid(), NOT NULL) - *Generated by database.*
-   **timestamp** (timestamptz, DEFAULT now()) - *Generated by database.*
-   **event_type** (varchar, NOT NULL) - *Not extracted by any information extractor.*
-   **user_id** (uuid) - *Not extracted by any information extractor. Foreign key to `users` table.*
-   **related_entity_type** (varchar) - *Not extracted by any information extractor.*
-   **related_entity_id** (uuid) - *Not extracted by any information extractor.*
-   **description** (text, NOT NULL) - *Not extracted by any information extractor.*
-   **old_value_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **new_value_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **details_jsonb** (jsonb) - *Not extracted by any information extractor.*
-   **created_at** (timestamptz, DEFAULT now()) - *Generated by database.*
